# 后端开发指南

## 缺失API端点

### 1. 健康检查端点
```javascript
// GET /api/v1/health
router.get('/health', async (req, res) => {
  const checks = await Promise.allSettled([
    db.query('SELECT 1'),
    redis.ping(),
    checkDiskSpace(),
    checkMemory()
  ]);
  
  const isHealthy = checks.every(check => check.status === 'fulfilled');
  res.status(isHealthy ? 200 : 503).json({
    success: isHealthy,
    data: {
      status: isHealthy ? 'healthy' : 'unhealthy',
      version: process.env.API_VERSION,
      timestamp: new Date().toISOString(),
      services: {
        database: checks[0].status === 'fulfilled' ? 'healthy' : 'unhealthy',
        redis: checks[1].status === 'fulfilled' ? 'healthy' : 'unhealthy'
      }
    }
  });
});
```

### 2. 健康评估API
```javascript
// POST /api/health/assessment
const calculateBMI = (height, weight) => Math.round((weight / ((height/100) ** 2)) * 100) / 100;

const calculateRecoveryStage = (painLevel, mobilityLevel, bmi) => {
  const score = (10 - painLevel) * 0.4 + mobilityLevel * 0.4 + (bmi >= 18.5 && bmi <= 24 ? 10 : 5) * 0.2;
  if (score >= 8) return 'advanced';
  if (score >= 5) return 'intermediate';
  return 'beginner';
};

router.post('/health/assessment', auth, async (req, res) => {
  const { height, weight, painLevel, mobilityLevel } = req.body;
  
  const bmi = calculateBMI(height, weight);
  const recoveryStage = calculateRecoveryStage(painLevel, mobilityLevel, bmi);
  const riskScore = Math.min((painLevel/10) * 0.4 + ((10-mobilityLevel)/10) * 0.4, 1);
  
  const assessment = await HealthAssessment.create({
    userId: req.user.id,
    height, weight, painLevel, mobilityLevel,
    bmi, recoveryStage, riskScore
  });
  
  res.json({ success: true, data: assessment });
});
```

### 3. 游戏数据API
```javascript
// POST /api/games/records
router.post('/games/records', auth, async (req, res) => {
  const { gameType, score, accuracy, duration, metadata } = req.body;
  
  const record = await GameRecord.create({
    userId: req.user.id,
    gameType, score, accuracy, duration, metadata
  });
  
  // 更新统计
  await GameStatistics.upsert({
    userId: req.user.id,
    gameType,
    totalSessions: sequelize.literal('total_sessions + 1'),
    bestScore: sequelize.fn('GREATEST', sequelize.col('best_score'), score),
    lastPlayed: new Date()
  });
  
  res.json({ success: true, data: record });
});
```

## 数据库表结构

### 核心表
```sql
-- 健康评估表
CREATE TABLE health_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    height DECIMAL(5,2) NOT NULL CHECK (height >= 100 AND height <= 250),
    weight DECIMAL(5,2) NOT NULL CHECK (weight >= 30 AND weight <= 200),
    pain_level INTEGER CHECK (pain_level >= 0 AND pain_level <= 10),
    mobility_level INTEGER CHECK (mobility_level >= 0 AND mobility_level <= 10),
    bmi DECIMAL(4,2),
    recovery_stage VARCHAR(50),
    risk_score DECIMAL(3,2),
    assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 游戏记录表
CREATE TABLE game_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    game_type VARCHAR(50) NOT NULL,
    score INTEGER NOT NULL DEFAULT 0,
    accuracy DECIMAL(5,4),
    duration INTEGER NOT NULL,
    metadata JSONB,
    session_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 游戏统计表
CREATE TABLE game_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    game_type VARCHAR(50) NOT NULL,
    total_sessions INTEGER DEFAULT 0,
    best_score INTEGER DEFAULT 0,
    average_score DECIMAL(8,2) DEFAULT 0,
    total_playtime INTEGER DEFAULT 0,
    last_played TIMESTAMP,
    UNIQUE(user_id, game_type)
);

-- 训练会话表
CREATE TABLE training_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    duration INTEGER NOT NULL,
    completed_actions INTEGER DEFAULT 0,
    accuracy DECIMAL(5,4),
    session_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 索引
```sql
CREATE INDEX idx_assessments_user_date ON health_assessments(user_id, assessment_date DESC);
CREATE INDEX idx_game_records_user_type ON game_records(user_id, game_type, session_date DESC);
CREATE INDEX idx_game_stats_user_type ON game_statistics(user_id, game_type);
```

## API规范

### 统一响应格式
```javascript
// 成功响应
{
  "success": true,
  "data": { /* 数据 */ },
  "message": "操作成功",
  "timestamp": "2025-07-24T10:30:00Z"
}

// 错误响应
{
  "success": false,
  "data": null,
  "message": "操作失败",
  "timestamp": "2025-07-24T10:30:00Z",
  "error": {
    "code": "ERROR_CODE",
    "message": "详细错误信息"
  }
}
```

### 错误处理中间件
```javascript
const errorHandler = (err, req, res, next) => {
  let statusCode = 500;
  let errorCode = 'INTERNAL_ERROR';
  
  if (err.name === 'ValidationError') {
    statusCode = 422;
    errorCode = 'VALIDATION_ERROR';
  } else if (err.name === 'UnauthorizedError') {
    statusCode = 401;
    errorCode = 'UNAUTHORIZED';
  }

  res.status(statusCode).json({
    success: false,
    data: null,
    message: err.message,
    timestamp: new Date().toISOString(),
    error: { code: errorCode, message: err.message }
  });
};
```

### JWT认证中间件
```javascript
const auth = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token) {
    return res.status(401).json({
      success: false,
      error: { code: 'MISSING_TOKEN', message: '缺少认证令牌' }
    });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        error: { code: 'INVALID_TOKEN', message: '无效令牌' }
      });
    }
    req.user = user;
    next();
  });
};
```

## 业务逻辑

### 健康评估算法
```javascript
class HealthAssessmentService {
  calculateBMI(height, weight) {
    return Math.round((weight / ((height/100) ** 2)) * 100) / 100;
  }

  getBMICategory(bmi) {
    if (bmi < 18.5) return '偏瘦';
    if (bmi < 24.0) return '正常';
    if (bmi < 28.0) return '超重';
    return '肥胖';
  }

  calculateRecoveryStage(painLevel, mobilityLevel, bmi) {
    let score = (10 - painLevel) * 0.4 + mobilityLevel * 0.4;
    const bmiScore = (bmi >= 18.5 && bmi <= 24) ? 10 : 5;
    score += bmiScore * 0.2;
    
    if (score >= 8) return 'advanced';
    if (score >= 5) return 'intermediate';
    return 'beginner';
  }

  generateRecommendations(bmi, painLevel, mobilityLevel) {
    const recommendations = [];
    
    // BMI建议
    const category = this.getBMICategory(bmi);
    if (category === '偏瘦') recommendations.push('建议增加营养摄入');
    else if (category === '超重') recommendations.push('建议控制饮食');
    else recommendations.push('保持当前体重');
    
    // 疼痛建议
    if (painLevel > 7) recommendations.push('疼痛严重，建议咨询医生');
    else if (painLevel > 4) recommendations.push('注意休息，避免过度训练');
    else recommendations.push('疼痛控制良好，可继续训练');
    
    return recommendations;
  }
}
```

### 游戏统计更新
```javascript
const updateGameStatistics = async (userId, gameType, newRecord) => {
  const stats = await GameStatistics.findOne({ 
    where: { userId, gameType } 
  });

  if (stats) {
    await stats.update({
      totalSessions: stats.totalSessions + 1,
      bestScore: Math.max(stats.bestScore, newRecord.score),
      totalPlaytime: stats.totalPlaytime + newRecord.duration,
      lastPlayed: new Date()
    });
  } else {
    await GameStatistics.create({
      userId, gameType,
      totalSessions: 1,
      bestScore: newRecord.score,
      totalPlaytime: newRecord.duration,
      lastPlayed: new Date()
    });
  }
};
```

## 测试用例

### 健康检查测试
```javascript
describe('Health Check', () => {
  it('should return 200 when healthy', async () => {
    const res = await request(app).get('/api/v1/health');
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data.status).toBe('healthy');
  });
});
```

### 健康评估测试
```javascript
describe('Health Assessment', () => {
  it('should create assessment', async () => {
    const data = { height: 175, weight: 70, painLevel: 3, mobilityLevel: 7 };
    const res = await request(app)
      .post('/api/health/assessment')
      .set('Authorization', `Bearer ${token}`)
      .send(data);
    
    expect(res.status).toBe(201);
    expect(res.body.data.bmi).toBe(22.86);
  });
});
```

## 部署配置

### 环境变量
```bash
DATABASE_URL=postgresql://user:pass@localhost:5432/shoutaoapp
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-secret-key
API_PORT=3000
NODE_ENV=production
```

### Docker配置
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### 数据库迁移
```javascript
// migrations/001_create_tables.js
exports.up = async (knex) => {
  await knex.schema.createTable('health_assessments', table => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users');
    table.decimal('height', 5, 2).notNullable();
    table.decimal('weight', 5, 2).notNullable();
    table.integer('pain_level').checkBetween([0, 10]);
    table.integer('mobility_level').checkBetween([0, 10]);
    table.decimal('bmi', 4, 2);
    table.string('recovery_stage', 50);
    table.timestamps(true, true);
  });
};
```

## 监控和日志

### 日志配置
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

### 性能监控
```javascript
const responseTime = require('response-time');
app.use(responseTime((req, res, time) => {
  logger.info({
    method: req.method,
    url: req.url,
    responseTime: time,
    statusCode: res.statusCode
  });
}));
```

## 开发流程

1. **环境搭建**: 安装Node.js、PostgreSQL、Redis
2. **数据库初始化**: 运行迁移脚本
3. **API开发**: 按优先级实现端点
4. **测试**: 单元测试 + 集成测试
5. **部署**: Docker容器化部署

## 优先级

1. **第1周**: 健康检查端点 + 基础监控
2. **第2周**: 健康评估API + 数据库表
3. **第3周**: 游戏数据API + 统计功能
4. **第4周**: 性能优化 + 完善测试
