const jwt = require('jsonwebtoken');

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************.U8ZD8FJiEe3QVgu2DaB3EGUiFV-Pzcn85nSt_z3MDBk';
const secret = process.env.JWT_SECRET || 'fallback-secret';

console.log('Current time:', Math.floor(Date.now() / 1000));

try {
  const decoded = jwt.verify(token, secret, {
    issuer: 'shoutao-backend',
    audience: 'shoutao-app'
  });
  console.log('Token is valid:', decoded);
} catch (error) {
  console.log('Token verification failed:', error.message);
  console.log('Error details:', error);
}
