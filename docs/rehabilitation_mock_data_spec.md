# 康复训练页面模拟数据规范 (Mock Data Specification)

## 1. 文档概述

### 1.1. 目的

本文档旨在详细定义和说明 `Shoutao` App 中 **康复训练 (Rehabilitation)** 功能模块在 **模拟（Mock）模式** 下所使用的前端数据模型、数据结构以及具体的示例内容。

### 1.2. 受众

本文档主要面向以下人员：

*   **前端开发者**: 理解数据结构，以便进行页面渲染和逻辑开发。
*   **后端开发者**: 以此为参考，设计和实现未来真实的 API 接口。
*   **测试人员**: 基于此数据进行模拟场景的测试。

### 1.3. 范围

本文档内容 **仅限于** Mock 模式下的数据，其来源为 `lib/features/rehabilitation/data/repositories/rehabilitation_repository.dart` 文件中的 `MockRehabilitationRepository` 类。所有数据均为前端代码中硬编码的静态内容，不涉及任何真实的网络请求或数据库交互。

---

## 2. 核心数据模型与示例

### 2.1. TrainingPlan (训练计划)

代表一个今日训练计划。

**模型定义:**

| 字段名        | 数据类型 | 中文说明       |
|---------------|----------|----------------|
| `id`          | `String` | 计划的唯一标识符 |
| `name`        | `String` | 计划名称       |
| `description` | `String` | 计划的简要描述 |
| `duration`    | `int`    | 计划时长（分钟） |
| `status`      | `String` | 当前状态 (例如: "进行中", "未开始") |
| `progress`    | `double` | 训练进度 (0.0 到 1.0) |

**数据示例 (JSON):**

```json
[
  {
    "id": "1",
    "name": "手指灵活训练",
    "description": "提升手指灵活度30%",
    "duration": 20,
    "status": "进行中",
    "progress": 0.0
  },
  {
    "id": "2",
    "name": "手腕力量训练",
    "description": "增强手腕肌肉力量",
    "duration": 15,
    "status": "未开始",
    "progress": 0.0
  },
  {
    "id": "3",
    "name": "抓握训练",
    "description": "提高手部抓握能力",
    "duration": 25,
    "status": "未开始",
    "progress": 0.0
  }
]
```

---

### 2.2. ActionPoint (动作要点)

训练开始前显示的动作要点提示。

**模型定义:**

| 字段名        | 数据类型 | 中文说明     |
|---------------|----------|--------------|
| `order`       | `int`    | 显示顺序     |
| `description` | `String` | 要点描述内容 |

**数据示例 (JSON):**

```json
[
  {
    "order": 1,
    "description": "保持手腕放松，避免过度用力"
  },
  {
    "order": 2,
    "description": "手指自然伸展，保持均匀用力"
  },
  {
    "order": 3,
    "description": "动作幅度要适中，不要过度拉伸"
  }
]
```

---

### 2.3. TrainingVideo (训练视频)

根据用户选择的训练计划动态加载的视频信息。

**逻辑说明:**

视频数据是 **动态返回** 的。系统会根据用户所选 `TrainingPlan` 的 `id`，从预设的视频列表中查找并返回对应的视频信息。映射关系如下：
*   `planId` = `'1'` -> 返回 "手部抓握训练" 视频
*   `planId` = `'2'` -> 返回 "手指灵活性训练" 视频
*   `planId` = `'3'` -> 返回 "协调性训练" 视频
*   其他 `planId` -> 返回 `null`

**模型定义:**

| 字段名          | 数据类型   | 中文说明                   |
|-----------------|------------|----------------------------|
| `id`            | `String`   | 视频的唯一标识符           |
| `title`         | `String`   | 视频标题                   |
| `description`   | `String`   | 视频的简要描述             |
| `thumbnailUrl`  | `String`   | 视频封面图的本地资源路径   |
| `videoUrl`      | `String`   | 视频文件的本地资源路径     |
| `duration`      | `int`      | 视频时长（分钟）           |
| `trainingType`  | `String`   | 训练类型标识符             |

**数据示例 (JSON):**

*   **当 Plan ID 为 "1" 时返回:**
    ```json
    {
      "id": "grip_training_video",
      "title": "手部抓握训练",
      "description": "通过抓握动作增强手部力量",
      "thumbnailUrl": "assets/images/rehabilitation/placeholder.png",
      "videoUrl": "assets/videos/train_1.mp4",
      "duration": 5,
      "trainingType": "grip_training"
    }
    ```
*   **当 Plan ID 为 "2" 时返回:**
    ```json
    {
      "id": "flexibility_training_video",
      "title": "手指灵活性训练",
      "description": "提高手指的灵活性和协调性",
      "thumbnailUrl": "assets/images/rehabilitation/placeholder.png",
      "videoUrl": "assets/videos/train_2.mp4",
      "duration": 8,
      "trainingType": "flexibility_training"
    }
    ```

---

### 2.4. RealTimeData (实时训练数据)

在训练过程中，由硬件（或模拟器）上报的实时数据。

**模型定义:**

| 字段名                  | 数据类型        | 中文说明             |
|-------------------------|-----------------|----------------------|
| `gripStrength`          | `double`        | 抓握力度             |
| `actionAccuracy`        | `double`        | 动作准确度           |
| `emgActivationLevel`    | `double`        | 肌电激活水平         |
| `wristFlexionAngle`     | `double`        | 手腕屈曲角度         |
| `forearmRotationAngle`  | `double`        | 前臂旋转角度         |
| `gripStrengthHistory`   | `List<double>`  | 抓握力度历史数据     |
| `actionAccuracyHistory` | `List<double>`  | 动作准确度历史数据   |
| `emgActivationHistory`  | `List<double>`  | 肌电激活水平历史数据 |

**数据示例 (JSON):**

*注意：在Mock模式下，这些值是由Bloc中的定时器模拟生成的，以下仅为某个时间点的快照示例。*

```json
{
  "gripStrength": 0.78,
  "actionAccuracy": 0.85,
  "emgActivationLevel": 0.65,
  "wristFlexionAngle": 15.5,
  "forearmRotationAngle": -10.2,
  "gripStrengthHistory": [0.75, 0.77, 0.78, 0.76, 0.78],
  "actionAccuracyHistory": [0.82, 0.84, 0.85, 0.86, 0.85],
  "emgActivationHistory": [0.60, 0.62, 0.65, 0.63, 0.64]
}
```

---

### 2.5. TrainingRecord (历史训练记录)

用户完成的训练记录。

**模型定义:**

| 字段名         | 数据类型 | 中文说明       |
|----------------|----------|----------------|
| `id`           | `String` | 记录的唯一标识符 |
| `trainingName` | `String` | 训练名称       |
| `date`         | `String` | 训练日期 (YYYY-MM-DD) |
| `time`         | `String` | 训练时间 (HH:MM) |
| `accuracy`     | `double` | 平均准确度     |
| `isCompleted`  | `bool`   | 是否完成训练   |

**数据示例 (JSON):**

```json
[
  {
    "id": "1",
    "trainingName": "手指灵活训练",
    "date": "2024-01-20",
    "time": "14:30",
    "accuracy": 0.92,
    "isCompleted": true
  },
  {
    "id": "2",
    "trainingName": "手腕力量训练",
    "date": "2024-01-19",
    "time": "15:45",
    "accuracy": 0.88,
    "isCompleted": true
  },
  {
    "id": "3",
    "trainingName": "抓握训练",
    "date": "2024-01-18",
    "time": "09:30",
    "accuracy": 0.90,
    "isCompleted": false
  }
]
``` 