# 安卓App - 康复训练API开发文档

## 1. 介绍

本文档为安卓App开发人员提供与后端 **康复训练 (Rehabilitation)** 模块API交互所需的技术规范。

### 1.1 基础URL

所有API请求都应基于以下URL：

- **开发环境**: `http://<your-dev-domain>/api/v1/app`
- **生产环境**: `http://<your-prod-domain>/api/v1/app`

### 1.2 认证

所有此模块下的API都需要用户认证。请在每个请求的HTTP `Authorization`头中携带从用户登录接口获取的`accessToken`。

- **格式**: `Authorization: Bearer <accessToken>`

### 1.3 响应结构

所有API响应都遵循项目统一的JSON结构，包含`success`, `message`, `data`, `timestamp`, `error`等字段。

---

## 2. API端点

### 2.1 获取训练计划列表

此接口用于获取所有当前可用的训练计划，供用户在App主页上选择。

- **Endpoint**: `GET /rehabilitation/plans`
- **认证**: 需要用户`accessToken`。
- **成功响应 (200 OK)**:
  - `data`字段是一个数组，每个对象都严格遵循`rehabilitation_mock_data_spec.md`中定义的`TrainingPlan`模型。
  - `status`和`progress`字段将始终为`"未开始"`和`0.0`，因为这些是用户状态，由App本地管理。

- **响应示例**:
  ```json
  {
    "success": true,
    "message": "Training plans retrieved successfully.",
    "data": [
      {
        "id": "plan-uuid-1",
        "name": "手指灵活训练",
        "description": "提升手指灵活度30%",
        "duration": 20,
        "status": "未开始",
        "progress": 0.0
      },
      {
        "id": "plan-uuid-2",
        "name": "手腕力量训练",
        "description": "增强手腕肌肉力量",
        "duration": 15,
        "status": "未开始",
        "progress": 0.0
      }
    ],
    "timestamp": "...",
    "error": null
  }
  ```

### 2.2 获取训练计划详情

当用户选择一个具体的训练计划后，调用此接口获取其完整的准备信息，包括动作要点和训练视频。

- **Endpoint**: `GET /rehabilitation/plans/:id`
- **认证**: 需要用户`accessToken`。
- **路径参数**:
  - `id` (string, required): 要获取详情的训练计划的UUID。
- **成功响应 (200 OK)**:
  - `data`字段是一个对象，包含了`TrainingPlan`的所有基础字段，并额外嵌套了`actionPoints`数组和`video`对象。
  - 所有字段和嵌套对象的结构都严格遵循`rehabilitation_mock_data_spec.md`中的定义。

- **响应示例**:
  ```json
  {
    "success": true,
    "message": "Training plan details retrieved successfully.",
    "data": {
      "id": "plan-uuid-1",
      "name": "手指灵活训练",
      "description": "提升手指灵活度30%",
      "duration": 20,
      "status": "未开始",
      "progress": 0.0,
      "actionPoints": [
        { "order": 1, "description": "保持手腕放松，避免过度用力" },
        { "order": 2, "description": "手指自然伸展，保持均匀用力" }
      ],
      "video": {
        "id": "video-uuid-1",
        "title": "手部抓握训练",
        "description": "通过抓握动作增强手部力量",
        "thumbnailUrl": "/uploads/thumbnails/some-image.png",
        "videoUrl": "/uploads/videos/some-video.mp4",
        "duration": 300,
        "trainingType": "beginner"
      }
    },
    "timestamp": "...",
    "error": null
  }
  ``` 