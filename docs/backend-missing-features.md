# 后端管理系统缺失功能开发文档

## 📋 文档概述

本文档基于现有Flutter项目（shoutaoapp0.3）和对接01.md接口文档的深入分析，详细说明了开发后端管理系统所需的缺失功能和API接口。

**文档版本**: v1.0
**创建时间**: 2025-07-26
**适用范围**: 后端开发团队、前端集成团队

---

## 🔍 现有API接口分析

### 1. 已实现的康复训练API接口

基于对接01.md文档和现有代码分析，以下接口已经实现：

#### 1.1 康复训练核心接口 ✅
```
GET /api/v1/training/rehabilitation/action-points
GET /api/v1/training/rehabilitation/training-targets
GET /api/v1/training/rehabilitation/training-plans
GET /api/v1/training/rehabilitation/training-records
GET /api/v1/training/rehabilitation/training-records/daily
DELETE /api/v1/training/rehabilitation/user-data
```

#### 1.2 用户认证接口 ✅
```
POST /api/v1/auth/login-password
POST /api/v1/auth/login-phone
POST /api/v1/auth/refresh
POST /api/v1/auth/send-sms
```

#### 1.3 用户管理接口 ✅
```
GET /api/v1/users/profile
GET /api/v1/users/{userId}
GET /api/v1/home/<USER>/{userId}
```

#### 1.4 训练会话接口 ✅
```
POST /api/v1/training/sessions
PUT /api/v1/training/sessions/{sessionId}
GET /api/v1/training/sessions
```

### 2. 现有API响应格式规范

所有现有API都遵循统一的响应格式：

```typescript
interface UnifiedApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  timestamp: string;
  error?: {
    code: string;
    message: string;
    details?: Record<string, any>;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

### 3. 现有认证机制

- **认证方式**: Bearer Token (JWT)
- **Token有效期**: 15分钟（access_token）
- **刷新Token有效期**: 7天（refresh_token）
- **请求头格式**: `Authorization: Bearer {access_token}`

---

## ❌ 缺失的管理系统API接口

### 1. 管理员认证系统

#### 1.1 管理员登录接口 ❌
```http
POST /api/v1/admin/auth/login
Content-Type: application/json

Request:
{
  "username": "admin",
  "password": "admin123"
}

Response:
{
  "success": true,
  "data": {
    "accessToken": "jwt_token_here",
    "refreshToken": "refresh_token_here",
    "admin": {
      "id": "admin-001",
      "username": "admin",
      "fullName": "系统管理员",
      "role": "super_admin",
      "email": "<EMAIL>",
      "permissions": ["read", "write", "delete", "manage_users"]
    }
  },
  "message": "管理员登录成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 1.2 管理员Token刷新接口 ❌
```http
POST /api/v1/admin/auth/refresh
Authorization: Bearer {refresh_token}

Response:
{
  "success": true,
  "data": {
    "accessToken": "new_jwt_token_here"
  },
  "message": "Token刷新成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 1.3 管理员登出接口 ❌
```http
POST /api/v1/admin/auth/logout
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "message": "登出成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

### 2. 训练计划管理接口

#### 2.1 创建训练计划 ❌
```http
POST /api/v1/admin/training-plans
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "name": "高级康复训练",
  "description": "适合康复后期的高强度训练",
  "content": "详细的训练计划内容...",
  "difficultyLevel": "advanced",
  "durationMinutes": 45,
  "targetMuscles": ["手臂", "手腕", "前臂"],
  "equipmentNeeded": ["康复手套", "力量训练器"],
  "instructions": [
    "热身5分钟",
    "主要训练30分钟",
    "放松10分钟"
  ],
  "videoUrls": ["video-001", "video-002"],
  "isActive": true,
  "sortOrder": 1
}

Response:
{
  "success": true,
  "data": {
    "id": "plan-002",
    "name": "高级康复训练",
    "description": "适合康复后期的高强度训练",
    "content": "详细的训练计划内容...",
    "difficultyLevel": "advanced",
    "durationMinutes": 45,
    "targetMuscles": ["手臂", "手腕", "前臂"],
    "equipmentNeeded": ["康复手套", "力量训练器"],
    "instructions": [
      "热身5分钟",
      "主要训练30分钟",
      "放松10分钟"
    ],
    "videoUrls": ["video-001", "video-002"],
    "isActive": true,
    "sortOrder": 1,
    "createdBy": "admin-001",
    "createdAt": "2025-07-26T10:00:00.000Z",
    "updatedAt": "2025-07-26T10:00:00.000Z"
  },
  "message": "训练计划创建成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 2.2 获取训练计划管理列表 ❌
```http
GET /api/v1/admin/training-plans?page=1&limit=20&search=&difficulty=&status=
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": "plan-001",
        "name": "基础康复训练",
        "description": "适合初学者的基础训练计划",
        "difficultyLevel": "beginner",
        "durationMinutes": 30,
        "targetMuscles": ["手臂", "手腕"],
        "equipmentNeeded": ["康复手套"],
        "videoCount": 3,
        "isActive": true,
        "sortOrder": 1,
        "createdBy": "admin-001",
        "createdAt": "2025-07-26T10:00:00.000Z",
        "updatedAt": "2025-07-26T10:00:00.000Z"
      }
    ]
  },
  "message": "获取成功",
  "timestamp": "2025-07-26T10:00:00.000Z",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  }
}
```

#### 2.3 更新训练计划 ❌
```http
PUT /api/v1/admin/training-plans/{id}
Authorization: Bearer {access_token}
Content-Type: application/json

Request: (同创建接口的请求格式)

Response:
{
  "success": true,
  "data": {
    "id": "plan-002",
    // ... 更新后的完整数据
  },
  "message": "训练计划更新成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 2.4 删除训练计划 ❌
```http
DELETE /api/v1/admin/training-plans/{id}
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "message": "训练计划删除成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 2.5 批量操作训练计划 ❌
```http
POST /api/v1/admin/training-plans/batch
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "action": "delete", // delete, activate, deactivate
  "planIds": ["plan-001", "plan-002", "plan-003"]
}

Response:
{
  "success": true,
  "data": {
    "processed": 3,
    "failed": 0,
    "results": [
      {"id": "plan-001", "status": "success"},
      {"id": "plan-002", "status": "success"},
      {"id": "plan-003", "status": "success"}
    ]
  },
  "message": "批量操作完成",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

### 3. 动作要点管理接口

#### 3.1 创建动作要点 ❌
```http
POST /api/v1/admin/action-points
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "title": "呼吸配合技巧",
  "description": "训练时的正确呼吸方法",
  "content": "详细的呼吸技巧说明...",
  "category": "coordination",
  "importanceLevel": "medium",
  "iconUrl": "/icons/breathing.png",
  "videoUrls": ["video-004"],
  "sortOrder": 5,
  "isActive": true
}

Response:
{
  "success": true,
  "data": {
    "id": "action-002",
    "title": "呼吸配合技巧",
    "description": "训练时的正确呼吸方法",
    "content": "详细的呼吸技巧说明...",
    "category": "coordination",
    "importanceLevel": "medium",
    "iconUrl": "/icons/breathing.png",
    "videoUrls": ["video-004"],
    "sortOrder": 5,
    "isActive": true,
    "createdBy": "admin-001",
    "createdAt": "2025-07-26T10:00:00.000Z",
    "updatedAt": "2025-07-26T10:00:00.000Z"
  },
  "message": "动作要点创建成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 3.2 获取动作要点管理列表 ❌
```http
GET /api/v1/admin/action-points?page=1&limit=20&search=&category=&importance=
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "actionPoints": [
      {
        "id": "action-001",
        "title": "正确的握持姿势",
        "description": "保持手腕自然弯曲...",
        "content": "详细的动作要点内容...",
        "category": "strength",
        "importanceLevel": "high",
        "iconUrl": "/icons/grip.png",
        "videoUrls": ["video-003"],
        "sortOrder": 1,
        "isActive": true,
        "createdBy": "admin-001",
        "createdAt": "2025-07-26T10:00:00.000Z",
        "updatedAt": "2025-07-26T10:00:00.000Z"
      }
    ]
  },
  "message": "获取成功",
  "timestamp": "2025-07-26T10:00:00.000Z",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 30,
    "totalPages": 2
  }
}
```

#### 3.3 更新动作要点 ❌
```http
PUT /api/v1/admin/action-points/{id}
Authorization: Bearer {access_token}
Content-Type: application/json

Request: (同创建接口的请求格式)

Response:
{
  "success": true,
  "data": {
    "id": "action-002",
    // ... 更新后的完整数据
  },
  "message": "动作要点更新成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 3.4 删除动作要点 ❌
```http
DELETE /api/v1/admin/action-points/{id}
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "message": "动作要点删除成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 3.5 批量操作动作要点 ❌
```http
POST /api/v1/admin/action-points/batch
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "action": "delete", // delete, activate, deactivate, update_category
  "actionPointIds": ["action-001", "action-002"],
  "params": {
    "category": "strength" // 仅当action为update_category时需要
  }
}

Response:
{
  "success": true,
  "data": {
    "processed": 2,
    "failed": 0,
    "results": [
      {"id": "action-001", "status": "success"},
      {"id": "action-002", "status": "success"}
    ]
  },
  "message": "批量操作完成",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

### 4. 视频文件管理接口

#### 4.1 上传视频文件 ❌
```http
POST /api/v1/admin/videos/upload
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

Request:
- file: (视频文件)
- title: "训练演示视频"
- description: "基础握持动作演示"
- category: "training_plan" // training_plan, action_point, general
- relatedId: "plan-001" // 关联的训练计划或动作要点ID

Response:
{
  "success": true,
  "data": {
    "id": "video-005",
    "title": "训练演示视频",
    "description": "基础握持动作演示",
    "fileName": "training_demo_20250726.mp4",
    "filePath": "/uploads/videos/training_demo_20250726.mp4",
    "fileSize": 15728640,
    "durationSeconds": 120,
    "format": "mp4",
    "thumbnailUrl": "/uploads/thumbnails/training_demo_20250726.jpg",
    "category": "training_plan",
    "relatedId": "plan-001",
    "uploadStatus": "completed",
    "createdBy": "admin-001",
    "createdAt": "2025-07-26T10:00:00.000Z"
  },
  "message": "视频上传成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 4.2 获取视频列表 ❌
```http
GET /api/v1/admin/videos?page=1&limit=20&search=&category=&status=
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "videos": [
      {
        "id": "video-001",
        "title": "基础训练演示",
        "description": "展示基础训练动作",
        "fileName": "basic_training.mp4",
        "filePath": "/uploads/videos/basic_training.mp4",
        "fileSize": 20971520,
        "durationSeconds": 180,
        "format": "mp4",
        "thumbnailUrl": "/uploads/thumbnails/basic_training.jpg",
        "category": "training_plan",
        "relatedId": "plan-001",
        "uploadStatus": "completed",
        "createdBy": "admin-001",
        "createdAt": "2025-07-26T10:00:00.000Z"
      }
    ]
  },
  "message": "获取成功",
  "timestamp": "2025-07-26T10:00:00.000Z",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 25,
    "totalPages": 2
  }
}
```

#### 4.3 更新视频信息 ❌
```http
PUT /api/v1/admin/videos/{id}
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "title": "更新后的视频标题",
  "description": "更新后的视频描述",
  "category": "action_point",
  "relatedId": "action-001"
}

Response:
{
  "success": true,
  "data": {
    "id": "video-001",
    // ... 更新后的完整数据
  },
  "message": "视频信息更新成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 4.4 删除视频文件 ❌
```http
DELETE /api/v1/admin/videos/{id}
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "message": "视频删除成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 4.5 批量删除视频 ❌
```http
POST /api/v1/admin/videos/batch-delete
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "videoIds": ["video-001", "video-002", "video-003"]
}

Response:
{
  "success": true,
  "data": {
    "deleted": 3,
    "failed": 0,
    "results": [
      {"id": "video-001", "status": "success"},
      {"id": "video-002", "status": "success"},
      {"id": "video-003", "status": "success"}
    ]
  },
  "message": "批量删除完成",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

### 5. 系统管理接口

#### 5.1 获取系统统计 ❌
```http
GET /api/v1/admin/dashboard/stats
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "trainingPlans": {
      "total": 50,
      "active": 45,
      "inactive": 5,
      "byDifficulty": {
        "beginner": 20,
        "intermediate": 18,
        "advanced": 12
      }
    },
    "actionPoints": {
      "total": 30,
      "active": 28,
      "inactive": 2,
      "byCategory": {
        "strength": 8,
        "flexibility": 7,
        "balance": 8,
        "coordination": 7
      }
    },
    "videos": {
      "total": 25,
      "totalSize": "2.5GB",
      "byStatus": {
        "completed": 23,
        "uploading": 1,
        "failed": 1
      },
      "byCategory": {
        "training_plan": 15,
        "action_point": 8,
        "general": 2
      }
    },
    "users": {
      "total": 1250,
      "active": 980,
      "newThisMonth": 45
    },
    "trainingRecords": {
      "totalSessions": 15680,
      "thisMonth": 1250,
      "averageSessionDuration": 32
    }
  },
  "message": "获取成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 5.2 获取操作日志 ❌
```http
GET /api/v1/admin/logs?page=1&limit=50&action=&adminId=&startDate=&endDate=
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log-001",
        "adminId": "admin-001",
        "adminName": "系统管理员",
        "action": "CREATE_TRAINING_PLAN",
        "resourceType": "training_plan",
        "resourceId": "plan-002",
        "details": {
          "planName": "高级康复训练",
          "difficultyLevel": "advanced"
        },
        "ipAddress": "*************",
        "userAgent": "Mozilla/5.0...",
        "createdAt": "2025-07-26T10:00:00.000Z"
      }
    ]
  },
  "message": "获取成功",
  "timestamp": "2025-07-26T10:00:00.000Z",
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 500,
    "totalPages": 10
  }
}
```

#### 5.3 系统配置管理 ❌
```http
GET /api/v1/admin/configs
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "configs": [
      {
        "id": "config-001",
        "configKey": "max_video_size",
        "configValue": "100",
        "description": "最大视频文件大小(MB)",
        "configType": "number",
        "isEditable": true
      },
      {
        "id": "config-002",
        "configKey": "allowed_video_formats",
        "configValue": "[\"mp4\", \"avi\", \"mov\", \"wmv\"]",
        "description": "允许的视频格式",
        "configType": "json",
        "isEditable": true
      }
    ]
  },
  "message": "获取成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

#### 5.4 更新系统配置 ❌
```http
PUT /api/v1/admin/configs/{configKey}
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "configValue": "150",
  "description": "最大视频文件大小(MB) - 已更新"
}

Response:
{
  "success": true,
  "data": {
    "id": "config-001",
    "configKey": "max_video_size",
    "configValue": "150",
    "description": "最大视频文件大小(MB) - 已更新",
    "configType": "number",
    "isEditable": true,
    "updatedAt": "2025-07-26T10:00:00.000Z"
  },
  "message": "配置更新成功",
  "timestamp": "2025-07-26T10:00:00.000Z"
}
```

---

## 🗄️ 数据库表结构补充需求

### 1. 管理员表 (admin_users)
```sql
CREATE TABLE admin_users (
  id VARCHAR(36) PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  email VARCHAR(100),
  full_name VARCHAR(100),
  role ENUM('super_admin', 'admin', 'editor') DEFAULT 'admin',
  permissions JSON, -- 权限列表
  is_active BOOLEAN DEFAULT true,
  last_login_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 训练计划管理表 (training_plans_admin)
```sql
CREATE TABLE training_plans_admin (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  content TEXT, -- 训练计划详细内容
  difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
  duration_minutes INT DEFAULT 30,
  target_muscles JSON, -- 目标肌群数组
  equipment_needed JSON, -- 所需设备数组
  instructions JSON, -- 训练指导步骤
  video_urls JSON, -- 关联的视频文件URLs
  is_active BOOLEAN DEFAULT true,
  sort_order INT DEFAULT 0,
  created_by VARCHAR(36),
  updated_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES admin_users(id),
  FOREIGN KEY (updated_by) REFERENCES admin_users(id)
);
```

### 3. 动作要点管理表 (action_points_admin)
```sql
CREATE TABLE action_points_admin (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  content TEXT, -- 详细内容
  category ENUM('strength', 'flexibility', 'balance', 'coordination') NOT NULL,
  importance_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
  icon_url VARCHAR(500),
  video_urls JSON, -- 关联的演示视频
  sort_order INT DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_by VARCHAR(36),
  updated_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES admin_users(id),
  FOREIGN KEY (updated_by) REFERENCES admin_users(id)
);
```

### 4. 视频文件管理表 (training_videos)
```sql
CREATE TABLE training_videos (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size BIGINT, -- 文件大小（字节）
  duration_seconds INT, -- 视频时长
  format VARCHAR(20), -- 视频格式 (mp4, avi, etc.)
  thumbnail_url VARCHAR(500), -- 缩略图URL
  category ENUM('training_plan', 'action_point', 'general') DEFAULT 'general',
  related_id VARCHAR(36), -- 关联的训练计划或动作要点ID
  is_active BOOLEAN DEFAULT true,
  upload_status ENUM('uploading', 'completed', 'failed') DEFAULT 'uploading',
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES admin_users(id)
);
```

### 5. 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
  id VARCHAR(36) PRIMARY KEY,
  config_key VARCHAR(100) UNIQUE NOT NULL,
  config_value TEXT,
  description TEXT,
  config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  is_editable BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 6. 操作日志表 (admin_logs)
```sql
CREATE TABLE admin_logs (
  id VARCHAR(36) PRIMARY KEY,
  admin_id VARCHAR(36),
  action VARCHAR(100) NOT NULL, -- 操作类型
  resource_type VARCHAR(50), -- 资源类型
  resource_id VARCHAR(36), -- 资源ID
  details JSON, -- 操作详情
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (admin_id) REFERENCES admin_users(id)
);
```

---

## 📁 文件上传和存储技术方案

### 1. 文件存储架构

#### 1.1 本地存储结构
```
/uploads/
├── videos/
│   ├── training_plans/
│   ├── action_points/
│   └── general/
├── thumbnails/
│   ├── auto_generated/
│   └── custom/
├── icons/
└── temp/
    └── uploads/
```

#### 1.2 文件命名规范
```javascript
// 视频文件命名格式
const videoFileName = `${category}_${relatedId}_${timestamp}_${originalName}`;
// 例如: training_plan_001_20250726_basic_training.mp4

// 缩略图命名格式
const thumbnailFileName = `${videoId}_thumbnail.jpg`;
// 例如: video-001_thumbnail.jpg
```

### 2. 文件上传处理流程

#### 2.1 上传前验证
```javascript
const uploadValidation = {
  maxFileSize: 100 * 1024 * 1024, // 100MB
  allowedFormats: ['mp4', 'avi', 'mov', 'wmv'],
  maxDuration: 600, // 10分钟
  minDuration: 5, // 5秒
};
```

#### 2.2 上传处理步骤
1. **文件验证**: 检查文件大小、格式、时长
2. **临时存储**: 先存储到temp目录
3. **视频处理**: 生成缩略图、获取元数据
4. **最终存储**: 移动到正式目录
5. **数据库记录**: 保存文件信息到数据库
6. **清理临时文件**: 删除temp目录中的临时文件

#### 2.3 缩略图生成
```javascript
// 使用FFmpeg生成缩略图
const generateThumbnail = async (videoPath, outputPath) => {
  return ffmpeg(videoPath)
    .screenshots({
      timestamps: ['10%'], // 在视频10%位置截取
      filename: 'thumbnail.jpg',
      folder: outputPath,
      size: '320x240'
    });
};
```

### 3. 云存储集成方案

#### 3.1 阿里云OSS集成
```javascript
const ossConfig = {
  region: 'oss-cn-hangzhou',
  bucket: 'shoutao-videos',
  accessKeyId: process.env.OSS_ACCESS_KEY_ID,
  accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
};

// 上传到OSS
const uploadToOSS = async (localPath, ossPath) => {
  const client = new OSS(ossConfig);
  return await client.put(ossPath, localPath);
};
```

#### 3.2 CDN加速配置
```javascript
const cdnConfig = {
  domain: 'https://cdn.shoutaoapp.com',
  videoPath: '/videos/',
  thumbnailPath: '/thumbnails/',
};

// 生成CDN URL
const generateCDNUrl = (filePath) => {
  return `${cdnConfig.domain}${filePath}`;
};
```

---

## 🔗 与现有系统的集成方案

### 1. API路径规划

#### 1.1 管理系统API路径
```
/api/v1/admin/          # 管理系统专用API前缀
├── auth/               # 管理员认证
├── training-plans/     # 训练计划管理
├── action-points/      # 动作要点管理
├── videos/             # 视频文件管理
├── dashboard/          # 仪表板统计
├── logs/               # 操作日志
└── configs/            # 系统配置
```

#### 1.2 与现有API的关系
```
现有用户API: /api/v1/training/rehabilitation/*
管理系统API: /api/v1/admin/*

数据流向:
管理系统 → 创建/编辑内容 → 用户API消费
```

### 2. 数据同步机制

#### 2.1 训练计划同步
```javascript
// 管理系统创建训练计划后，同步到用户API
const syncTrainingPlan = async (planData) => {
  // 1. 在admin表中创建记录
  const adminPlan = await createAdminTrainingPlan(planData);

  // 2. 同步到用户API使用的表
  const userPlan = await syncToUserTrainingPlans(adminPlan);

  // 3. 清除相关缓存
  await clearTrainingPlanCache();

  return { adminPlan, userPlan };
};
```

#### 2.2 动作要点同步
```javascript
// 管理系统更新动作要点后，同步到用户API
const syncActionPoints = async (actionPointData) => {
  // 1. 更新admin表
  const adminActionPoint = await updateAdminActionPoint(actionPointData);

  // 2. 同步到用户API表
  const userActionPoint = await syncToUserActionPoints(adminActionPoint);

  // 3. 触发缓存更新
  await refreshActionPointsCache();

  return { adminActionPoint, userActionPoint };
};
```

### 3. 权限控制集成

#### 3.1 管理员权限定义
```javascript
const adminPermissions = {
  SUPER_ADMIN: [
    'read', 'write', 'delete', 'manage_users', 'manage_configs', 'view_logs'
  ],
  ADMIN: [
    'read', 'write', 'delete', 'view_logs'
  ],
  EDITOR: [
    'read', 'write'
  ]
};
```

#### 3.2 权限验证中间件
```javascript
const checkPermission = (requiredPermission) => {
  return async (req, res, next) => {
    const admin = req.admin; // 从JWT token中解析
    const hasPermission = admin.permissions.includes(requiredPermission);

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'PERMISSION_DENIED',
          message: '权限不足'
        },
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};
```

### 4. 错误处理统一

#### 4.1 错误码定义
```javascript
const adminErrorCodes = {
  // 认证相关
  ADMIN_AUTH_INVALID_CREDENTIALS: 'A001',
  ADMIN_AUTH_TOKEN_EXPIRED: 'A002',
  ADMIN_AUTH_TOKEN_INVALID: 'A003',

  // 权限相关
  ADMIN_PERMISSION_DENIED: 'A101',
  ADMIN_ROLE_INVALID: 'A102',

  // 资源相关
  ADMIN_RESOURCE_NOT_FOUND: 'A201',
  ADMIN_RESOURCE_CONFLICT: 'A202',
  ADMIN_RESOURCE_VALIDATION_ERROR: 'A203',

  // 文件相关
  ADMIN_FILE_TOO_LARGE: 'A301',
  ADMIN_FILE_FORMAT_INVALID: 'A302',
  ADMIN_FILE_UPLOAD_FAILED: 'A303',
};
```

#### 4.2 统一错误处理器
```javascript
const adminErrorHandler = (error, req, res, next) => {
  const errorResponse = {
    success: false,
    message: error.message || '服务器内部错误',
    timestamp: new Date().toISOString(),
    error: {
      code: error.code || 'INTERNAL_ERROR',
      message: error.message || '未知错误',
      details: error.details || {}
    }
  };

  // 记录错误日志
  logger.error('Admin API Error:', {
    error: errorResponse,
    request: {
      method: req.method,
      url: req.url,
      admin: req.admin?.id
    }
  });

  res.status(error.statusCode || 500).json(errorResponse);
};
```

---

## 🚀 开发优先级建议

### 第一阶段 (高优先级)
1. **管理员认证系统** - 基础安全保障
2. **训练计划CRUD接口** - 核心功能
3. **基础文件上传功能** - 支持视频管理

### 第二阶段 (中优先级)
1. **动作要点管理接口** - 内容管理完善
2. **视频文件管理接口** - 多媒体内容支持
3. **系统统计接口** - 管理监控

### 第三阶段 (低优先级)
1. **批量操作接口** - 效率提升
2. **操作日志系统** - 审计追踪
3. **系统配置管理** - 灵活配置

---

## 📝 开发注意事项

### 1. 数据一致性
- 确保管理系统和用户API数据同步
- 实现事务处理，避免数据不一致
- 建立数据校验机制

### 2. 性能考虑
- 大文件上传的分片处理
- 视频处理的异步队列
- 缓存策略的合理使用

### 3. 安全要求
- 管理员权限严格控制
- 文件上传安全验证
- 操作日志完整记录

### 4. 扩展性设计
- 模块化的API设计
- 可配置的业务规则
- 支持未来功能扩展

---

**文档维护**: 开发团队
**最后更新**: 2025-07-26
**状态**: 待开发实现
