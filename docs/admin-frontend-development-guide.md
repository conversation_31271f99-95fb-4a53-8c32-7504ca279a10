# 后台管理系统 - 前端开发技术文档

## 1. 介绍

本文档旨在为后台管理系统的前端开发人员提供与后端API交互所需的所有技术规范和指南，包括环境配置、开发服务器设置和常见问题解决方案。

### 1.1 系统架构概览

**后端技术栈**:
- Node.js + TypeScript (主要API服务)
- Express.js (Web框架)
- Socket.IO (实时通信，端口3001)
- PostgreSQL (主数据库)
- Redis (缓存和会话存储)
- Prisma (ORM)

### 1.2 基础URL

所有API请求都应基于以下URL：

- **开发环境**: `http://localhost:3000/api/v1/admin`
- **生产环境**: `http://<your-prod-domain>/api/v1/admin`

### 1.3 API约定

- **请求格式**: 所有`POST`, `PUT`, `PATCH`请求的`Content-Type`应为`application/json`，除非另有说明（例如，文件上传）。
- **响应格式**: 所有API响应都遵循统一的JSON结构。
- **日期格式**: 所有日期和时间均采用`ISO 8601`格式 (e.g., `2023-10-27T10:00:00.000Z`)。

---

## 2. 后端环境配置与启动

### 2.1 环境要求

在开始前端开发之前，请确保后端环境已正确配置：

**系统要求**:
- Node.js >= 18.0.0
- npm >= 8.0.0
- PostgreSQL >= 15
- Redis >= 7.0 (可选，用于缓存)

### 2.2 环境变量配置

后端使用 `.env` 文件进行环境配置。请确保项目根目录下存在 `.env` 文件，包含以下必需变量：

```bash
# 核心应用配置
NODE_ENV=development
PORT=3000

# 数据库配置 (必需)
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# 安全配置 (必需)
JWT_SECRET=your-super-secret-jwt-key-change-in-production-32-chars-min
ENCRYPTION_KEY=your-encryption-key-for-medical-data-32-chars-min

# JWT令牌配置
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# CORS配置
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# 速率限制
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# WebSocket配置
WEBSOCKET_PORT=3001
WEBSOCKET_CORS_ORIGIN=*

# 健康检查
HEALTH_CHECK_TIMEOUT=5000

# 监控配置
METRICS_ENABLED=true

# 可选配置
REDIS_URL=redis://localhost:6379
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 2.3 快速启动后端服务

1. **克隆项目并安装依赖**:
   ```bash
   cd /path/to/backend/project
   npm install
   ```

2. **配置环境变量**:
   ```bash
   # 如果没有.env文件，从模板复制
   cp .env.example .env
   # 编辑.env文件，设置正确的数据库连接等信息
   ```

3. **数据库设置**:
   ```bash
   # 生成Prisma客户端
   npx prisma generate

   # 运行数据库迁移
   npx prisma migrate dev

   # (可选) 填充测试数据
   npm run db:seed
   ```

4. **启动开发服务器**:
   ```bash
   npm run dev
   ```

   成功启动后，您应该看到类似以下的输出：
   ```
   [INFO] Starting Shoutao Rehabilitation Training App...
   🚀 Server running on port 3000
   📊 Environment: development
   🔗 Database: Connected
   🌐 WebSocket: Enabled
   ```

### 2.4 验证后端服务

启动后，您可以通过以下方式验证后端服务是否正常运行：

- **健康检查**: `GET http://localhost:3000/health`
- **API文档**: `GET http://localhost:3000/api-docs` (Swagger UI)
- **WebSocket连接**: `ws://localhost:3001`

---

## 3. 统一响应结构

每个API响应都将包含以下字段：

```json
{
  "success": true, // boolean: true表示成功, false表示失败
  "message": "操作成功", // string: 操作结果的描述信息
  "data": {}, // object | array | null: 成功时返回的数据
  "timestamp": "2023-10-27T10:00:00.000Z", // string: 服务器响应时间戳
  "error": null, // object | null: 失败时返回的错误详情
  "pagination": null // object | null: 列表数据分页信息
}
```

### 3.1 成功响应示例

```json
{
  "success": true,
  "message": "用户创建成功",
  "data": {
    "id": "clxql0b3i00003b6q4y7f8z9a",
    "username": "newAdmin",
    "createdAt": "2025-07-26T06:54:20.123Z"
  },
  "timestamp": "2025-07-26T06:54:20.124Z",
  "error": null,
  "pagination": null
}
```

### 3.2 失败响应示例

```json
{
  "success": false,
  "message": "输入验证失败",
  "data": null,
  "timestamp": "2025-07-26T06:55:00.456Z",
  "error": {
    "code": "VALIDATION_ERROR",
    "details": [
      {
        "field": "password",
        "message": "密码长度不能少于8个字符"
      }
    ]
  },
  "pagination": null
}
```

### 3.3 分页结构

对于返回列表数据的API，`pagination`对象将包含以下信息：

```json
{
  "pagination": {
    "totalItems": 100,
    "totalPages": 10,
    "currentPage": 1,
    "pageSize": 10
  }
}
```

---

## 4. 认证 (Authentication)

本系统使用JWT (JSON Web Token)进行认证。

### 4.1 认证流程

1.  **登录**: 前端使用用户名和密码调用登录接口。
2.  **获取Token**: 登录成功后，后端会返回`accessToken`和`refreshToken`。
3.  **存储Token**: 前端需要安全地存储这两个Token（例如，在`localStorage`或`sessionStorage`中）。
4.  **发送请求**: 在之后的所有需要认证的API请求中，必须在HTTP `Authorization`头中携带`accessToken`。
    -   格式: `Authorization: Bearer <accessToken>`
5.  **刷新Token**: `accessToken`的有效期较短。当它过期时（通常API会返回401 Unauthorized），前端需要使用`refreshToken`调用刷新Token的接口，以获取一对新的`accessToken`和`refreshToken`。
6.  **登出**: 调用登出接口，后端会使`refreshToken`失效。前端应清除本地存储的Token。

### 4.2 认证相关API

#### 4.2.1 管理员登录

- **功能**: 使用用户名和密码进行身份验证。
- **Endpoint**: `POST /auth/login`
- **请求体**:
  ```json
  {
    "username": "admin",
    "password": "yourpassword"
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "success": true,
    "message": "登录成功",
    "data": {
      "accessToken": "ey...",
      "refreshToken": "ey..."
    },
    // ...
  }
  ```

#### 4.2.2 刷新Token

- **功能**: 使用有效的`refreshToken`获取新的`accessToken`和`refreshToken`。
- **Endpoint**: `POST /auth/refresh`
- **请求体**:
  ```json
  {
    "refreshToken": "your_current_refresh_token"
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "success": true,
    "message": "令牌刷新成功",
    "data": {
      "accessToken": "new_ey...",
      "refreshToken": "new_ey..."
    },
    // ...
  }
  ```

#### 4.2.3 管理员登出

- **功能**: 使当前用户的`refreshToken`失效。
- **Endpoint**: `POST /auth/logout`
- **认证**: 需要`accessToken`。
- **成功响应 (200 OK)**:
  ```json
  {
    "success": true,
    "message": "登出成功",
    "data": null,
    // ...
  }
  ```

---

## 5. 训练计划管理 (Training Plans)

需要`read`或`write`权限。

#### 4.1 创建训练计划

- **Endpoint**: `POST /training-plans`
- **权限**: `write`
- **请求体**:
  ```json
  {
    "name": "初级康复训练计划",
    "description": "适用于第一阶段康复患者的训练内容。",
    "cycle": 30, // 训练周期（天）
    "actionPoints": [
      { "id": "action_point_id_1" },
      { "id": "action_point_id_2" }
    ]
  }
  ```
- **成功响应 (201 Created)**: 返回新创建的训练计划对象。

#### 4.2 获取训练计划列表

- **Endpoint**: `GET /training-plans`
- **权限**: `read`
- **查询参数**:
  - `page` (number, optional, default: 1): 页码。
  - `pageSize` (number, optional, default: 10): 每页数量。
  - `sortBy` (string, optional, default: 'createdAt'): 排序字段。
  - `sortOrder` (string, optional, default: 'desc'): 排序顺序 (`asc` | `desc`)。
- **成功响应 (200 OK)**: 返回训练计划对象数组及分页信息。

#### 4.3 获取单个训练计划

- **Endpoint**: `GET /training-plans/:id`
- **权限**: `read`
- **成功响应 (200 OK)**: 返回指定的训练计划对象。

#### 4.4 更新训练计划

- **Endpoint**: `PUT /training-plans/:id`
- **权限**: `write`
- **请求体**: (只需提供需要更新的字段)
  ```json
  {
    "name": "更新后的训练计划名称",
    "cycle": 45
  }
  ```
- **成功响应 (200 OK)**: 返回更新后的训练计划对象。

#### 4.5 删除训练计划

- **Endpoint**: `DELETE /training-plans/:id`
- **权限**: `delete`
- **成功响应 (200 OK)**:
  ```json
  {
    "success": true,
    "message": "训练计划已成功删除",
    "data": null,
    // ...
  }
  ```

---

## 5. 视频文件管理 (Training Videos)

#### 5.1 上传训练视频

- **功能**: 上传视频文件，后端将进行异步处理（提取元数据、生成缩略图）。
- **Endpoint**: `POST /videos/upload`
- **认证**: 需要`accessToken`。
- **请求格式**: `multipart/form-data`
- **表单字段**:
  - `video` (File): 视频文件本身。
  - `title` (string): 视频标题。
  - `description` (string): 视频描述。
  - `actionPointId` (string): 关联的动作要点ID。
- **成功响应 (202 Accepted)**:
  - 后端立即返回此响应，表示请求已被接受并将进行后台处理。
  - 响应体中包含已创建的视频记录的初始信息（此时状态为`uploading`）。前端可以通过轮询或WebSocket接收处理完成的通知。
  ```json
  {
    "success": true,
    "message": "视频已开始上传和处理，请稍后查看结果。",
    "data": {
      "id": "video_id_123",
      "title": "深蹲教学",
      "status": "UPLOADING",
      // ...
    },
    // ...
  }
  ```
- **处理完成后**: 视频记录的状态会更新为`COMPLETED`或`FAILED`。

---

## 6. 错误码 (Error Codes)

(此部分可根据未来需要进行扩充)

| Code                 | HTTP Status | 描述                               |
| -------------------- | ----------- | ---------------------------------- |
| `UNAUTHORIZED`       | 401         | 未提供或Token无效/已过期。         |
| `FORBIDDEN`          | 403         | 用户已认证，但无权访问该资源。     |
| `NOT_FOUND`          | 404         | 请求的资源不存在。                 |
| `VALIDATION_ERROR`   | 400/422     | 请求参数验证失败。                 |
| `INTERNAL_SERVER_ERROR` | 500         | 服务器内部发生未知错误。           |


---

## 7. 康复训练内容管理 (Rehabilitation Content)

此部分接口用于后台管理人员创建、配置和管理给患者使用的“康复训练包”。一个“训练包”包含一个核心计划、一个关联视频和一组动作要点。

### 7.1 创建训练包

- **功能**: 创建一个完整的训练包。
- **Endpoint**: `POST /training-plans`
- **权限**: `write`
- **请求体**:
  ```json
  {
    "name": "初级手腕屈伸训练",
    "description": "适用于康复早期的基础手腕活动度训练。",
    "difficultyLevel": "beginner",
    "durationMinutes": 15,
    "isActive": true,
    "videoId": "c7a8b9d0-e1f2-3a4b-5c6d-7e8f9a0b1c2d",
    "actionPoints": [
      { "order": 1, "description": "动作要缓慢，感受手腕的伸展。" },
      { "order": 2, "description": "在最大角度保持5秒。" },
      { "order": 3, "description": "如果感到疼痛请立即停止。" }
    ]
  }
  ```
- **成功响应 (201 Created)**: 返回新创建的、包含所有关联数据的完整训练包对象。

### 7.2 更新训练包

- **功能**: 更新一个训练包。注意：每次更新都会**完全替换**所有的动作要点。
- **Endpoint**: `PUT /training-plans/:id`
- **权限**: `write`
- **请求体**: (只需提供需要更新的字段)
  ```json
  {
    "name": "中级手腕屈伸训练 (加强版)",
    "durationMinutes": 20,
    "actionPoints": [
      { "order": 1, "description": "增加手腕的活动范围。" },
      { "order": 2, "description": "可以尝试轻微负重。" }
    ]
  }
  ```
- **成功响应 (200 OK)**: 返回更新后的、包含所有关联数据的完整训练包对象。 