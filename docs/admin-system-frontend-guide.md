# 后端管理系统前端开发文档

## 📋 文档概述

本文档为前端开发团队提供与后端管理系统API集成的技术指南。所有接口都基于RESTful原则设计，并使用统一的响应格式。

**文档版本**: v1.0
**创建时间**: 2025-07-27
**基路径**: `/api/v1/admin`

---

## 🔑 管理员认证

所有管理系统API（除登录和刷新Token外）都需要在HTTP请求头中提供有效的`accessToken`。

**请求头格式**: `Authorization: Bearer {accessToken}`

### 1. 管理员登录

- **Endpoint**: `POST /auth/login`
- **描述**: 使用用户名和密码进行身份验证，获取`accessToken`和`refreshToken`。

**请求体 (JSON)**:
```json
{
  "username": "admin_username",
  "password": "your_password"
}
```

**成功响应 (200 OK)**:
```json
{
  "success": true,
  "data": {
    "admin": {
      "id": "uuid-string",
      "username": "testadmin_auth",
      "email": null,
      "fullName": null,
      "role": "admin",
      "permissions": ["read", "write"],
      "isActive": true,
      "lastLoginAt": "2025-07-27T10:00:00.000Z",
      "createdAt": "2025-07-27T09:59:00.000Z",
      "updatedAt": "2025-07-27T10:00:00.000Z"
    },
    "tokens": {
      "accessToken": "ey...",
      "refreshToken": "ey..."
    }
  },
  "message": "Admin login successful",
  "timestamp": "2025-07-27T10:00:00.000Z"
}
```

### 2. 刷新Token

- **Endpoint**: `POST /auth/refresh`
- **描述**: 使用`refreshToken`获取一个新的`accessToken`和`refreshToken`，用于延长会话有效期。

**请求体 (JSON)**:
```json
{
  "refreshToken": "the_refresh_token_from_login"
}
```

**成功响应 (200 OK)**:
```json
{
  "success": true,
  "data": {
    "accessToken": "new_ey...",
    "refreshToken": "new_ey..."
  },
  "message": "Token refreshed successfully",
  "timestamp": "2025-07-27T10:15:00.000Z"
}
```

### 3. 管理员登出

- **Endpoint**: `POST /auth/logout`
- **描述**: 服务端使`refreshToken`失效。这是一个认证接口，需要在请求头中提供`accessToken`。
- **注意**: 登出后，前端应主动清除本地存储的`accessToken`和`refreshToken`。

**请求体 (JSON) (可选)**:
```json
{
  "refreshToken": "the_refresh_token_to_invalidate"
}
```

**成功响应 (200 OK)**:
```json
{
  "success": true,
  "data": null,
  "message": "Logout successful",
  "timestamp": "2025-07-27T10:20:00.000Z"
}
```

---

## 🏋️ 训练计划管理

- **基路径**: `/training-plans`
- **所需权限**: `read`, `write`, `delete`

### 1. 创建训练计划

- **Endpoint**: `POST /training-plans`

**请求体 (JSON)**:
```json
{
  "name": "初级手腕恢复计划",
  "description": "适用于第一阶段恢复的轻度训练。",
  "difficultyLevel": "beginner",
  "durationMinutes": 15,
  "isActive": true
}
```

**成功响应 (201 Created)**:
```json
{
  "success": true,
  "data": {
    "id": "new-plan-uuid",
    "name": "初级手腕恢复计划",
    "description": "适用于第一阶段恢复的轻度训练。",
    "difficultyLevel": "beginner",
    // ... other fields
  },
  "message": "Training plan created successfully.",
  "timestamp": "2025-07-27T11:00:00.000Z"
}
```

### 2. 获取训练计划列表 (分页/过滤)

- **Endpoint**: `GET /training-plans`
- **查询参数**:
  - `page` (number, optional, default: 1): 页码
  - `limit` (number, optional, default: 10): 每页数量
  - `search` (string, optional): 按计划名称模糊搜索
  - `difficulty` (string, optional): 按难度等级过滤 ('beginner', 'intermediate', 'advanced')
  - `status` (string, optional): 按状态过滤 ('active', 'inactive')

**成功响应 (200 OK)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "plan-uuid-1",
      "name": "Test Plan 1",
      // ...
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1
  },
  "message": "Training plans retrieved successfully.",
  "timestamp": "2025-07-27T11:05:00.000Z"
}
```

### 3. 获取单个训练计划

- **Endpoint**: `GET /training-plans/:id`

**成功响应 (200 OK)**:
```json
{
  "success": true,
  "data": {
    "id": "plan-uuid-1",
    "name": "Test Plan 1",
    // ...
  },
  "message": "Training plan retrieved successfully.",
  "timestamp": "2025-07-27T11:10:00.000Z"
}
```

### 4. 更新训练计划

- **Endpoint**: `PUT /training-plans/:id`

**请求体 (JSON)**:
```json
{
  "name": "更新后的计划名称",
  "isActive": false
}
```

**成功响应 (200 OK)**:
```json
{
  "success": true,
  "data": {
    "id": "plan-uuid-1",
    "name": "更新后的计划名称",
    "isActive": false,
    // ...
  },
  "message": "Training plan updated successfully.",
  "timestamp": "2025-07-27T11:15:00.000Z"
}
```

### 5. 删除训练计划

- **Endpoint**: `DELETE /training-plans/:id`

**成功响应 (200 OK)**:
```json
{
  "success": true,
  "data": null,
  "message": "Training plan deleted successfully.",
  "timestamp": "2025-07-27T11:20:00.000Z"
}
```

---

## 📹 视频文件管理

### 异步上传视频

- **Endpoint**: `POST /videos/upload`
- **描述**: 这是一个异步接口。客户端上传文件后，服务端会立即返回`202 Accepted`响应，表示文件已接收并排队等待处理。前端需要轮询视频状态或通过WebSocket接收处理完成的通知。
- **请求类型**: `multipart/form-data`
  - `video`: 视频文件本身
  - `title` (string): 视频标题
  - `description` (string, optional): 视频描述

**成功接收响应 (202 Accepted)**:
```json
{
  "success": true,
  "data": {
    "videoId": "new-video-uuid",
    "status": "queued",
    "message": "Video upload accepted and is being processed."
  },
  "message": "Video processing started.",
  "timestamp": "2025-07-27T12:00:00.000Z"
}
```
---

## ⚙️ 通用API约定

### 统一响应格式
所有API都遵循统一的成功响应格式：
```typescript
interface ApiResponse<T> {
  success: true;
  data: T;
  message: string;
  timestamp: string;
  pagination?: { /* ... */ };
}
```

### 统一错误格式
所有API都遵循统一的错误响应格式：
```typescript
interface ApiErrorResponse {
  success: false;
  error: {
    code: string; // e.g., 'UNAUTHORIZED', 'NOT_FOUND', 'VALIDATION_ERROR'
    message: string;
    details?: any; // For validation errors
  };
  timestamp: string;
}
```
**HTTP状态码**:
- `200`: OK
- `201`: Created
- `202`: Accepted (用于异步任务)
- `400`: Bad Request (通常是验证错误)
- `401`: Unauthorized (Token无效或缺失)
- `403`: Forbidden (权限不足)
- `404`: Not Found (资源不存在)
- `500`: Internal Server Error 