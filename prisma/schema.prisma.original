// Shoutao App Database Schema
// Smart Glove Rehabilitation Training System

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id               String    @id @default(uuid()) @db.Uuid
  username         String    @unique @db.VarChar(50)
  email            String?   @unique @db.VarChar(100)
  phoneNumber      String?   @unique @map("phone_number") @db.VarChar(20)
  passwordHash     String    @map("password_hash") @db.VarChar(255)
  fullName         String?   @map("full_name") @db.VarChar(100)
  age              Int?
  gender           String?   @db.VarChar(10)
  recoveryPhase    String?   @map("recovery_phase") @db.VarChar(50)
  recoveryProgress Decimal   @default(0.000) @map("recovery_progress") @db.Decimal(4, 3)
  avatarUrl        String?   @map("avatar_url") @db.Var<PERSON>har(255)
  isActive         Boolean   @default(true) @map("is_active")
  lastLoginAt      DateTime? @map("last_login_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  // Relations
  tokens              UserToken[]
  devices             UserDevice[]
  rehabilitationPlans RehabilitationPlan[] @relation("UserPlans")
  createdPlans        RehabilitationPlan[] @relation("CreatedPlans")
  approvedPlans       RehabilitationPlan[] @relation("ApprovedPlans")
  trainingSessions    TrainingSession[]
  gameRecords         GameRecord[]
  posts               Post[]
  comments            Comment[]
  likes               Like[]
  favorites           Favorite[]
  healthAssessments   HealthAssessment[]

  @@map("users")
}

model UserToken {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String?   @map("user_id") @db.Uuid
  tokenType String    @map("token_type") @db.VarChar(20)
  tokenHash String    @map("token_hash") @db.VarChar(255)
  expiresAt DateTime  @map("expires_at")
  usedAt    DateTime? @map("used_at")
  createdAt DateTime  @default(now()) @map("created_at")

  // SMS verification specific fields
  phoneNumber String?  @map("phone_number") @db.VarChar(20)
  purpose     String?  @map("purpose") @db.VarChar(20) // 'register', 'login', 'reset_password'
  attempts    Int      @default(0) @map("attempts")
  ipAddress   String?  @map("ip_address") @db.VarChar(45)
  userAgent   String?  @map("user_agent") @db.VarChar(500)

  user User? @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_tokens")
}

model UserDevice {
  id               String    @id @default(uuid()) @db.Uuid
  userId           String    @map("user_id") @db.Uuid
  deviceMac        String    @unique @map("device_mac") @db.VarChar(17)
  deviceName       String    @default("Smart Glove") @map("device_name") @db.VarChar(100)
  deviceType       String    @default("smart_glove") @map("device_type") @db.VarChar(50)
  pairingToken     String?   @map("pairing_token") @db.VarChar(255)
  lastConnectedAt  DateTime? @map("last_connected_at")
  batteryLevel     Int?      @map("battery_level") @db.SmallInt
  firmwareVersion  String?   @map("firmware_version") @db.VarChar(20)
  hardwareVersion  String?   @map("hardware_version") @db.VarChar(20)
  calibrationData  Json?     @map("calibration_data")
  deviceStatus     String    @default("offline") @map("device_status") @db.VarChar(20)
  isActive         Boolean   @default(true) @map("is_active")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  trainingSessions TrainingSession[]

  @@map("user_devices")
}

// Rehabilitation and Training
model RehabilitationPlan {
  id                   String    @id @default(uuid()) @db.Uuid
  userId               String    @map("user_id") @db.Uuid
  planName             String    @map("plan_name") @db.VarChar(100)
  recoveryStage        String    @map("recovery_stage") @db.VarChar(50)
  targetForceRange     Json      @map("target_force_range")
  trainingDurationMins Int       @default(30) @map("training_duration_minutes")
  sessionsPerWeek      Int       @default(5) @map("sessions_per_week")
  difficultyLevel      Int       @default(1) @map("difficulty_level") @db.SmallInt
  difficultyParams     Json?     @map("difficulty_params")
  createdById          String?   @map("created_by") @db.Uuid
  approvedById         String?   @map("approved_by") @db.Uuid
  startDate            DateTime  @map("start_date") @db.Date
  endDate              DateTime? @map("end_date") @db.Date
  isActive             Boolean   @default(true) @map("is_active")
  notes                String?
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")

  user             User              @relation("UserPlans", fields: [userId], references: [id], onDelete: Cascade)
  createdBy        User?             @relation("CreatedPlans", fields: [createdById], references: [id])
  approvedBy       User?             @relation("ApprovedPlans", fields: [approvedById], references: [id])
  trainingSessions TrainingSession[]

  @@map("rehabilitation_plans")
}

model TrainingSession {
  id                     String    @id @default(uuid()) @db.Uuid
  userId                 String    @map("user_id") @db.Uuid
  deviceId               String?   @map("device_id") @db.Uuid
  rehabilitationPlanId   String?   @map("rehabilitation_plan_id") @db.Uuid
  sessionType            String    @map("session_type") @db.VarChar(50)
  sessionName            String?   @map("session_name") @db.VarChar(100)
  startTime              DateTime  @map("start_time")
  endTime                DateTime? @map("end_time")
  plannedDurationSeconds Int?      @map("planned_duration_seconds")
  actualDurationSeconds  Int?      @map("actual_duration_seconds")
  targetActions          Int?      @map("target_actions")
  completedActions       Int       @default(0) @map("completed_actions")
  targetAccuracy         Decimal?  @map("target_accuracy") @db.Decimal(4, 3)
  achievedAccuracy       Decimal?  @map("achieved_accuracy") @db.Decimal(4, 3)
  averageGripStrength    Decimal?  @map("average_grip_strength") @db.Decimal(6, 3)
  maxGripStrength        Decimal?  @map("max_grip_strength") @db.Decimal(6, 3)
  minGripStrength        Decimal?  @map("min_grip_strength") @db.Decimal(6, 3)
  totalScore             Int       @default(0) @map("total_score")
  sessionStatus          String    @default("active") @map("session_status") @db.VarChar(20)
  endReason              String?   @map("end_reason") @db.VarChar(50)
  sessionData            Json?     @map("session_data")
  notes                  String?
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")

  user               User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  device             UserDevice?           @relation(fields: [deviceId], references: [id])
  rehabilitationPlan RehabilitationPlan?   @relation(fields: [rehabilitationPlanId], references: [id])
  dataPoints         TrainingDataPoint[]
  gameRecords        GameRecord[]

  @@map("training_sessions")
}

model TrainingDataPoint {
  id             BigInt   @id @default(autoincrement())
  sessionId      String   @map("session_id") @db.Uuid
  timestamp      DateTime
  sequenceNumber Int      @map("sequence_number")
  gripStrength   Decimal? @map("grip_strength") @db.Decimal(6, 3)
  fingerPositions Json?   @map("finger_positions")
  handPose       Json?    @map("hand_pose")
  accuracy       Decimal? @db.Decimal(4, 3)
  actionType     String?  @map("action_type") @db.VarChar(50)
  gestureType    String?  @map("gesture_type") @db.VarChar(50)
  confidence     Decimal? @db.Decimal(4, 3)
  sensorData     Json?    @map("sensor_data")
  processedData  Json?    @map("processed_data")
  qualityScore   Decimal? @map("quality_score") @db.Decimal(4, 3)
  anomalyFlags   Json?    @map("anomaly_flags")
  createdAt      DateTime @default(now()) @map("created_at")

  session TrainingSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("training_data_points")
}

// Game System
model GameRecord {
  id                  String    @id @default(uuid()) @db.Uuid
  userId              String    @map("user_id") @db.Uuid
  sessionId           String?   @map("session_id") @db.Uuid
  gameType            String    @map("game_type") @db.VarChar(50)
  level               Int       @default(1)
  score               Int       @default(0)
  fruitsCollected     Int       @default(0) @map("fruits_collected")
  targetFruits        Int       @default(0) @map("target_fruits")
  averageGripStrength Decimal?  @map("average_grip_strength") @db.Decimal(6, 3)
  maxGripStrength     Decimal?  @map("max_grip_strength") @db.Decimal(6, 3)
  minGripStrength     Decimal?  @map("min_grip_strength") @db.Decimal(6, 3)
  accuracy            Decimal?  @db.Decimal(4, 3)
  playTimeSeconds     Int?      @map("play_time_seconds")
  comboCount          Int       @default(0) @map("combo_count")
  perfectPicks        Int       @default(0) @map("perfect_picks")
  missedFruits        Int       @default(0) @map("missed_fruits")
  penaltyScore        Int       @default(0) @map("penalty_score")
  endReason           String?   @map("end_reason") @db.VarChar(50)
  levelConfig         Json?     @map("level_config")
  metadata            Json?
  playedAt            DateTime  @default(now()) @map("played_at")
  createdAt           DateTime  @default(now()) @map("created_at")

  user    User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  session TrainingSession? @relation(fields: [sessionId], references: [id])
  actions GameAction[]

  @@map("game_records")
}

model GameAction {
  id             BigInt   @id @default(autoincrement())
  gameRecordId   String   @map("game_record_id") @db.Uuid
  timestamp      DateTime
  sequenceNumber Int      @map("sequence_number")
  actionType     String   @map("action_type") @db.VarChar(50)
  gripStrength   Decimal? @map("grip_strength") @db.Decimal(6, 3)
  accuracy       Decimal? @db.Decimal(4, 3)
  score          Int      @default(0)
  fruitType      String?  @map("fruit_type") @db.VarChar(50)
  positionX      Decimal? @map("position_x") @db.Decimal(6, 3)
  positionY      Decimal? @map("position_y") @db.Decimal(6, 3)
  gestureData    Json?    @map("gesture_data")
  sensorData     Json?    @map("sensor_data")
  createdAt      DateTime @default(now()) @map("created_at")

  gameRecord GameRecord @relation(fields: [gameRecordId], references: [id], onDelete: Cascade)

  @@map("game_actions")
}

// Community System
model Post {
  id               String    @id @default(uuid()) @db.Uuid
  authorId         String    @map("author_id") @db.Uuid
  title            String?   @db.VarChar(200)
  content          String
  postType         String    @default("forum") @map("post_type") @db.VarChar(50)
  category         String?   @db.VarChar(50)
  tags             Json?
  images           Json?
  likesCount       Int       @default(0) @map("likes_count")
  commentsCount    Int       @default(0) @map("comments_count")
  viewsCount       Int       @default(0) @map("views_count")
  isPinned         Boolean   @default(false) @map("is_pinned")
  isFeatured       Boolean   @default(false) @map("is_featured")
  status           String    @default("published") @db.VarChar(20)
  moderationStatus String    @default("approved") @map("moderation_status") @db.VarChar(20)
  metadata         Json?
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  author    User        @relation(fields: [authorId], references: [id], onDelete: Cascade)
  comments  Comment[]
  likes     Like[]      @relation("PostLikes")
  favorites Favorite[]

  @@map("posts")
}

model Comment {
  id               String    @id @default(uuid()) @db.Uuid
  postId           String    @map("post_id") @db.Uuid
  authorId         String    @map("author_id") @db.Uuid
  parentCommentId  String?   @map("parent_comment_id") @db.Uuid
  content          String
  likesCount       Int       @default(0) @map("likes_count")
  repliesCount     Int       @default(0) @map("replies_count")
  status           String    @default("published") @db.VarChar(20)
  moderationStatus String    @default("approved") @map("moderation_status") @db.VarChar(20)
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  post          Post      @relation(fields: [postId], references: [id], onDelete: Cascade)
  author        User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  parentComment Comment?  @relation("CommentReplies", fields: [parentCommentId], references: [id], onDelete: Cascade)
  replies       Comment[] @relation("CommentReplies")
  likes         Like[]    @relation("CommentLikes")

  @@map("comments")
}

model Like {
  id         String   @id @default(uuid()) @db.Uuid
  userId     String   @map("user_id") @db.Uuid
  postId     String?  @map("post_id") @db.Uuid
  commentId  String?  @map("comment_id") @db.Uuid
  targetType String   @map("target_type") @db.VarChar(20)
  createdAt  DateTime @default(now()) @map("created_at")

  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  post    Post?    @relation("PostLikes", fields: [postId], references: [id], onDelete: Cascade)
  comment Comment? @relation("CommentLikes", fields: [commentId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@unique([userId, commentId])
  @@map("likes")
}

model Favorite {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  postId    String   @map("post_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@map("favorites")
}

// Health Assessment System
model HealthAssessment {
  id                String    @id @default(uuid()) @db.Uuid
  userId            String    @map("user_id") @db.Uuid
  templateId        String?   @map("template_id") @db.Uuid
  assessmentType    String    @map("assessment_type") @db.VarChar(50)
  assessmentName    String    @map("assessment_name") @db.VarChar(100)
  description       String?
  status            String    @default("pending") @db.VarChar(20)
  startTime         DateTime? @map("start_time")
  endTime           DateTime? @map("end_time")
  durationSeconds   Int?      @map("duration_seconds")
  overallScore      Decimal?  @map("overall_score") @db.Decimal(5, 2)
  maxPossibleScore  Decimal   @default(100.00) @map("max_possible_score") @db.Decimal(5, 2)
  completionRate    Decimal?  @map("completion_rate") @db.Decimal(4, 3)
  assessmentData    Json?     @map("assessment_data")
  recommendations   Json?
  notes             String?
  conductedBy       String?   @map("conducted_by") @db.Uuid
  reviewedBy        String?   @map("reviewed_by") @db.Uuid
  reviewedAt        DateTime? @map("reviewed_at")
  isActive          Boolean   @default(true) @map("is_active")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  user              User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  template          AssessmentTemplate?     @relation(fields: [templateId], references: [id])
  results           AssessmentResult[]
  reports           AssessmentReport[]

  @@map("health_assessments")
}

model AssessmentTemplate {
  id                String    @id @default(uuid()) @db.Uuid
  templateName      String    @map("template_name") @db.VarChar(100)
  templateType      String    @map("template_type") @db.VarChar(50)
  description       String?
  targetCondition   String?   @map("target_condition") @db.VarChar(100)
  difficultyLevel   Int       @default(1) @map("difficulty_level") @db.SmallInt
  estimatedDuration Int       @map("estimated_duration") // in seconds
  maxScore          Decimal   @default(100.00) @map("max_score") @db.Decimal(5, 2)
  assessmentConfig  Json      @map("assessment_config")
  scoringRules      Json      @map("scoring_rules")
  isActive          Boolean   @default(true) @map("is_active")
  version           String    @default("1.0") @db.VarChar(10)
  createdBy         String?   @map("created_by") @db.Uuid
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  assessments       HealthAssessment[]

  @@map("assessment_templates")
}

model AssessmentResult {
  id                String    @id @default(uuid()) @db.Uuid
  assessmentId      String    @map("assessment_id") @db.Uuid
  taskName          String    @map("task_name") @db.VarChar(100)
  taskType          String    @map("task_type") @db.VarChar(50)
  taskOrder         Int       @map("task_order")
  startTime         DateTime  @map("start_time")
  endTime           DateTime? @map("end_time")
  durationSeconds   Int?      @map("duration_seconds")
  score             Decimal   @default(0.00) @db.Decimal(5, 2)
  maxScore          Decimal   @map("max_score") @db.Decimal(5, 2)
  accuracy          Decimal?  @db.Decimal(4, 3)
  completionStatus  String    @map("completion_status") @db.VarChar(20)
  performanceData   Json?     @map("performance_data")
  gripStrengthData  Json?     @map("grip_strength_data")
  movementData      Json?     @map("movement_data")
  errorAnalysis     Json?     @map("error_analysis")
  notes             String?
  createdAt         DateTime  @default(now()) @map("created_at")

  assessment        HealthAssessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  @@map("assessment_results")
}

model AssessmentReport {
  id                String    @id @default(uuid()) @db.Uuid
  assessmentId      String    @map("assessment_id") @db.Uuid
  reportType        String    @map("report_type") @db.VarChar(50)
  title             String    @db.VarChar(200)
  summary           String
  detailedAnalysis  String    @map("detailed_analysis")
  recommendations   Json
  progressComparison Json?   @map("progress_comparison")
  visualData        Json?     @map("visual_data")
  reportData        Json      @map("report_data")
  generatedBy       String?   @map("generated_by") @db.Uuid
  generatedAt       DateTime  @default(now()) @map("generated_at")
  isPublic          Boolean   @default(false) @map("is_public")
  createdAt         DateTime  @default(now()) @map("created_at")

  assessment        HealthAssessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  @@map("assessment_reports")
}
