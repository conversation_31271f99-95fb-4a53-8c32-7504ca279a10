// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ==============================================================================
// Main Application Models
// ==============================================================================

model User {
  id                      String                 @id @default(uuid()) @db.Uuid
  username                String?                @unique @db.VarChar(50)
  passwordHash            String?                @map("password_hash") @db.VarChar(255)
  phoneNumber             String                 @unique @map("phone_number") @db.VarChar(20)
  email                   String?                @unique @db.VarChar(100)
  fullName                String?                @map("full_name") @db.VarChar(100)
  nickname                String?                @db.VarChar(50)
  avatarUrl               String?                @map("avatar_url") @db.VarChar(255)
  gender                  String?                @db.VarChar(10) // male, female, other
  birthDate               DateTime?              @map("birth_date") @db.Date
  status                  String                 @default("active") @db.VarChar(20) // active, suspended, deleted
  isActive                Boolean                @default(true) @map("is_active")
  lastLoginAt             DateTime?              @map("last_login_at")
  createdAt               DateTime               @default(now()) @map("created_at")
  updatedAt               DateTime               @updatedAt @map("updated_at")
  // Relations
  smsVerifications        SmsVerification[]
  tokens                  UserToken[]
  rehabilitationRecords   RehabilitationRecord[]

  @@map("users")
}

model SmsVerification {
  id          String   @id @default(uuid()) @db.Uuid
  userId      String   @map("user_id") @db.Uuid
  phoneNumber String   @map("phone_number") @db.VarChar(20)
  code        String   @db.VarChar(10)
  purpose     String   @db.VarChar(50) // e.g., 'login', 'reset-password', 'register'
  isUsed      Boolean  @default(false) @map("is_used")
  expiresAt   DateTime @map("expires_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([phoneNumber, purpose])
  @@map("sms_verifications")
}

model UserToken {
  id           String   @id @default(uuid()) @db.Uuid
  userId       String   @map("user_id") @db.Uuid
  token        String   @unique @db.Text
  tokenHash    String?  @map("token_hash") @db.Text
  type         String   @db.VarChar(20) // refresh_token
  tokenType    String?  @map("token_type") @db.VarChar(20)
  ipAddress    String?  @map("ip_address") @db.VarChar(45)
  userAgent    String?  @map("user_agent") @db.Text
  expiresAt    DateTime @map("expires_at")
  isUsed       Boolean  @default(false) @map("is_used")
  usedAt       DateTime? @map("used_at")
  createdAt    DateTime @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_tokens")
}

model RehabilitationRecord {
  id              String   @id @default(uuid()) @db.Uuid
  userId          String   @map("user_id") @db.Uuid
  planId          String   @map("plan_id") @db.Uuid
  planName        String   @map("plan_name") @db.VarChar(100)
  completedAt     DateTime @map("completed_at")
  durationSeconds Int      @map("duration_seconds")
  satisfaction    Int?     // e.g., 1-5 rating
  notes           String?  @db.Text
  createdAt       DateTime @default(now()) @map("created_at")

  user User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan TrainingPlanAdmin @relation(fields: [planId], references: [id], onDelete: Restrict)

  @@map("rehabilitation_records")
}

// ==============================================================================
// Admin System Models
// ==============================================================================

// Admin Management System
model AdminUser {
  id           String    @id @default(uuid()) @db.Uuid
  username     String    @unique @db.VarChar(50)
  passwordHash String    @map("password_hash") @db.VarChar(255)
  email        String?   @db.VarChar(100)
  fullName     String?   @map("full_name") @db.VarChar(100)
  role         String    @default("admin") @db.VarChar(20) // super_admin, admin, editor
  permissions  Json?     // Array of permission strings
  isActive     Boolean   @default(true) @map("is_active")
  lastLoginAt  DateTime? @map("last_login_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  tokens                AdminToken[]
  createdTrainingPlans  TrainingPlanAdmin[] @relation("CreatedByAdmin")
  updatedTrainingPlans  TrainingPlanAdmin[] @relation("UpdatedByAdmin")
  createdActionPoints   TrainingActionPoint[]  @relation("CreatedByAdmin")
  updatedActionPoints   TrainingActionPoint[]  @relation("UpdatedByAdmin")
  uploadedVideos        TrainingVideo[]     @relation("UploadedByAdmin")
  updatedVideos         TrainingVideo[]     @relation("UpdatedByAdmin")
  adminLogs             AdminLog[]

  @@map("admin_users")
}

model AdminToken {
  id        String   @id @default(uuid()) @db.Uuid
  adminId   String   @map("admin_id") @db.Uuid
  token     String   @unique @db.Text
  tokenHash String?  @map("token_hash") @db.Text
  type      String   @db.VarChar(20) // e.g., 'refresh_token'
  ipAddress String?  @map("ip_address") @db.VarChar(45)
  userAgent String?  @map("user_agent") @db.Text
  expiresAt DateTime @map("expires_at")
  isUsed    Boolean  @default(false) @map("is_used")
  usedAt    DateTime? @map("used_at")
  createdAt DateTime @default(now()) @map("created_at")

  admin AdminUser @relation(fields: [adminId], references: [id], onDelete: Cascade)

  @@map("admin_tokens")
}

model AdminLog {
  id        String   @id @default(uuid()) @db.Uuid
  adminId   String   @map("admin_id") @db.Uuid
  action    String   @db.VarChar(255) // e.g., 'login', 'create_user', 'delete_video'
  targetId  String?  @map("target_id") @db.VarChar(50) // ID of the affected resource
  details   Json?
  ipAddress String?  @map("ip_address") @db.VarChar(45)
  createdAt DateTime @default(now()) @map("created_at")

  admin AdminUser @relation(fields: [adminId], references: [id], onDelete: Cascade)

  @@map("admin_logs")
}

// ==============================================================================
// Content Models (Managed by Admin)
// ==============================================================================

// This is the main "rehabilitation package" that groups a video and action points.
model TrainingPlanAdmin {
  id              String   @id @default(uuid()) @db.Uuid
  name            String   @db.VarChar(100)
  title           String   @db.VarChar(100)
  description     String?  @db.Text
  category        String   @db.VarChar(50)
  difficultyLevel String?  @db.VarChar(20) // beginner, intermediate, advanced
  durationMinutes Int?     @map("duration_minutes")
  tags            String[]
  isActive        Boolean  @default(false) @map("is_active")
  sortOrder       Int?     @map("sort_order")
  createdBy       String   @map("created_by") @db.Uuid
  updatedBy       String?  @map("updated_by") @db.Uuid
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  creator               AdminUser              @relation("CreatedByAdmin", fields: [createdBy], references: [id])
  updater               AdminUser?             @relation("UpdatedByAdmin", fields: [updatedBy], references: [id])
  video                 TrainingVideo?         @relation(fields: [videoId], references: [id])
  actionPoints          TrainingActionPoint[]
  rehabilitationRecords RehabilitationRecord[]

  videoId String? @unique @map("video_id") @db.Uuid

  @@map("training_plans")
}

// Renamed from ActionPointAdmin
model TrainingActionPoint {
  id          String   @id @default(uuid()) @db.Uuid
  planId      String   @map("plan_id") @db.Uuid
  order       Int      // The display order, e.g., 1, 2, 3
  description String
  createdBy   String   @map("created_by") @db.Uuid
  updatedBy   String?  @map("updated_by") @db.Uuid
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  plan        TrainingPlanAdmin @relation(fields: [planId], references: [id], onDelete: Cascade)
  creator     AdminUser         @relation("CreatedByAdmin", fields: [createdBy], references: [id])
  updater     AdminUser?        @relation("UpdatedByAdmin", fields: [updatedBy], references: [id])

  @@map("training_action_points")
}

// Represents a video file used in training
model TrainingVideo {
  id             String    @id @default(uuid()) @db.Uuid
  title          String    @db.VarChar(100)
  fileName       String    @map("file_name") @db.VarChar(255)
  filePath       String    @map("file_path") @db.VarChar(255)
  fileType       String    @map("file_type") @db.VarChar(50)
  fileSize       Int       @map("file_size") // in bytes
  duration       Float?    // in seconds
  thumbnailPath  String?   @map("thumbnail_path") @db.VarChar(255)
  status         String    @default("pending") @db.VarChar(20) // pending, processing, ready, error
  uploadedBy     String    @map("uploaded_by") @db.Uuid
  updatedBy      String?   @map("updated_by") @db.Uuid
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  // Relations
  uploader       AdminUser @relation("UploadedByAdmin", fields: [uploadedBy], references: [id])
  updater        AdminUser? @relation("UpdatedByAdmin", fields: [updatedBy], references: [id])
  trainingPlan   TrainingPlanAdmin?

  @@map("training_videos")
}
