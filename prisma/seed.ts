import { PrismaClient } from '@prisma/client';
import { passwordService } from '../src/utils/password';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

async function main() {
  logger.info('Start seeding...');

  const superAdminUsername = 'superadmin';
  const superAdminPassword = 'SuperAdmin@2024';

  // Check if the superadmin already exists
  const existingAdmin = await prisma.adminUser.findUnique({
    where: {
      username: superAdminUsername,
    },
  });

  if (existingAdmin) {
    logger.info(`Admin user '${superAdminUsername}' already exists. Skipping creation.`);
  } else {
    const hashedPassword = await passwordService.hashPassword(superAdminPassword);

    await prisma.adminUser.create({
      data: {
        username: superAdminUsername,
        passwordHash: hashedPassword,
        role: 'super_admin',
        permissions: ['read', 'write', 'delete', 'manage_users', 'system_settings'],
        isActive: true,
      },
    });
    logger.info(`Successfully created super admin: ${superAdminUsername}`);
    logger.info(`Password: ${superAdminPassword}`);
  }

  logger.info('Seeding finished.');
}

main()
  .catch((e) => {
    logger.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 