"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
process.env['NODE_ENV'] = 'test';
process.env['JWT_SECRET'] = 'test-jwt-secret-for-testing-only';
process.env['ENCRYPTION_KEY'] = 'test-encryption-key-for-testing';
process.env['DATABASE_URL'] = 'postgresql://postgres:<EMAIL>:5432/shoutao_test';
beforeAll(async () => {
    const prisma = new client_1.PrismaClient();
    try {
        await prisma.$executeRaw `CREATE DATABASE IF NOT EXISTS shoutao_test`;
    }
    catch (error) {
    }
    await prisma.$disconnect();
});
afterAll(async () => {
});
jest.setTimeout(30000);
//# sourceMappingURL=setup.js.map