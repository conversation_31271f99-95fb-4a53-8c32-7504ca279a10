import { PrismaClient } from '@prisma/client';

// Set test environment
process.env['NODE_ENV'] = 'test';
process.env['JWT_SECRET'] = 'test-jwt-secret-for-testing-only';
process.env['ENCRYPTION_KEY'] = 'test-encryption-key-for-testing';
process.env['DATABASE_URL'] = 'postgresql://postgres:<EMAIL>:5432/shoutao_test';

// Global test setup
beforeAll(async () => {
  // Setup test database
  const prisma = new PrismaClient();
  
  try {
    // Create test database if it doesn't exist
    await prisma.$executeRaw`CREATE DATABASE IF NOT EXISTS shoutao_test`;
  } catch (error) {
    // Database might already exist, ignore error
  }
  
  await prisma.$disconnect();
});

// Global test teardown
afterAll(async () => {
  // Clean up any global resources
});

// Increase timeout for database operations
jest.setTimeout(30000);
