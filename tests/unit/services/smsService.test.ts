import { smsService } from '../../../src/services/smsService';
import { PrismaClient } from '@prisma/client';

// Mock Prisma
jest.mock('@prisma/client');
const mockPrisma = {
  userToken: {
    create: jest.fn(),
    findFirst: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
    deleteMany: jest.fn(),
  },
  user: {
    findUnique: jest.fn(),
  },
} as any;

// Mock password service
jest.mock('../../../src/utils/password', () => ({
  passwordService: {
    hashPassword: jest.fn().mockResolvedValue('hashed-code'),
    verifyPassword: jest.fn(),
  },
}));

// Mock logger
jest.mock('../../../src/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
  logSecurity: jest.fn(),
}));

describe('SmsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (PrismaClient as jest.Mock).mockImplementation(() => mockPrisma);
  });

  describe('sendVerificationCode', () => {
    it('should send verification code successfully', async () => {
      mockPrisma.userToken.count.mockResolvedValue(0); // No rate limiting
      mockPrisma.userToken.findFirst.mockResolvedValue(null); // No recent code
      mockPrisma.userToken.create.mockResolvedValue({
        id: 'token-id',
        phoneNumber: '+8613800138000',
      });

      const result = await smsService.sendVerificationCode({
        phoneNumber: '+8613800138000',
        purpose: 'register',
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
      });

      expect(result.sent).toBe(true);
      expect(result.expiresIn).toBe(300); // 5 minutes
      expect(result.retryAfter).toBe(60); // 1 minute
      expect(mockPrisma.userToken.create).toHaveBeenCalled();
    });

    it('should enforce rate limiting by phone number', async () => {
      mockPrisma.userToken.count.mockResolvedValue(5); // Max codes reached

      await expect(
        smsService.sendVerificationCode({
          phoneNumber: '+8613800138000',
          purpose: 'register',
        })
      ).rejects.toThrow('Too many SMS requests');
    });

    it('should enforce rate limiting by IP address', async () => {
      mockPrisma.userToken.count
        .mockResolvedValueOnce(0) // Phone number check passes
        .mockResolvedValueOnce(16); // IP address check fails (5*3+1)

      await expect(
        smsService.sendVerificationCode({
          phoneNumber: '+8613800138000',
          purpose: 'register',
          ipAddress: '127.0.0.1',
        })
      ).rejects.toThrow('Too many SMS requests from this IP address');
    });

    it('should prevent sending code too frequently', async () => {
      mockPrisma.userToken.count.mockResolvedValue(0);
      mockPrisma.userToken.findFirst.mockResolvedValue({
        id: 'recent-token',
        createdAt: new Date(),
      });

      await expect(
        smsService.sendVerificationCode({
          phoneNumber: '+8613800138000',
          purpose: 'register',
        })
      ).rejects.toThrow('Please wait 60 seconds');
    });
  });

  describe('verifyCode', () => {
    it('should verify code successfully', async () => {
      const mockToken = {
        id: 'token-id',
        tokenHash: 'hashed-code',
        attempts: 0,
        expiresAt: new Date(Date.now() + 300000), // 5 minutes from now
      };

      mockPrisma.userToken.findFirst.mockResolvedValue(mockToken);
      mockPrisma.userToken.update.mockResolvedValue(mockToken);

      const { passwordService } = require('../../../src/utils/password');
      passwordService.verifyPassword.mockResolvedValue(true);

      const result = await smsService.verifyCode({
        phoneNumber: '+8613800138000',
        code: '123456',
        purpose: 'register',
      });

      expect(result).toBe(true);
      expect(mockPrisma.userToken.update).toHaveBeenCalledWith({
        where: { id: 'token-id' },
        data: {
          attempts: 1,
          usedAt: expect.any(Date),
        },
      });
    });

    it('should reject invalid code', async () => {
      const mockToken = {
        id: 'token-id',
        tokenHash: 'hashed-code',
        attempts: 0,
        expiresAt: new Date(Date.now() + 300000),
      };

      mockPrisma.userToken.findFirst.mockResolvedValue(mockToken);
      mockPrisma.userToken.update.mockResolvedValue(mockToken);

      const { passwordService } = require('../../../src/utils/password');
      passwordService.verifyPassword.mockResolvedValue(false);

      await expect(
        smsService.verifyCode({
          phoneNumber: '+8613800138000',
          code: '123456',
          purpose: 'register',
        })
      ).rejects.toThrow('Invalid verification code');
    });

    it('should reject when no valid code exists', async () => {
      mockPrisma.userToken.findFirst.mockResolvedValue(null);

      await expect(
        smsService.verifyCode({
          phoneNumber: '+8613800138000',
          code: '123456',
          purpose: 'register',
        })
      ).rejects.toThrow('Invalid or expired verification code');
    });

    it('should reject when max attempts exceeded', async () => {
      const mockToken = {
        id: 'token-id',
        tokenHash: 'hashed-code',
        attempts: 3, // Max attempts reached
        expiresAt: new Date(Date.now() + 300000),
      };

      mockPrisma.userToken.findFirst.mockResolvedValue(mockToken);

      await expect(
        smsService.verifyCode({
          phoneNumber: '+8613800138000',
          code: '123456',
          purpose: 'register',
        })
      ).rejects.toThrow('Maximum verification attempts exceeded');
    });
  });

  describe('isPhoneRegistered', () => {
    it('should return true for registered phone', async () => {
      mockPrisma.user.findUnique.mockResolvedValue({ id: 'user-id' });

      const result = await smsService.isPhoneRegistered('+8613800138000');
      expect(result).toBe(true);
    });

    it('should return false for unregistered phone', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const result = await smsService.isPhoneRegistered('+8613800138000');
      expect(result).toBe(false);
    });
  });

  describe('cleanupExpiredCodes', () => {
    it('should clean up expired codes', async () => {
      mockPrisma.userToken.deleteMany.mockResolvedValue({ count: 5 });

      await smsService.cleanupExpiredCodes();

      expect(mockPrisma.userToken.deleteMany).toHaveBeenCalledWith({
        where: {
          tokenType: 'sms_verification',
          expiresAt: {
            lt: expect.any(Date),
          },
        },
      });
    });
  });
});
