import { passwordService } from '@/utils/password';

describe('PasswordService', () => {
  describe('hashPassword', () => {
    it('should hash a password successfully', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await passwordService.hashPassword(password);
      
      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(50);
    });

    it('should throw error for short password', async () => {
      const shortPassword = '123';
      
      await expect(passwordService.hashPassword(shortPassword))
        .rejects.toThrow('Password must be at least 6 characters long');
    });

    it('should throw error for empty password', async () => {
      await expect(passwordService.hashPassword(''))
        .rejects.toThrow('Password must be at least 6 characters long');
    });
  });

  describe('verifyPassword', () => {
    it('should verify correct password', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await passwordService.hashPassword(password);
      
      const isValid = await passwordService.verifyPassword(password, hashedPassword);
      expect(isValid).toBe(true);
    });

    it('should reject incorrect password', async () => {
      const password = 'TestPassword123!';
      const wrongPassword = 'WrongPassword123!';
      const hashedPassword = await passwordService.hashPassword(password);
      
      const isValid = await passwordService.verifyPassword(wrongPassword, hashedPassword);
      expect(isValid).toBe(false);
    });

    it('should return false for empty password', async () => {
      const hashedPassword = await passwordService.hashPassword('TestPassword123!');
      
      const isValid = await passwordService.verifyPassword('', hashedPassword);
      expect(isValid).toBe(false);
    });

    it('should return false for empty hash', async () => {
      const isValid = await passwordService.verifyPassword('TestPassword123!', '');
      expect(isValid).toBe(false);
    });
  });

  describe('validatePasswordStrength', () => {
    it('should validate strong password', () => {
      const strongPassword = 'StrongPass123!@#';
      const result = passwordService.validatePasswordStrength(strongPassword);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.score).toBeGreaterThan(4);
    });

    it('should reject weak password', () => {
      const weakPassword = 'weak';
      const result = passwordService.validatePasswordStrength(weakPassword);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.score).toBeLessThan(3);
    });

    it('should reject common patterns', () => {
      const commonPassword = 'password123';
      const result = passwordService.validatePasswordStrength(commonPassword);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password contains common patterns and is not secure');
    });
  });

  describe('generateRandomPassword', () => {
    it('should generate password with default length', () => {
      const password = passwordService.generateRandomPassword();
      
      expect(password).toBeDefined();
      expect(password.length).toBe(12);
    });

    it('should generate password with custom length', () => {
      const customLength = 16;
      const password = passwordService.generateRandomPassword(customLength);
      
      expect(password.length).toBe(customLength);
    });

    it('should generate different passwords each time', () => {
      const password1 = passwordService.generateRandomPassword();
      const password2 = passwordService.generateRandomPassword();
      
      expect(password1).not.toBe(password2);
    });
  });

  describe('generateTemporaryPassword', () => {
    it('should generate secure temporary password', () => {
      const tempPassword = passwordService.generateTemporaryPassword();
      
      expect(tempPassword).toBeDefined();
      expect(tempPassword.length).toBe(12);
      
      // Validate the generated password meets security requirements
      const validation = passwordService.validatePasswordStrength(tempPassword);
      expect(validation.isValid).toBe(true);
    });

    it('should generate different temporary passwords', () => {
      const temp1 = passwordService.generateTemporaryPassword();
      const temp2 = passwordService.generateTemporaryPassword();
      
      expect(temp1).not.toBe(temp2);
    });
  });
});
