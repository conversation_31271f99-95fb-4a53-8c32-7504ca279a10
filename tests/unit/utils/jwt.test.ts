import { jwtService } from '@/utils/jwt';

describe('JwtService', () => {
  const mockPayload = {
    sub: 'user-123',
    username: 'testuser',
    email: '<EMAIL>',
  };

  describe('generateAccessToken', () => {
    it('should generate a valid access token', () => {
      const token = jwtService.generateAccessToken(mockPayload);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT has 3 parts
    });

    it('should generate different tokens for different payloads', () => {
      const payload1 = { ...mockPayload, sub: 'user-1' };
      const payload2 = { ...mockPayload, sub: 'user-2' };
      
      const token1 = jwtService.generateAccessToken(payload1);
      const token2 = jwtService.generateAccessToken(payload2);
      
      expect(token1).not.toBe(token2);
    });
  });

  describe('generateRefreshToken', () => {
    it('should generate a valid refresh token', () => {
      const token = jwtService.generateRefreshToken(mockPayload);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3);
    });
  });

  describe('generateTokenPair', () => {
    it('should generate both access and refresh tokens', () => {
      const tokenPair = jwtService.generateTokenPair(mockPayload);
      
      expect(tokenPair.accessToken).toBeDefined();
      expect(tokenPair.refreshToken).toBeDefined();
      expect(tokenPair.expiresIn).toBeDefined();
      expect(typeof tokenPair.expiresIn).toBe('number');
    });

    it('should generate different access and refresh tokens', () => {
      const tokenPair = jwtService.generateTokenPair(mockPayload);
      
      expect(tokenPair.accessToken).not.toBe(tokenPair.refreshToken);
    });
  });

  describe('verifyAccessToken', () => {
    it('should verify valid access token', () => {
      const token = jwtService.generateAccessToken(mockPayload);
      const decoded = jwtService.verifyAccessToken(token);
      
      expect(decoded.sub).toBe(mockPayload.sub);
      expect(decoded.username).toBe(mockPayload.username);
      expect(decoded.email).toBe(mockPayload.email);
    });

    it('should throw error for invalid token', () => {
      const invalidToken = 'invalid.token.here';
      
      expect(() => jwtService.verifyAccessToken(invalidToken))
        .toThrow('Invalid access token');
    });

    it('should throw error for malformed token', () => {
      const malformedToken = 'not-a-jwt-token';
      
      expect(() => jwtService.verifyAccessToken(malformedToken))
        .toThrow('Invalid access token');
    });
  });

  describe('verifyRefreshToken', () => {
    it('should verify valid refresh token', () => {
      const token = jwtService.generateRefreshToken(mockPayload);
      const decoded = jwtService.verifyRefreshToken(token);
      
      expect(decoded.sub).toBe(mockPayload.sub);
      expect(decoded.username).toBe(mockPayload.username);
      expect(decoded.email).toBe(mockPayload.email);
    });

    it('should throw error for invalid refresh token', () => {
      const invalidToken = 'invalid.refresh.token';
      
      expect(() => jwtService.verifyRefreshToken(invalidToken))
        .toThrow('Invalid refresh token');
    });
  });

  describe('extractTokenFromHeader', () => {
    it('should extract token from valid Bearer header', () => {
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      const authHeader = `Bearer ${token}`;
      
      const extracted = jwtService.extractTokenFromHeader(authHeader);
      expect(extracted).toBe(token);
    });

    it('should return null for missing header', () => {
      const extracted = jwtService.extractTokenFromHeader(undefined);
      expect(extracted).toBeNull();
    });

    it('should return null for invalid header format', () => {
      const extracted = jwtService.extractTokenFromHeader('InvalidHeader');
      expect(extracted).toBeNull();
    });

    it('should return null for non-Bearer header', () => {
      const extracted = jwtService.extractTokenFromHeader('Basic dGVzdDp0ZXN0');
      expect(extracted).toBeNull();
    });
  });

  describe('getTokenExpiry', () => {
    it('should return expiry date for valid token', () => {
      const token = jwtService.generateAccessToken(mockPayload);
      const expiry = jwtService.getTokenExpiry(token);
      
      expect(expiry).toBeInstanceOf(Date);
      expect(expiry!.getTime()).toBeGreaterThan(Date.now());
    });

    it('should return null for invalid token', () => {
      const expiry = jwtService.getTokenExpiry('invalid.token');
      expect(expiry).toBeNull();
    });
  });
});
