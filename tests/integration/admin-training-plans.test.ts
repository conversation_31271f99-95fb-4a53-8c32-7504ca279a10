import request from 'supertest';
import express from 'express';
import { application } from '@/core/Application';
import { PrismaClient, AdminUser, TrainingPlanAdmin } from '@prisma/client';
import { passwordService } from '@/utils/password';

let app: express.Application;
const prisma = new PrismaClient();
let testAdmin: AdminUser;
let accessToken: string;
let createdPlan: TrainingPlanAdmin;

describe('Admin Training Plans API', () => {

  beforeAll(async () => {
    await application.initialize();
    app = application.getApp();

    // Create a test admin user with permissions
    const hashedPassword = await passwordService.hashPassword('PlanP@ssw0rd!');
    testAdmin = await prisma.adminUser.create({
      data: {
        username: 'testadmin_plans',
        passwordHash: hashedPassword,
        role: 'admin',
        permissions: ['read', 'write', 'delete'],
      },
    });

    // Login to get the access token
    const res = await request(app)
      .post('/api/v1/admin/auth/login')
      .send({
        username: 'testadmin_plans',
        password: 'PlanP@ssw0rd!',
      });
    accessToken = res.body.data.tokens.accessToken;
  });

  afterAll(async () => {
    // Clean up all test data
    await prisma.trainingPlanAdmin.deleteMany({ where: { createdBy: testAdmin.id } });
    await prisma.adminUser.delete({ where: { id: testAdmin.id } });
  });

  it('should create a new training plan', async () => {
    const res = await request(app)
      .post('/api/v1/admin/training-plans')
      .set('Authorization', `Bearer ${accessToken}`)
      .send({
        name: 'Test Plan 1',
        description: 'A plan for testing.',
        difficultyLevel: 'beginner',
      });

    expect(res.statusCode).toEqual(201);
    expect(res.body.success).toBe(true);
    expect(res.body.data.name).toBe('Test Plan 1');
    createdPlan = res.body.data;
  });

  it('should get a list of training plans', async () => {
    const res = await request(app)
      .get('/api/v1/admin/training-plans')
      .set('Authorization', `Bearer ${accessToken}`);

    expect(res.statusCode).toEqual(200);
    expect(res.body.success).toBe(true);
    expect(Array.isArray(res.body.data)).toBe(true);
    expect(res.body.data.length).toBeGreaterThan(0);
  });

  it('should get a single training plan by ID', async () => {
    const res = await request(app)
      .get(`/api/v1/admin/training-plans/${createdPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(res.statusCode).toEqual(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data.id).toBe(createdPlan.id);
  });

  it('should update a training plan', async () => {
    const res = await request(app)
      .put(`/api/v1/admin/training-plans/${createdPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({
        name: 'Updated Test Plan 1',
        isActive: false,
      });

    expect(res.statusCode).toEqual(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data.name).toBe('Updated Test Plan 1');
    expect(res.body.data.isActive).toBe(false);
  });

  it('should delete a training plan', async () => {
    const res = await request(app)
      .delete(`/api/v1/admin/training-plans/${createdPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(res.statusCode).toEqual(200);
    expect(res.body.success).toBe(true);

    // Verify it's actually deleted
    const getRes = await request(app)
      .get(`/api/v1/admin/training-plans/${createdPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`);
    expect(getRes.statusCode).toEqual(404);
  });
}); 