import request from 'supertest';
import express from 'express';
import { application } from '@/core/Application';
import { PrismaClient, AdminUser } from '@prisma/client';
import { passwordService } from '@/utils/password';

let app: express.Application;
const prisma = new PrismaClient();
let testAdmin: AdminUser;

describe('Admin Authentication API', () => {

  beforeAll(async () => {
    await application.initialize();
    app = application.getApp();

    // Create a test admin user
    const hashedPassword = await passwordService.hashPassword('StrongP@ssw0rd!');
    testAdmin = await prisma.adminUser.create({
      data: {
        username: 'testadmin_auth',
        passwordHash: hashedPassword,
        role: 'admin',
        permissions: ['read', 'write'],
      },
    });
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.adminUser.delete({ where: { id: testAdmin.id } });
    await prisma.adminToken.deleteMany({ where: { adminId: testAdmin.id } });
  });

  // Test Suite
  let accessToken = '';
  let refreshToken = '';

  it('should login a valid admin and return tokens', async () => {
    const res = await request(app)
      .post('/api/v1/admin/auth/login')
      .send({
        username: 'testadmin_auth',
        password: 'StrongP@ssw0rd!',
      });

    expect(res.statusCode).toEqual(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data.admin.username).toBe('testadmin_auth');
    expect(res.body.data.tokens.accessToken).toBeDefined();
    expect(res.body.data.tokens.refreshToken).toBeDefined();

    accessToken = res.body.data.tokens.accessToken;
    refreshToken = res.body.data.tokens.refreshToken;
  });

  it('should fail to login with invalid credentials', async () => {
    const res = await request(app)
      .post('/api/v1/admin/auth/login')
      .send({
        username: 'testadmin_auth',
        password: 'WrongPassword',
      });

    expect(res.statusCode).toEqual(401);
    expect(res.body.success).toBe(false);
  });

  it('should refresh tokens with a valid refresh token', async () => {
    const res = await request(app)
      .post('/api/v1/admin/auth/refresh')
      .send({ refreshToken });

    expect(res.statusCode).toEqual(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data.accessToken).toBeDefined();
    expect(res.body.data.refreshToken).toBeDefined();
    expect(res.body.data.accessToken).not.toBe(accessToken);
  });

  it('should successfully logout', async () => {
    const res = await request(app)
      .post('/api/v1/admin/auth/logout')
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ refreshToken });

    expect(res.statusCode).toEqual(200);
    expect(res.body.success).toBe(true);
  });

  it('should fail to use a refresh token after it has been used', async () => {
    // Attempt to refresh again with the same (now used) refresh token
    const res = await request(app)
      .post('/api/v1/admin/auth/refresh')
      .send({ refreshToken });

    expect(res.statusCode).toEqual(401);
    expect(res.body.success).toBe(false);
  });
}); 