const jwt = require('jsonwebtoken');

const payload = {
  sub: '550e8400-e29b-41d4-a716-446655440000',
  username: 'testuser',
  email: '<EMAIL>'
};

const secret = process.env.JWT_SECRET || 'fallback-secret';
const token = jwt.sign(payload, secret, {
  expiresIn: '1h',
  issuer: 'shoutao-backend',
  audience: 'shoutao-app'
});

console.log('Generated token:', token);
console.log('Current time (JS):', Date.now());
console.log('Current time (Unix):', Math.floor(Date.now() / 1000));
