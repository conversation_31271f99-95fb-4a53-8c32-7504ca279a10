import { PrismaClient } from '@prisma/client';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

async function main() {
  logger.info('🌱 Starting database seeding...');

  try {
    logger.info('Database seeding started...');
    
    // Note: This seed file is for production use
    // No demo users or test data will be created
    // Only essential system data and templates will be seeded

    // Create health assessment templates
    logger.info('Creating health assessment templates...');
    const assessmentTemplates = await prisma.healthAssessmentTemplate.createMany({
      data: [
        {
          templateName: 'Basic Motor Function Assessment',
          assessmentType: 'motor_function',
          difficultyLevel: 1,
          estimatedDuration: 1800, // 30 minutes
          maxScore: 100.00,
          assessmentConfig: {
            tasks: [
              { name: 'Finger Tap Test', type: 'coordination', duration: 300 },
              { name: 'Grip Strength Test', type: 'grip_strength', duration: 180 },
              { name: 'Range of Motion Test', type: 'range_of_motion', duration: 420 },
            ],
          },
          scoringRules: {
            accuracy: { weight: 0.4 },
            consistency: { weight: 0.3 },
            speed: { weight: 0.3 },
          },
          isActive: true,
        },
        {
          templateName: 'Advanced Dexterity Assessment',
          assessmentType: 'dexterity',
          difficultyLevel: 2,
          estimatedDuration: 2700, // 45 minutes
          maxScore: 100.00,
          assessmentConfig: {
            tasks: [
              { name: 'Precision Pinch Test', type: 'precision', duration: 480 },
              { name: 'Finger Dexterity Test', type: 'finger_dexterity', duration: 600 },
              { name: 'Grip Strength Endurance', type: 'endurance', duration: 720 },
              { name: 'Coordination Tasks', type: 'coordination', duration: 900 },
            ],
          },
          scoringRules: {
            precision: { weight: 0.35 },
            endurance: { weight: 0.25 },
            coordination: { weight: 0.4 },
          },
          isActive: true,
        },
        {
          templateName: 'Cognitive-Motor Integration Assessment',
          assessmentType: 'cognitive_motor',
          difficultyLevel: 3,
          estimatedDuration: 2100, // 35 minutes
          maxScore: 100.00,
          assessmentConfig: {
            tasks: [
              { name: 'Reaction Time Test', type: 'reaction_time', duration: 420 },
              { name: 'Dual Task Performance', type: 'cognitive', duration: 600 },
              { name: 'Sequential Movement Test', type: 'coordination', duration: 480 },
              { name: 'Memory-Guided Movements', type: 'cognitive', duration: 600 },
            ],
          },
          scoringRules: {
            reaction_time: { weight: 0.3 },
            cognitive_load: { weight: 0.35 },
            motor_control: { weight: 0.35 },
          },
          isActive: true,
        },
      ],
      skipDuplicates: true,
    });

    logger.info(`✅ Created ${assessmentTemplates.count} assessment templates`);

    // Create system configuration data (if needed)
    logger.info('Creating system configuration...');
    
    // Example: Create default system settings
    // This would be actual production configuration, not test data
    
    logger.info('🎉 Database seeding completed successfully!');
    logger.info('Production database is ready with essential system data.');

  } catch (error) {
    logger.error('❌ Database seeding failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    logger.error('Seed script error:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
