import { PrismaClient } from '@prisma/client';
import { passwordService } from '../src/utils/password';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

const TARGET_PHONE_NUMBER = '18535158150';
const NEW_PASSWORD = 'Password@2024';

async function main() {
  logger.info(`Attempting to reset password for user: ${TARGET_PHONE_NUMBER}`);

  // Find user by either phone number or username
  const user = await prisma.user.findFirst({
    where: {
      OR: [
        { phoneNumber: TARGET_PHONE_NUMBER },
        { username: TARGET_PHONE_NUMBER },
      ],
    },
  });

  if (!user) {
    logger.error(`User with phone number or username '${TARGET_PHONE_NUMBER}' not found.`);
    return;
  }

  logger.info(`User found: ${user.username} (ID: ${user.id}). Hashing new password...`);

  const newPasswordHash = await passwordService.hashPassword(NEW_PASSWORD);

  await prisma.user.update({
    where: {
      id: user.id,
    },
    data: {
      passwordHash: newPasswordHash,
    },
  });

  logger.info(`Successfully reset password for user ${user.username}.`);
  logger.info(`You can now log in with phone number ${TARGET_PHONE_NUMBER} and password: ${NEW_PASSWORD}`);
}

main()
  .catch((e) => {
    logger.error('Password reset script failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 