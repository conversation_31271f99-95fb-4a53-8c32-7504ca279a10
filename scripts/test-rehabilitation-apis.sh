#!/bin/bash

# 康复训练API健康检查脚本
# 使用方法: ./scripts/test-rehabilitation-apis.sh [BASE_URL] [TOKEN]

set -e

# 默认配置
BASE_URL=${1:-"http://localhost:3000"}
TOKEN=${2:-""}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  康复训练API健康检查${NC}"
    echo -e "${BLUE}================================${NC}"
    echo "测试时间: $(date)"
    echo "服务器地址: $BASE_URL"
    echo ""
}

print_test() {
    echo -e "${YELLOW}测试: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 生成测试token（如果未提供）
generate_token() {
    if [ -z "$TOKEN" ]; then
        echo "正在生成测试token..."
        cd "$(dirname "$0")/.."
        TOKEN=$(JWT_SECRET=shoutao-dev-jwt-secret-2024-change-in-production node generate-token.js | grep "Generated token:" | cut -d' ' -f3)
        if [ -z "$TOKEN" ]; then
            print_error "无法生成测试token"
            exit 1
        fi
        print_success "Token生成成功"
    fi
}

# 测试API端点
test_api() {
    local name="$1"
    local endpoint="$2"
    local expected_status="${3:-200}"
    
    print_test "$name"
    
    local response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        "$BASE_URL$endpoint")
    
    local body=$(echo "$response" | sed -E 's/HTTPSTATUS\:[0-9]{3}$//')
    local status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
    
    if [ "$status" = "$expected_status" ]; then
        # 检查响应格式
        local success=$(echo "$body" | jq -r '.success // empty' 2>/dev/null)
        if [ "$success" = "true" ]; then
            print_success "$name - 状态码: $status, 响应格式: 正确"
        else
            print_warning "$name - 状态码: $status, 响应格式: 可能有问题"
        fi
    else
        print_error "$name - 期望状态码: $expected_status, 实际状态码: $status"
        echo "响应内容: $body"
        return 1
    fi
}

# 主测试函数
run_tests() {
    local failed=0
    
    # 测试健康检查
    print_test "服务器健康检查"
    local health_response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$BASE_URL/health")
    local health_status=$(echo "$health_response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
    
    if [ "$health_status" = "200" ]; then
        print_success "服务器健康检查 - 状态码: $health_status"
    else
        print_error "服务器健康检查失败 - 状态码: $health_status"
        return 1
    fi
    
    echo ""
    
    # 生成token
    generate_token
    
    echo ""
    
    # 测试康复训练API
    test_api "训练记录API" "/api/v1/training/rehabilitation/training-records?type=all&limit=20&offset=0" || ((failed++))
    test_api "训练计划API" "/api/v1/training/rehabilitation/training-plans?status=active&difficulty=all" || ((failed++))
    test_api "动作要点API" "/api/v1/training/rehabilitation/action-points" || ((failed++))
    test_api "训练目标API" "/api/v1/training/rehabilitation/training-targets" || ((failed++))
    test_api "每日训练记录API" "/api/v1/training/rehabilitation/training-records/daily" || ((failed++))
    test_api "每日训练记录API(带参数)" "/api/v1/training/rehabilitation/training-records/daily?startDate=2025-07-20&endDate=2025-07-25" || ((failed++))
    
    echo ""
    echo -e "${BLUE}================================${NC}"
    
    if [ $failed -eq 0 ]; then
        print_success "所有测试通过! 🎉"
        echo -e "${GREEN}康复训练API运行正常${NC}"
    else
        print_error "有 $failed 个测试失败"
        echo -e "${RED}请检查API服务状态${NC}"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_warning "jq 未安装，响应格式检查将被跳过"
    fi
}

# 主程序
main() {
    print_header
    check_dependencies
    run_tests
}

# 运行主程序
main "$@"
