import { PrismaClient } from '@prisma/client';
import { passwordService } from '../src/utils/password';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

async function main() {
  logger.info('🌱 Starting database seeding...');

  try {
    // Create demo users
    const demoUsers = [
      {
        username: 'demo_patient',
        email: '<EMAIL>',
        password: 'DemoPass123!',
        fullName: 'Demo Patient',
        age: 45,
        gender: 'male',
        recoveryPhase: 'early_recovery',
      },
      {
        username: 'demo_doctor',
        email: '<EMAIL>',
        password: 'DemoPass123!',
        fullName: 'Dr. Demo',
        age: 38,
        gender: 'female',
        recoveryPhase: null,
      },
      {
        username: 'demo_therapist',
        email: '<EMAIL>',
        password: 'DemoPass123!',
        fullName: 'Demo Therapist',
        age: 32,
        gender: 'male',
        recoveryPhase: null,
      },
    ];

    logger.info('Creating demo users...');
    const createdUsers = [];

    for (const userData of demoUsers) {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { username: userData.username },
      });

      if (existingUser) {
        logger.info(`User ${userData.username} already exists, skipping...`);
        createdUsers.push(existingUser);
        continue;
      }

      // Hash password
      const passwordHash = await passwordService.hashPassword(userData.password);

      // Create user
      const user = await prisma.user.create({
        data: {
          username: userData.username,
          email: userData.email,
          passwordHash,
          fullName: userData.fullName,
          age: userData.age,
          gender: userData.gender,
          recoveryPhase: userData.recoveryPhase,
        },
      });

      createdUsers.push(user);
      logger.info(`✅ Created user: ${user.username}`);
    }

    // Create demo devices
    logger.info('Creating demo devices...');
    const demoDevices = [
      {
        userId: createdUsers[0]?.id || '', // demo_patient
        deviceMac: '00:11:22:33:44:55',
        deviceName: 'Smart Glove Pro',
        deviceType: 'smart_glove',
        deviceStatus: 'online',
        batteryLevel: 85,
        firmwareVersion: '1.2.3',
        hardwareVersion: '2.1.0',
      },
      {
        userId: createdUsers[0]?.id || '', // demo_patient
        deviceMac: '00:11:22:33:44:66',
        deviceName: 'Smart Glove Lite',
        deviceType: 'smart_glove',
        deviceStatus: 'offline',
        batteryLevel: 42,
        firmwareVersion: '1.1.8',
        hardwareVersion: '1.5.2',
      },
    ];

    for (const deviceData of demoDevices) {
      const existingDevice = await prisma.userDevice.findUnique({
        where: { deviceMac: deviceData.deviceMac },
      });

      if (existingDevice) {
        logger.info(`Device ${deviceData.deviceMac} already exists, skipping...`);
        continue;
      }

      const device = await prisma.userDevice.create({
        data: deviceData,
      });

      logger.info(`✅ Created device: ${device.deviceName} (${device.deviceMac})`);
    }

    // Create demo rehabilitation plans
    logger.info('Creating demo rehabilitation plans...');
    const demoPlans = [
      {
        userId: createdUsers[0]?.id || '', // demo_patient
        planName: 'Early Recovery Program',
        recoveryStage: 'early_recovery',
        targetForceRange: {
          min: 5,
          max: 25,
          unit: 'N',
        },
        trainingDurationMins: 20,
        sessionsPerWeek: 5,
        difficultyLevel: 1,
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        notes: 'Initial rehabilitation program focusing on basic grip strength recovery.',
      },
      {
        userId: createdUsers[0]?.id || '', // demo_patient
        planName: 'Progressive Strength Training',
        recoveryStage: 'intermediate_recovery',
        targetForceRange: {
          min: 20,
          max: 50,
          unit: 'N',
        },
        trainingDurationMins: 30,
        sessionsPerWeek: 4,
        difficultyLevel: 3,
        startDate: new Date(Date.now() + 31 * 24 * 60 * 60 * 1000), // 31 days from now
        endDate: new Date(Date.now() + 61 * 24 * 60 * 60 * 1000), // 61 days from now
        notes: 'Advanced program for building strength and coordination.',
        isActive: false,
      },
    ];

    for (const planData of demoPlans) {
      const plan = await prisma.rehabilitationPlan.create({
        data: planData,
      });

      logger.info(`✅ Created rehabilitation plan: ${plan.planName}`);
    }

    // Create demo community posts
    logger.info('Creating demo community posts...');
    const demoPosts = [
      {
        authorId: createdUsers[0]?.id || '', // demo_patient
        title: 'My Recovery Journey - Week 1',
        content: 'Just completed my first week of training with the smart glove. The exercises are challenging but I can already feel some improvement in my grip strength. The app makes it easy to track progress and the games are actually fun!',
        postType: 'forum',
        category: 'recovery_stories',
        tags: ['recovery', 'week1', 'progress'],
      },
      {
        authorId: createdUsers[1]?.id || '', // demo_doctor
        title: 'Tips for Effective Rehabilitation',
        content: 'As a healthcare professional, I want to share some tips for maximizing your rehabilitation results:\n\n1. Consistency is key - try to maintain regular training sessions\n2. Listen to your body - don\'t push through pain\n3. Track your progress - the data helps us adjust your program\n4. Stay motivated - celebrate small victories\n\nRemember, recovery is a journey, not a race.',
        postType: 'expert_column',
        category: 'medical_advice',
        tags: ['tips', 'rehabilitation', 'medical'],
        isFeatured: true,
      },
      {
        authorId: createdUsers[2]?.id || '', // demo_therapist
        title: 'Welcome to the Shoutao Community!',
        content: 'Welcome to our supportive community! This is a place where patients, caregivers, and healthcare professionals can share experiences, ask questions, and support each other on the recovery journey.\n\nFeel free to introduce yourself and share your story. We\'re all here to help each other succeed!',
        postType: 'forum',
        category: 'general',
        tags: ['welcome', 'community', 'introduction'],
        isPinned: true,
      },
    ];

    for (const postData of demoPosts) {
      const post = await prisma.post.create({
        data: postData,
      });

      logger.info(`✅ Created post: ${post.title}`);
    }

    // Create assessment templates
    logger.info('Creating assessment templates...');
    const assessmentTemplates = [
      {
        templateName: 'Basic Motor Function Assessment',
        templateType: 'motor_function',
        description: 'Evaluates basic motor function and coordination',
        targetCondition: 'General motor impairment',
        difficultyLevel: 1,
        estimatedDuration: 1800, // 30 minutes
        maxScore: 100.00,
        assessmentConfig: {
          tasks: [
            { name: 'Finger Tap Test', type: 'coordination', duration: 300 },
            { name: 'Grip Strength Test', type: 'grip_strength', duration: 180 },
            { name: 'Range of Motion Test', type: 'range_of_motion', duration: 420 },
          ],
        },
        scoringRules: {
          accuracy: { weight: 0.4 },
          consistency: { weight: 0.3 },
          speed: { weight: 0.2 },
          endurance: { weight: 0.1 },
        },
        version: '1.0',
      },
      {
        templateName: 'Comprehensive Hand Function Assessment',
        templateType: 'comprehensive',
        description: 'Comprehensive evaluation of hand and finger function',
        targetCondition: 'Hand and finger rehabilitation',
        difficultyLevel: 2,
        estimatedDuration: 2700, // 45 minutes
        maxScore: 100.00,
        assessmentConfig: {
          tasks: [
            { name: 'Precision Pinch Test', type: 'precision', duration: 480 },
            { name: 'Finger Dexterity Test', type: 'finger_dexterity', duration: 600 },
            { name: 'Grip Strength Endurance', type: 'endurance', duration: 720 },
            { name: 'Coordination Tasks', type: 'coordination', duration: 900 },
          ],
        },
        scoringRules: {
          precision: { weight: 0.35 },
          strength: { weight: 0.25 },
          endurance: { weight: 0.2 },
          coordination: { weight: 0.2 },
        },
        version: '1.0',
      },
      {
        templateName: 'Cognitive-Motor Assessment',
        templateType: 'cognitive_assessment',
        description: 'Evaluates cognitive aspects of motor control',
        targetCondition: 'Cognitive-motor integration',
        difficultyLevel: 3,
        estimatedDuration: 2100, // 35 minutes
        maxScore: 100.00,
        assessmentConfig: {
          tasks: [
            { name: 'Reaction Time Test', type: 'reaction_time', duration: 420 },
            { name: 'Dual Task Performance', type: 'cognitive', duration: 600 },
            { name: 'Sequential Movement Test', type: 'coordination', duration: 480 },
            { name: 'Memory-Guided Movements', type: 'cognitive', duration: 600 },
          ],
        },
        scoringRules: {
          reaction_time: { weight: 0.3 },
          accuracy: { weight: 0.3 },
          dual_task_cost: { weight: 0.25 },
          memory_performance: { weight: 0.15 },
        },
        version: '1.0',
      },
    ];

    const createdTemplates = await prisma.assessmentTemplate.createMany({
      data: assessmentTemplates,
    });

    logger.info(`✅ Created ${createdTemplates.count} assessment templates`);

    logger.info('🎉 Database seeding completed successfully!');
    logger.info(`Created ${createdUsers.length} users, ${demoDevices.length} devices, ${demoPlans.length} rehabilitation plans, ${demoPosts.length} posts, and ${createdTemplates.count} assessment templates.`);

  } catch (error) {
    logger.error('❌ Database seeding failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((error) => {
    logger.error('Seeding error:', error);
    process.exit(1);
  });
