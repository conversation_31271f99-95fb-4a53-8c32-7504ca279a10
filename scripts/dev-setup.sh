#!/bin/bash

# Shoutao Backend Development Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up Shoutao Backend development environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Setup environment variables
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your actual configuration values."
else
    echo "✅ .env file already exists"
fi

# Setup database
echo "🗄️  Setting up database..."
if command -v psql &> /dev/null; then
    echo "✅ PostgreSQL client found"
    
    # Test database connection
    if npx prisma db push --accept-data-loss; then
        echo "✅ Database schema updated successfully"
    else
        echo "⚠️  Database connection failed. Please check your DATABASE_URL in .env"
    fi
else
    echo "⚠️  PostgreSQL client not found. Please install postgresql-client"
fi

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Setup Git hooks
echo "🪝 Setting up Git hooks..."
npx husky install

# Create logs directory
echo "📁 Creating logs directory..."
mkdir -p logs

# Create uploads directory
echo "📁 Creating uploads directory..."
mkdir -p uploads

# Run tests to verify setup
echo "🧪 Running tests to verify setup..."
if npm test; then
    echo "✅ All tests passed!"
else
    echo "⚠️  Some tests failed. Please check the test output above."
fi

# Build the project
echo "🔨 Building the project..."
if npm run build; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. Update your .env file with the correct database and service configurations"
echo "2. Run 'npm run dev' to start the development server"
echo "3. Visit http://localhost:3000/health to verify the server is running"
echo ""
echo "Available commands:"
echo "  npm run dev          - Start development server"
echo "  npm run build        - Build for production"
echo "  npm run test         - Run tests"
echo "  npm run test:watch   - Run tests in watch mode"
echo "  npm run lint         - Check code style"
echo "  npm run lint:fix     - Fix code style issues"
echo "  npm run format       - Format code with Prettier"
echo ""
