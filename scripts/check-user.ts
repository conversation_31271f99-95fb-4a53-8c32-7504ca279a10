import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkUser() {
  try {
    console.log('Checking user with phone number +8618535158150...');

    // Check if user exists by phone number
    const userByPhone = await prisma.user.findUnique({
      where: { phoneNumber: '+8618535158150' },
    });

    if (userByPhone) {
      console.log('User found by phone number:', {
        id: userByPhone.id,
        username: userByPhone.username,
        phoneNumber: userByPhone.phoneNumber,
        email: userByPhone.email,
        fullName: userByPhone.fullName,
        isActive: userByPhone.isActive,
        createdAt: userByPhone.createdAt
      });
    } else {
      console.log('No user found with phone number +8618535158150');
    }

    // Also check our test user
    const testUser = await prisma.user.findUnique({
      where: { id: '7fa6029e-bdab-428c-a91b-5c8c74a32d33' },
    });

    if (testUser) {
      console.log('Test user found:', {
        id: testUser.id,
        username: testUser.username,
        phoneNumber: testUser.phoneNumber,
        email: testUser.email,
        fullName: testUser.fullName,
        isActive: testUser.isActive
      });
    }

    // List all users
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        phoneNumber: true,
        email: true,
        fullName: true,
        isActive: true,
        createdAt: true
      }
    });

    console.log('\nAll users in database:');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (${user.phoneNumber || user.email}) - Active: ${user.isActive}`);
    });

  } catch (error) {
    console.error('Error checking user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

checkUser()
  .then(() => {
    console.log('✅ User check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ User check failed:', error);
    process.exit(1);
  });
