const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Use a simple hash for testing (not secure for production)
    const passwordHash = '$2b$10$test.hash.for.testing.purposes.only';
    
    // Create test user
    const user = await prisma.user.create({
      data: {
        id: '550e8400-e29b-41d4-a716-446655440000',
        username: 'testuser',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        passwordHash: passwordHash,
        fullName: 'Test User',
        age: 30,
        gender: 'male',
        recoveryPhase: 'intermediate',
        recoveryProgress: 0.5,
        isActive: true
      }
    });
    
    console.log('Test user created:', user);
  } catch (error) {
    if (error.code === 'P2002') {
      console.log('Test user already exists');
    } else {
      console.error('Error creating test user:', error);
    }
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
