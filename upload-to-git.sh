#!/bin/bash

# 通用Git上传脚本
# 请将 YOUR_GIT_REPOSITORY_URL 替换为您的实际仓库URL

echo "🚀 准备上传到Git仓库..."

# 检查是否已有远程仓库
if git remote | grep -q origin; then
    echo "⚠️  已存在origin远程仓库，正在移除..."
    git remote remove origin
fi

# 添加远程仓库
echo "📝 请输入您的Git仓库URL（例如：https://github.com/username/repo.git）："
read -p "仓库URL: " REPO_URL

if [ -z "$REPO_URL" ]; then
    echo "❌ 错误：仓库URL不能为空"
    exit 1
fi

git remote add origin "$REPO_URL"

# 推送到远程仓库
echo "🔄 正在推送代码..."
git branch -M main
git push -u origin main

if [ $? -eq 0 ]; then
    echo "✅ 代码已成功上传到Git仓库!"
    echo "📝 请访问您的仓库查看代码: $REPO_URL"
else
    echo "❌ 上传失败，请检查仓库URL和网络连接"
fi
