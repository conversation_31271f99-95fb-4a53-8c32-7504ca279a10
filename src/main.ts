#!/usr/bin/env node

/**
 * Shoutao Rehabilitation Training App - Backend Server
 * 
 * This is the main entry point for the Express.js application.
 * It initializes and starts the server with all necessary middleware,
 * routes, and services configured according to Express.js best practices.
 */

import 'reflect-metadata'; // Required for dependency injection
import { application } from '@/core/Application';
import { logger } from '@/utils/logger';
import { env } from '@/config/env';

/**
 * Main function to start the application
 */
async function main(): Promise<void> {
  try {
    logger.info('Starting Shoutao Rehabilitation Training App...', {
      nodeVersion: process.version,
      environment: env.NODE_ENV,
      port: env.PORT,
      timestamp: new Date().toISOString()
    });

    // Start the application
    await application.start();

    logger.info('Application started successfully! 🚀', {
      message: 'Server is ready to accept connections',
      healthCheck: `http://localhost:${env.PORT}/health`,
      apiDocs: `http://localhost:${env.PORT}/api/v1`,
    });

  } catch (error) {
    logger.error('Failed to start application', { 
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });
    
    process.exit(1);
  }
}

// Handle startup errors
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception during startup', { error });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection during startup', { reason, promise });
  process.exit(1);
});

// Start the application
if (require.main === module) {
  main();
}

export { application };
