import express, { Application as ExpressApp, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { Server } from 'http';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { requestLogger } from '@/middleware/requestLogger';
import { performanceMonitoring, metricsCollection } from '@/middleware/monitoring';
import { container, SERVICE_NAMES } from '@/core/Container';
import { env } from '@/config/env';

/**
 * Main application class that configures and manages the Express server
 */
export class Application {
  private app: ExpressApp;
  private server: Server | null = null;
  private isInitialized = false;

  constructor() {
    this.app = express();
  }

  /**
   * Initialize the application with all middleware and routes
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('Application already initialized');
      return;
    }

    try {
      logger.info('Initializing application...');

      // Initialize services
      await this.initializeServices();

      // Configure middleware
      this.configureMiddleware();

      // Configure routes
      this.configureRoutes();

      // Configure error handling
      this.configureErrorHandling();

      this.isInitialized = true;
      logger.info('Application initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize application', { error });
      throw error;
    }
  }

  /**
   * Start the HTTP server
   */
  public async start(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(env.PORT, () => {
          logger.info('Server started successfully', {
            port: env.PORT,
            environment: env.NODE_ENV,
            version: process.env.npm_package_version || '1.0.0',
          });
          resolve();
        });

        this.server.on('error', (error) => {
          logger.error('Server error', { error });
          reject(error);
        });

        // Graceful shutdown handling
        this.setupGracefulShutdown();

      } catch (error) {
        logger.error('Failed to start server', { error });
        reject(error);
      }
    });
  }

  /**
   * Stop the HTTP server
   */
  public async stop(): Promise<void> {
    if (!this.server) {
      logger.warn('Server is not running');
      return;
    }

    return new Promise((resolve) => {
      this.server!.close(async () => {
        logger.info('Server stopped');

        // Cleanup services
        await this.cleanup();

        resolve();
      });
    });
  }

  /**
   * Get the Express application instance
   */
  public getApp(): ExpressApp {
    return this.app;
  }

  /**
   * Initialize all services
   */
  private async initializeServices(): Promise<void> {
    logger.info('Initializing services...');

    // Register services in container
    await this.registerServices();

    // Initialize all services
    await container.initializeAll();

    logger.info('All services initialized');
  }

  /**
   * Register services in the dependency injection container
   */
  private async registerServices(): Promise<void> {
    // Import and register services
    const { CacheService } = await import('@/services/CacheService');
    const { TrainingSessionService } = await import('@/services/TrainingSessionService');
    const { DatabaseService } = await import('@/services/database/DatabaseService');
    const { TrainingSessionRepository } = await import('@/services/database/TrainingSessionRepository');
    const { HealthAssessmentService } = await import('@/services/HealthAssessmentService');
    const { HealthAssessmentRepository } = await import('@/services/database/HealthAssessmentRepository');
    const { TrainingPlanService } = await import('@/services/TrainingPlanService');
    const { TrainingPlanRepository } = await import('@/services/database/TrainingPlanRepository');
    const { TrainingRecordService } = await import('@/services/TrainingRecordService');

    // Register services in dependency order
    container.register(SERVICE_NAMES.DATABASE_SERVICE, () => new DatabaseService());
    container.register(SERVICE_NAMES.CACHE_SERVICE, () => new CacheService());
    container.register(SERVICE_NAMES.TRAINING_SESSION_REPOSITORY, () => new TrainingSessionRepository());
    container.register(SERVICE_NAMES.TRAINING_SESSION_SERVICE, () => new TrainingSessionService());
    container.register(SERVICE_NAMES.HEALTH_ASSESSMENT_REPOSITORY, () => new HealthAssessmentRepository());
    container.register(SERVICE_NAMES.HEALTH_ASSESSMENT_SERVICE, () => new HealthAssessmentService());
    container.register(SERVICE_NAMES.TRAINING_PLAN_REPOSITORY, () => new TrainingPlanRepository());
    container.register(SERVICE_NAMES.TRAINING_PLAN_SERVICE, () => new TrainingPlanService());
    container.register(SERVICE_NAMES.TRAINING_RECORD_SERVICE, () => new TrainingRecordService());

    // Initialize data cleanup scheduler
    const { dataCleanupScheduler } = await import('@/services/DataCleanupScheduler');
    dataCleanupScheduler.start();
    logger.info('Data cleanup scheduler started');

    // Add more service registrations here as needed
    logger.info('Services registered in container');
  }

  /**
   * Configure Express middleware
   */
  private configureMiddleware(): void {
    logger.info('Configuring middleware...');

    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ['\'self\''],
          styleSrc: ['\'self\'', '\'unsafe-inline\''],
          scriptSrc: ['\'self\''],
          imgSrc: ['\'self\'', 'data:', 'https:'],
        },
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: env.CORS_ORIGIN?.split(',') || '*',
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Compression
    this.app.use(compression({
      level: 6,
      threshold: 1024,
      filter: (req, res) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        return compression.filter(req, res);
      },
    }));

    // Rate limiting
    this.app.use(rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: env.NODE_ENV === 'production' ? 100 : 1000, // requests per window
      message: {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests, please try again later',
        },
        timestamp: new Date().toISOString(),
      },
      standardHeaders: true,
      legacyHeaders: false,
    }));

    // Body parsing
    this.app.use(express.json({
      limit: '10mb',
      verify: (req, res, buf) => {
        // Store raw body for webhook verification if needed
        (req as any).rawBody = buf;
      },
    }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use(requestLogger);

    // Performance monitoring
    this.app.use(performanceMonitoring);
    this.app.use(metricsCollection);

    // Health check endpoint (before authentication)
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        success: true,
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          version: process.env.npm_package_version || '1.0.0',
        },
      });
    });







    // Setup graceful shutdown handling
    this.app.post('/api/v1/auth-demo/login-phone', async (req: Request, res: Response) => {
      try {
        const { phoneNumber, smsCode } = req.body;

        // Demo: Accept any 6-digit code for demo purposes
        if (!smsCode || smsCode.length !== 6) {
          return res.status(400).json({
            success: false,
            error: {
              message: 'Invalid SMS code format',
              code: 'INVALID_SMS_CODE',
            },
            timestamp: new Date().toISOString(),
          });
        }

        // Demo user data
        const demoUser = {
          id: '550e8400-e29b-41d4-a716-************',
          username: 'demo_user',
          phoneNumber: phoneNumber.startsWith('+86') ? phoneNumber : `+86${phoneNumber}`,
          fullName: '演示用户',
          email: '<EMAIL>',
        };

        // Generate demo tokens
        const accessToken = `demo-access-token-${Date.now()}`;
        const refreshToken = `demo-refresh-token-${Date.now()}`;

        res.json({
          success: true,
          data: {
            user: {
              id: demoUser.id,
              username: demoUser.username,
              phoneNumber: demoUser.phoneNumber,
              fullName: demoUser.fullName,
              email: demoUser.email,
              isActive: true,
              lastLoginAt: new Date().toISOString(),
            },
            accessToken: accessToken,
            refreshToken: refreshToken,
            expiresIn: 3600,
          },
          message: 'Phone login successful',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'PHONE_LOGIN_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Create Test User Demo API
    this.app.post('/api/v1/auth-demo/create-test-user', async (req: Request, res: Response) => {
      try {
        // Import UserService
        const { userService } = await import('@/services/userService');

        const testUserData = {
          username: 'demo_user',
          phoneNumber: '+8618535158150',
          password: '123456',
          fullName: '演示用户',
          email: '<EMAIL>',
          age: 30,
          gender: 'male',
        };

        try {
          const result = await userService.createUser(testUserData);

          res.json({
            success: true,
            data: {
              user: result.user,
              message: '测试用户创建成功',
            },
            timestamp: new Date().toISOString(),
          });
        } catch (createError: any) {
          if (createError.message?.includes('already exists')) {
            res.json({
              success: true,
              data: {
                message: '测试用户已存在，可以直接使用',
                credentials: {
                  identifier: '18535158150 或 demo_user',
                  password: '123456',
                },
              },
              timestamp: new Date().toISOString(),
            });
          } else {
            throw createError;
          }
        }
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'CREATE_USER_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // SMS Demo API - Send SMS Code (POST /auth-demo/send-sms)
    this.app.post('/api/v1/auth-demo/send-sms', async (req: Request, res: Response) => {
      try {
        const { phoneNumber, purpose } = req.body;

        // Generate a demo verification code
        const demoCode = '123456'; // Fixed demo code for testing

        res.json({
          success: true,
          data: {
            sent: true,
            expiresIn: 300, // 5 minutes
            retryAfter: 60,
            demoCode: demoCode, // Only for demo purposes
            message: `验证码已发送到 ${phoneNumber}，演示验证码为: ${demoCode}`,
          },
          message: 'SMS verification code sent successfully',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'SMS_SEND_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // SMS Demo API - Register with Phone (POST /auth-demo/register-phone)
    this.app.post('/api/v1/auth-demo/register-phone', async (req: Request, res: Response) => {
      try {
        const { phoneNumber, smsCode, fullName, password } = req.body;

        // Demo: Accept the fixed demo code
        if (smsCode !== '123456') {
          return res.status(401).json({
            success: false,
            error: {
              message: 'Invalid or expired verification code',
              code: 'INVALID_SMS_CODE',
              details: '演示环境请使用验证码: 123456',
            },
            timestamp: new Date().toISOString(),
          });
        }

        // Generate demo user data
        const userId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const username = `user_${phoneNumber.replace(/\D/g, '').slice(-8)}`;

        // Generate demo tokens
        const accessToken = `demo-access-token-${Date.now()}`;
        const refreshToken = `demo-refresh-token-${Date.now()}`;

        res.json({
          success: true,
          data: {
            user: {
              id: userId,
              username: username,
              phoneNumber: phoneNumber,
              fullName: fullName,
              email: null,
              isActive: true,
              createdAt: new Date().toISOString(),
              lastLoginAt: new Date().toISOString(),
            },
            accessToken: accessToken,
            refreshToken: refreshToken,
            expiresIn: 3600,
          },
          message: 'Registration successful',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'REGISTER_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Debug SMS Verification Status
    this.app.get('/api/v1/debug/sms-status/:phoneNumber', async (req: Request, res: Response) => {
      try {
        const { phoneNumber } = req.params;
        const { purpose = 'register' } = req.query;

        // Import Prisma client
        const { PrismaClient } = await import('@prisma/client');
        const prisma = new PrismaClient();

        // Find all SMS verification records for this phone number
        const records = await prisma.userToken.findMany({
          where: {
            phoneNumber: phoneNumber,
            tokenType: 'sms_verification',
            purpose: purpose as string,
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        });

        // Find the most recent valid record
        const validRecord = await prisma.userToken.findFirst({
          where: {
            phoneNumber: phoneNumber,
            purpose: purpose as string,
            tokenType: 'sms_verification',
            usedAt: null,
            expiresAt: {
              gt: new Date(),
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

        await prisma.$disconnect();

        res.json({
          success: true,
          data: {
            phoneNumber: phoneNumber,
            purpose: purpose,
            totalRecords: records.length,
            hasValidRecord: !!validRecord,
            validRecord: validRecord ? {
              id: validRecord.id,
              createdAt: validRecord.createdAt,
              expiresAt: validRecord.expiresAt,
              attempts: validRecord.attempts,
              usedAt: validRecord.usedAt,
              isExpired: validRecord.expiresAt < new Date(),
            } : null,
            recentRecords: records.map(record => ({
              id: record.id,
              createdAt: record.createdAt,
              expiresAt: record.expiresAt,
              attempts: record.attempts,
              usedAt: record.usedAt,
              isExpired: record.expiresAt < new Date(),
              isUsed: !!record.usedAt,
            })),
          },
          message: 'SMS verification status retrieved',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'DEBUG_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Temporary SMS Send API for Testing (mimics real SMS service)
    this.app.post('/api/v1/auth/send-sms-temp', async (req: Request, res: Response) => {
      try {
        const { phoneNumber, purpose } = req.body;

        // Import services
        const { PrismaClient } = await import('@prisma/client');
        const { passwordService } = await import('@/utils/password');
        const prisma = new PrismaClient();

        // Generate a fixed verification code for testing
        const code = '123456';
        const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

        // Hash the code for storage
        const codeHash = await passwordService.hashPassword(code);

        // Store verification code in database
        await prisma.userToken.create({
          data: {
            tokenType: 'sms_verification',
            tokenHash: codeHash,
            expiresAt,
            phoneNumber: phoneNumber,
            purpose: purpose || 'register',
            ipAddress: req.ip || null,
            userAgent: req.get('User-Agent') || null,
          },
        });

        await prisma.$disconnect();

        res.json({
          success: true,
          data: {
            sent: true,
            expiresIn: 300, // 5 minutes
            retryAfter: 60,
            // For testing only - remove in production
            testCode: code,
            message: `验证码已发送到 ${phoneNumber}，测试验证码为: ${code}`,
          },
          message: 'SMS verification code sent successfully',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'SMS_SEND_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Training records demo endpoint (before authentication)
    this.app.get('/api/v1/training-records-demo/:userId/daily', async (req: Request, res: Response) => {
      try {
        const { userId } = req.params;
        const { date = '2025-07-24' } = req.query;

        // Import TrainingRecordService
        const { TrainingRecordService } = await import('@/services/TrainingRecordService');
        const trainingRecordService = new TrainingRecordService();

        const records = await trainingRecordService.getDailyRecords(userId, date as string);

        res.json({
          success: true,
          data: records,
          message: 'Daily training records retrieved successfully (demo)',
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'DEMO_ERROR',
          },
        });
      }
    });

    // Data cleanup demo endpoint (before authentication)
    this.app.get('/api/v1/training-records-cleanup-demo', async (req: Request, res: Response) => {
      try {
        // Import TrainingRecordService
        const { TrainingRecordService } = await import('@/services/TrainingRecordService');
        const trainingRecordService = new TrainingRecordService();

        const result = await trainingRecordService.cleanupAllData();

        res.json({
          success: true,
          data: result,
          message: 'Data cleanup completed successfully (demo)',
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'CLEANUP_DEMO_ERROR',
          },
        });
      }
    });

    // Training Session Demo API - Create Session (POST /training/sessions)
    this.app.post('/api/v1/training-sessions-demo', async (req: Request, res: Response) => {
      try {
        const { userId, trainingPlanId, trainingPlanName, trainingType } = req.body;

        // Generate demo session data
        const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const startTime = new Date().toISOString();

        res.json({
          success: true,
          data: {
            id: sessionId,
            userId: userId || '550e8400-e29b-41d4-a716-************',
            trainingPlanId: trainingPlanId || 'plan-uuid-demo',
            trainingPlanName: trainingPlanName || '手指灵活训练',
            trainingType: trainingType || 'rehabilitation',
            startTime: startTime,
            status: 'in_progress',
          },
          message: '训练会话创建成功',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'SESSION_CREATE_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Training Session Demo API - Upload Session Data (PUT /training/sessions/:sessionId)
    this.app.put('/api/v1/training-sessions-demo/:sessionId', async (req: Request, res: Response) => {
      try {
        const { sessionId } = req.params;
        const { endTime, duration, averageGripStrength, maxGripStrength, averageAccuracy, totalScore, completedActions, dataPoints } = req.body;

        // Generate demo record ID
        const recordId = `record-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        res.json({
          success: true,
          data: {
            sessionId: sessionId,
            recordId: recordId,
          },
          message: '训练数据上传成功',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'SESSION_UPLOAD_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Training Today Demo API (GET /training/today/:userId)
    this.app.get('/api/v1/training-today-demo/:userId', async (req: Request, res: Response) => {
      try {
        const { userId } = req.params;

        // Generate demo today's training data
        const demoData = {
          trainingDuration: 45, // minutes
          completedActions: 12,
          accuracy: 0.87,
          score: 680,
          averageGripStrength: 3.8,
          maxGripStrength: 4.2,
          trainingCount: 3,
        };

        res.json({
          success: true,
          data: demoData,
          message: '今日训练数据获取成功',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'TODAY_DATA_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Training Weekly Demo API (GET /training/weekly/:userId)
    this.app.get('/api/v1/training-weekly-demo/:userId', async (req: Request, res: Response) => {
      try {
        const { userId } = req.params;

        // Generate demo weekly training data
        const demoData = [
          {
            date: '2025-01-20',
            trainingDuration: 30,
            completedActions: 10,
            accuracy: 0.9,
            score: 500,
            goalAchieved: true,
          },
          {
            date: '2025-01-21',
            trainingDuration: 45,
            completedActions: 15,
            accuracy: 0.85,
            score: 680,
            goalAchieved: true,
          },
          {
            date: '2025-01-22',
            trainingDuration: 0,
            completedActions: 0,
            accuracy: 0,
            score: 0,
            goalAchieved: false,
          },
          {
            date: '2025-01-23',
            trainingDuration: 35,
            completedActions: 12,
            accuracy: 0.88,
            score: 620,
            goalAchieved: true,
          },
          {
            date: '2025-01-24',
            trainingDuration: 40,
            completedActions: 14,
            accuracy: 0.92,
            score: 750,
            goalAchieved: true,
          },
        ];

        res.json({
          success: true,
          data: demoData,
          message: '周训练数据获取成功',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'WEEKLY_DATA_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Training Records Demo API (GET /training/records/:userId)
    this.app.get('/api/v1/training-records-demo/:userId', async (req: Request, res: Response) => {
      try {
        const { userId } = req.params;
        const { page = 1, limit = 20, date } = req.query;

        // Generate demo training records
        const demoRecords = [
          {
            id: 'record-uuid-1',
            userId: userId,
            trainingPlanName: '手指灵活训练',
            trainingDate: '2025-01-24',
            startTime: '2025-01-24T14:00:00Z',
            endTime: '2025-01-24T14:30:00Z',
            durationMinutes: 30,
            completionRate: 1.0,
            accuracyRate: 0.87,
            averageGripStrength: 3.8,
            maxGripStrength: 4.2,
            totalScore: 680,
            completedActions: 15,
            status: 'completed',
          },
          {
            id: 'record-uuid-2',
            userId: userId,
            trainingPlanName: '力量恢复训练',
            trainingDate: '2025-01-23',
            startTime: '2025-01-23T10:00:00Z',
            endTime: '2025-01-23T10:35:00Z',
            durationMinutes: 35,
            completionRate: 0.95,
            accuracyRate: 0.88,
            averageGripStrength: 3.5,
            maxGripStrength: 4.0,
            totalScore: 620,
            completedActions: 12,
            status: 'completed',
          },
        ];

        const totalCount = 50;
        const totalPages = Math.ceil(totalCount / Number(limit));

        res.json({
          success: true,
          data: {
            records: demoRecords,
            totalCount: totalCount,
            totalPages: totalPages,
            currentPage: Number(page),
          },
          message: '训练记录获取成功',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'RECORDS_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Training Goals Demo API - Get Today's Goal (GET /training/goals/:userId/today)
    this.app.get('/api/v1/training-goals-demo/:userId/today', async (req: Request, res: Response) => {
      try {
        const { userId } = req.params;

        const demoGoal = {
          targetDuration: 60,
          targetActions: 15,
          targetAccuracy: 0.85,
          currentDuration: 45,
          currentActions: 12,
          currentAccuracy: 0.87,
          isCompleted: false,
        };

        res.json({
          success: true,
          data: demoGoal,
          message: '今日目标获取成功',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'GOAL_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Training Goals Demo API - Update Today's Goal (PUT /training/goals/:userId/today)
    this.app.put('/api/v1/training-goals-demo/:userId/today', async (req: Request, res: Response) => {
      try {
        const { userId } = req.params;
        const { targetDuration, targetActions, targetAccuracy } = req.body;

        const updatedGoal = {
          goalDate: new Date().toISOString().split('T')[0],
          targetDuration: targetDuration || 60,
          targetActions: targetActions || 15,
          targetAccuracy: targetAccuracy || 0.85,
          currentDuration: 45,
          currentActions: 12,
          currentAccuracy: 0.87,
          isCompleted: false,
        };

        res.json({
          success: true,
          data: updatedGoal,
          message: '今日目标更新成功',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'GOAL_UPDATE_ERROR',
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Data cleanup scheduler status endpoint (before authentication)
    this.app.get('/api/v1/training-records-scheduler-status', async (req: Request, res: Response) => {
      try {
        // Import DataCleanupScheduler
        const { dataCleanupScheduler } = await import('@/services/DataCleanupScheduler');

        const status = dataCleanupScheduler.getStatus();

        res.json({
          success: true,
          data: status,
          message: 'Data cleanup scheduler status retrieved successfully',
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'SCHEDULER_STATUS_ERROR',
          },
        });
      }
    });

    logger.info('Middleware configured');
  }

  /**
   * Configure application routes
   */
  private configureRoutes(): void {
    logger.info('Configuring routes...');

    const apiPrefix = '/api/v1';

    // Import and use refactored routes
    this.app.use(`${apiPrefix}/auth`, require('@/routes/auth.refactored').default);
    this.app.use(`${apiPrefix}/training`, require('@/routes/rehabilitation.refactored').default);
    this.app.use(`${apiPrefix}/training`, require('@/routes/trainingSessions.refactored').default);
    this.app.use(`${apiPrefix}/training`, require('@/routes/trainingPlan.refactored').default);
    this.app.use(`${apiPrefix}/training`, require('@/routes/trainingRecord.refactored').default);
    this.app.use(`${apiPrefix}/health`, require('@/routes/health.refactored').default);
    // Temporarily disabled until validation middleware is fixed
    // this.app.use(`${apiPrefix}/health`, require('@/routes/healthAssessment.refactored').default);

    // Import existing routes (to be refactored later)
    this.app.use(`${apiPrefix}/home`, require('@/routes/home').default);
    this.app.use(`${apiPrefix}/users`, require('@/routes/user').default);
    this.app.use(`${apiPrefix}/users`, require('@/routes/userRecords').default);
    this.app.use(`${apiPrefix}/training`, require('@/routes/trainingSessions').default);
    this.app.use(`${apiPrefix}/games`, require('@/routes/game').default);
    this.app.use(`${apiPrefix}/health`, require('@/routes/healthAssessment').default);
    this.app.use(`${apiPrefix}/devices`, require('@/routes/device').default);

    // 404 handler for undefined routes
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'ROUTE_NOT_FOUND',
          message: `Route ${req.method} ${req.originalUrl} not found`,
        },
        timestamp: new Date().toISOString(),
      });
    });

    logger.info('Routes configured');
  }

  /**
   * Configure error handling middleware
   */
  private configureErrorHandling(): void {
    // Global error handler (must be last)
    this.app.use(errorHandler);

    logger.info('Error handling configured');
  }

  /**
   * Setup graceful shutdown handling
   */
  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`);

      try {
        await this.stop();
        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown', { error });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', { error });
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection', { reason, promise });
      process.exit(1);
    });
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    try {
      await container.destroyAll();
      logger.info('Cleanup completed');
    } catch (error) {
      logger.error('Error during cleanup', { error });
    }
  }
}

// Export singleton instance
export const application = new Application();
