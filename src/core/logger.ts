import winston from 'winston';
import { env } from '@/config/env';

const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.json(),
);

const transports = [
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize({ all: true }),
      winston.format.printf(
        (info) => `${info.timestamp} [${info.level}]: ${info.message}`,
      ),
    ),
  }),
];

const logger = winston.createLogger({
  level: env.LOG_LEVEL || 'info',
  levels: logLevels,
  format,
  transports,
  defaultMeta: {
    service: 'shoutao-backend',
    environment: env.NODE_ENV,
  },
});

winston.addColors(logColors);

export { logger };
