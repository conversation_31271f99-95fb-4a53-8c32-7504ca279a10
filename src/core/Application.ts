import express, { Application as ExpressApplication, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import { Router } from 'express';
import { env } from '@/config/env';
import { logger } from './logger';
import { errorHandler } from '@/middleware/errorHandler';
import { requestLogger } from '@/middleware/requestLogger';
import adminRoutes from '@/routes/admin';
import appRoutes from '@/routes/app';
import { swaggerDocs } from '@/config/swagger';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      startTime?: number;
      adminId?: string;
      userId?: string;
    }
  }
}

class Application {
  public app: ExpressApplication;
  private port: number;

  constructor() {
    this.app = express();
    this.port = env.PORT;
    this.configureMiddleware();
    this.configureRoutes();
    this.configureErrorHandling();
  }

  private configureMiddleware(): void {
    const allowedOrigins = (env.CORS_ORIGIN || `http://localhost:${this.port}`).split(',');

    const corsOptions: cors.CorsOptions = {
      origin: (origin, callback) => {
        if (!origin || allowedOrigins.includes(origin) || env.NODE_ENV === 'development') {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
    };

    this.app.options('*', cors(corsOptions));
    this.app.use(cors(corsOptions));

    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    this.app.set('trust proxy', 1);

    this.app.use((req: Request, res: Response, next: NextFunction) => {
      req.startTime = Date.now();
      next();
    });
    
    this.app.use(requestLogger);
  }

  private configureRoutes(): void {
    const apiRouter = Router();

    apiRouter.get('/', (req, res) => {
      res.send(`
        <h1>Shoutao Rehabilitation Training API</h1>
        <p>Welcome to the API documentation.</p>
        <p>Visit <a href="/api/v1/docs">/api/v1/docs</a> for the Swagger UI.</p>
      `);
    });

    swaggerDocs(apiRouter);

    apiRouter.use('/admin', adminRoutes);
    apiRouter.use('/app', appRoutes);

    this.app.use('/api/v1', apiRouter);

    this.app.get('/health', (req, res) => {
      res.status(200).json({ status: 'UP', timestamp: new Date() });
    });
  }

  private configureErrorHandling(): void {
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      await new Promise<void>((resolve) => {
        this.app.listen(this.port, () => {
          logger.info(`Server started successfully`, {
            port: this.port,
            version: '1.0.0', // Changed from env.APP_VERSION to hardcoded version
          });
          logger.info(
            `Application started successfully! 🚀 Server is ready to accept connections`,
            {
              healthCheck: `http://localhost:${this.port}/health`,
              apiDocs: `http://localhost:${this.port}/api/v1/docs`,
            },
          );
          resolve();
        });
      });
    } catch (error) {
      logger.error('Failed to start application', { error: (error as Error).message });
      process.exit(1);
    }
  }
}

export default Application;

// Export a singleton instance
export const application = new Application();
