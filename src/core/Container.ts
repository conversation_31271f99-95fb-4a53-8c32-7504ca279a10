import { logger } from '@/utils/logger';

/**
 * Dependency injection container
 * Implements singleton pattern for service management
 */
export class Container {
  private static instance: Container;
  private services = new Map<string, any>();
  private factories = new Map<string, ServiceFactory>();
  private singletons = new Set<string>();

  private constructor() {}

  /**
   * Get container singleton instance
   */
  public static getInstance(): Container {
    if (!Container.instance) {
      Container.instance = new Container();
    }
    return Container.instance;
  }

  /**
   * Register a service factory
   */
  public register<T>(
    name: string,
    factory: ServiceFactory<T>,
    options: ServiceOptions = {}
  ): void {
    if (this.factories.has(name)) {
      logger.warn(`Service ${name} is already registered. Overwriting.`);
    }

    this.factories.set(name, factory);

    if (options.singleton !== false) {
      this.singletons.add(name);
    }

    logger.info(`Service registered: ${name}`, {
      singleton: this.singletons.has(name),
      options,
    });
  }

  /**
   * Register a service instance directly
   */
  public registerInstance<T>(name: string, instance: T): void {
    this.services.set(name, instance);
    this.singletons.add(name);

    logger.info(`Service instance registered: ${name}`);
  }

  /**
   * Get a service instance
   */
  public get<T>(name: string): T {
    // Return existing instance if it's a singleton
    if (this.singletons.has(name) && this.services.has(name)) {
      return this.services.get(name) as T;
    }

    // Get factory and create instance
    const factory = this.factories.get(name);
    if (!factory) {
      throw new Error(`Service ${name} is not registered`);
    }

    try {
      const instance = factory(this);

      // Store instance if it's a singleton
      if (this.singletons.has(name)) {
        this.services.set(name, instance);
      }

      logger.debug(`Service instance created: ${name}`);
      return instance as T;
    } catch (error) {
      logger.error(`Failed to create service instance: ${name}`, { error });
      throw error;
    }
  }

  /**
   * Check if a service is registered
   */
  public has(name: string): boolean {
    return this.factories.has(name) || this.services.has(name);
  }

  /**
   * Remove a service registration
   */
  public unregister(name: string): void {
    this.factories.delete(name);
    this.services.delete(name);
    this.singletons.delete(name);

    logger.info(`Service unregistered: ${name}`);
  }

  /**
   * Get all registered service names
   */
  public getRegisteredServices(): string[] {
    const factoryNames = Array.from(this.factories.keys());
    const instanceNames = Array.from(this.services.keys());
    return [...new Set([...factoryNames, ...instanceNames])];
  }

  /**
   * Clear all services (use with caution)
   */
  public clear(): void {
    this.factories.clear();
    this.services.clear();
    this.singletons.clear();

    logger.warn('All services cleared from container');
  }

  /**
   * Initialize all registered services that have an initialize method
   */
  public async initializeAll(): Promise<void> {
    const serviceNames = this.getRegisteredServices();

    logger.info('Initializing all services', { count: serviceNames.length });

    for (const serviceName of serviceNames) {
      try {
        const service = this.get(serviceName);

        if (service && typeof service.initialize === 'function') {
          await service.initialize();
          logger.info(`Service initialized: ${serviceName}`);
        }
      } catch (error) {
        logger.error(`Failed to initialize service: ${serviceName}`, { error });
        throw error;
      }
    }

    logger.info('All services initialized successfully');
  }

  /**
   * Destroy all services that have a destroy method
   */
  public async destroyAll(): Promise<void> {
    const serviceNames = this.getRegisteredServices();

    logger.info('Destroying all services', { count: serviceNames.length });

    for (const serviceName of serviceNames) {
      try {
        const service = this.services.get(serviceName);

        if (service && typeof service.destroy === 'function') {
          await service.destroy();
          logger.info(`Service destroyed: ${serviceName}`);
        }
      } catch (error) {
        logger.error(`Failed to destroy service: ${serviceName}`, { error });
      }
    }

    this.clear();
    logger.info('All services destroyed');
  }
}

/**
 * Service factory function type
 */
export type ServiceFactory<T = any> = (container: Container) => T;

/**
 * Service registration options
 */
export interface ServiceOptions {
  singleton?: boolean;
  dependencies?: string[];
  metadata?: Record<string, any>;
}

/**
 * Service decorator for automatic registration
 */
export function Service(name: string, options: ServiceOptions = {}) {
  return function <T extends new (...args: any[]) => any>(constructor: T) {
    const container = Container.getInstance();

    container.register(
      name,
      (container) => {
        // Resolve dependencies if specified
        const dependencies = options.dependencies?.map(dep => container.get(dep)) || [];
        return new constructor(...dependencies);
      },
      options
    );

    return constructor;
  };
}

/**
 * Injectable decorator for marking classes as injectable
 */
export function Injectable(name?: string) {
  return function <T extends new (...args: any[]) => any>(constructor: T) {
    const serviceName = name || constructor.name;

    // Add metadata for reflection
    Reflect.defineMetadata('injectable', true, constructor);
    Reflect.defineMetadata('serviceName', serviceName, constructor);

    return constructor;
  };
}

/**
 * Inject decorator for dependency injection
 */
export function Inject(serviceName: string) {
  return function (target: any, propertyKey: string | symbol | undefined, parameterIndex: number) {
    const existingTokens = Reflect.getMetadata('inject:tokens', target) || [];
    existingTokens[parameterIndex] = serviceName;
    Reflect.defineMetadata('inject:tokens', existingTokens, target);
  };
}

// Export singleton instance
export const container = Container.getInstance();

// Service name constants
export const SERVICE_NAMES = {
  USER_SERVICE: 'UserService',
  AUTH_SERVICE: 'AuthService',
  REHABILITATION_SERVICE: 'RehabilitationService',
  TRAINING_SESSION_SERVICE: 'TrainingSessionService',
  TRAINING_SESSION_REPOSITORY: 'TrainingSessionRepository',
  HEALTH_ASSESSMENT_SERVICE: 'HealthAssessmentService',
  HEALTH_ASSESSMENT_REPOSITORY: 'HealthAssessmentRepository',
  TRAINING_PLAN_SERVICE: 'TrainingPlanService',
  TRAINING_PLAN_REPOSITORY: 'TrainingPlanRepository',
  TRAINING_RECORD_SERVICE: 'TrainingRecordService',
  CACHE_SERVICE: 'CacheService',
  FILE_SERVICE: 'FileService',
  NOTIFICATION_SERVICE: 'NotificationService',
  EVENT_SERVICE: 'EventService',
  DATABASE_SERVICE: 'DatabaseService',
  LOGGER_SERVICE: 'LoggerService',
} as const;

export type ServiceName = typeof SERVICE_NAMES[keyof typeof SERVICE_NAMES];
