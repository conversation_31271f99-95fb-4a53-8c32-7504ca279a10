import { Queue, Worker } from 'bullmq';
import IORedis from 'ioredis';
import { logger } from '@/utils/logger';

// 视频处理任务的数据接口
export interface VideoJobData {
  videoId: string;
  filePath: string;
}

// 推荐：将Redis连接配置移至中心化配置文件
const connection = new IORedis(process.env.REDIS_URL || 'redis://localhost:6379', {
  maxRetriesPerRequest: null,
});

connection.on('connect', () => {
  logger.info('Redis connection established for BullMQ.');
});

connection.on('error', (err) => {
  logger.error('Redis connection error for BullMQ:', err);
});

// 1. 创建并导出视频处理队列
export const videoQueue = new Queue<VideoJobData>('video-processing', {
  connection,
  defaultJobOptions: {
    attempts: 3, // 任务失败后最多重试3次
    backoff: {
      type: 'exponential',
      delay: 1000, // 第一次重试前延迟1秒
    },
    removeOnComplete: true, // 任务完成后自动删除
    removeOnFail: {
      age: 24 * 3600, // 失败任务保留24小时
    },
  },
});

logger.info('BullMQ video processing queue initialized.');
