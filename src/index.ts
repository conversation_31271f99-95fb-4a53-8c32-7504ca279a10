import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { PrismaClient } from '@prisma/client';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';

import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';
import { rateLimiter } from '@/middleware/rateLimiter';
import { requestLogger } from '@/middleware/requestLogger';
import {
  performanceMonitoring,
  metricsCollection,
  rateLimiting as customRateLimiting
} from '@/middleware/monitoring';
import { validateEnv } from '@/config/env';
import { logger } from '@/utils/logger';

// Routes
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/user';
import userRecordsRoutes from '@/routes/userRecords';
import trainingSessionsRoutes from '@/routes/trainingSessions';
import rehabilitationRoutes from '@/routes/rehabilitation';
import deviceRoutes from '@/routes/device';
import trainingRoutes from '@/routes/training';
import gameRoutes from '@/routes/game';
import gameRecordsRoutes from '@/routes/gameRecords';
import communityRoutes from '@/routes/community';
import healthRoutes from '@/routes/health';
import healthAssessmentRoutes from '@/routes/healthAssessment';
import homeRoutes from '@/routes/home';

// WebSocket handlers
import { setupDeviceWebSocket } from '@/websocket/deviceHandler';
import { setupGameWebSocket } from '@/websocket/gameHandler';

class Application {
  public app: express.Application;
  public server: any;
  public io!: SocketIOServer;
  public prisma: PrismaClient;
  private port: number;

  constructor() {
    this.app = express();
    this.port = parseInt(process.env['PORT'] || '3000', 10);
    this.prisma = new PrismaClient();
    
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeWebSocket();
    this.initializeErrorHandling();
  }

  private initializeMiddlewares(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env['CORS_ORIGIN']?.split(',') || ['http://localhost:3000'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Client-Version', 'X-Platform'],
    }));

    // Compression and parsing
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging
    if (process.env['NODE_ENV'] !== 'test') {
      this.app.use(morgan('combined', {
        stream: { write: (message: string) => logger.info(message.trim()) }
      }));
    }

    // Rate limiting
    this.app.use(rateLimiter);

    // Request logging middleware
    this.app.use(requestLogger);

    // Performance monitoring
    this.app.use(performanceMonitoring);
    this.app.use(metricsCollection);

    // Health check (before auth) - both paths for compatibility
    this.app.use('/health', healthRoutes);
    this.app.use('/api/v1/health', healthRoutes);
  }

  private initializeRoutes(): void {
    const apiPrefix = '/api/v1';

    // API routes
    this.app.use(`${apiPrefix}/auth`, authRoutes);
    this.app.use(`${apiPrefix}/users`, userRoutes);
    this.app.use(`${apiPrefix}/users`, userRecordsRoutes);
    this.app.use(`${apiPrefix}/training`, trainingSessionsRoutes);
    this.app.use(`${apiPrefix}/training`, rehabilitationRoutes);
    this.app.use(`${apiPrefix}/devices`, deviceRoutes);
    this.app.use(`${apiPrefix}/training`, trainingRoutes);
    this.app.use(`${apiPrefix}/games`, gameRoutes);
    this.app.use(`${apiPrefix}/games`, gameRecordsRoutes);
    this.app.use(`${apiPrefix}/community`, communityRoutes);
    this.app.use(`${apiPrefix}/health`, healthAssessmentRoutes);
    this.app.use(`${apiPrefix}/home`, homeRoutes);

    // Root endpoint
    this.app.get('/', (_req, res) => {
      res.json({
        success: true,
        message: 'Shoutao Backend API',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
      });
    });
  }

  private initializeWebSocket(): void {
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env['WEBSOCKET_CORS_ORIGIN'] || "*",
        methods: ["GET", "POST"]
      },
      transports: ['websocket', 'polling']
    });

    // Setup WebSocket namespaces
    setupDeviceWebSocket(this.io);
    setupGameWebSocket(this.io);

    logger.info('WebSocket server initialized');
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Validate environment variables
      validateEnv();

      // Connect to database
      await this.prisma.$connect();
      logger.info('Database connected successfully');

      // Start server
      this.server.listen(this.port, () => {
        logger.info(`🚀 Server running on port ${this.port}`);
        logger.info(`📊 Environment: ${process.env['NODE_ENV']}`);
        logger.info(`🔗 Database: Connected`);
        logger.info(`🌐 WebSocket: Enabled`);
      });

      // Graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      try {
        // Close server
        if (this.server) {
          this.server.close(() => {
            logger.info('HTTP server closed');
          });
        }

        // Close WebSocket connections
        if (this.io) {
          this.io.close(() => {
            logger.info('WebSocket server closed');
          });
        }

        // Disconnect from database
        await this.prisma.$disconnect();
        logger.info('Database disconnected');

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }
}

// Start the application
const app = new Application();
app.start().catch((error) => {
  logger.error('Application startup failed:', error);
  process.exit(1);
});

export default app;
