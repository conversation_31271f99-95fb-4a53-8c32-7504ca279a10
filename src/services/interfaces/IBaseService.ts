/**
 * Base service interface that defines common service operations
 */
export interface IBaseService {
  /**
   * Service name for logging and identification
   */
  readonly serviceName: string;

  /**
   * Initialize the service (if needed)
   */
  initialize?(): Promise<void>;

  /**
   * Cleanup resources when service is destroyed
   */
  destroy?(): Promise<void>;
}

/**
 * Generic CRUD service interface
 */
export interface ICrudService<T, CreateDto, UpdateDto> extends IBaseService {
  /**
   * Create a new entity
   */
  create(data: CreateDto): Promise<T>;

  /**
   * Find entity by ID
   */
  findById(id: string): Promise<T | null>;

  /**
   * Find entities with pagination and filters
   */
  findMany(options: FindManyOptions): Promise<PaginatedResult<T>>;

  /**
   * Update entity by ID
   */
  update(id: string, data: UpdateDto): Promise<T>;

  /**
   * Delete entity by ID
   */
  delete(id: string): Promise<boolean>;

  /**
   * Check if entity exists
   */
  exists(id: string): Promise<boolean>;
}

/**
 * Options for finding multiple entities
 */
export interface FindManyOptions {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
  include?: string[];
}

/**
 * Paginated result wrapper
 */
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
    hasMore: boolean;
  };
}

/**
 * Service operation result
 */
export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: ServiceError;
  metadata?: Record<string, any>;
}

/**
 * Service error interface
 */
export interface ServiceError {
  code: string;
  message: string;
  details?: any;
  statusCode?: number;
}

/**
 * Cache service interface
 */
export interface ICacheService extends IBaseService {
  /**
   * Get value from cache
   */
  get<T>(key: string): Promise<T | null>;

  /**
   * Set value in cache with optional TTL
   */
  set<T>(key: string, value: T, ttlSeconds?: number): Promise<void>;

  /**
   * Delete value from cache
   */
  delete(key: string): Promise<boolean>;

  /**
   * Check if key exists in cache
   */
  exists(key: string): Promise<boolean>;

  /**
   * Clear all cache entries (use with caution)
   */
  clear(): Promise<void>;

  /**
   * Get multiple values from cache
   */
  mget<T>(keys: string[]): Promise<(T | null)[]>;

  /**
   * Set multiple values in cache
   */
  mset<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void>;
}

/**
 * Event service interface for pub/sub functionality
 */
export interface IEventService extends IBaseService {
  /**
   * Emit an event
   */
  emit(eventName: string, data: any): Promise<void>;

  /**
   * Subscribe to an event
   */
  subscribe(eventName: string, handler: EventHandler): Promise<void>;

  /**
   * Unsubscribe from an event
   */
  unsubscribe(eventName: string, handler: EventHandler): Promise<void>;
}

/**
 * Event handler function type
 */
export type EventHandler = (data: any) => Promise<void> | void;

/**
 * File service interface
 */
export interface IFileService extends IBaseService {
  /**
   * Upload a file
   */
  upload(file: FileUpload): Promise<FileResult>;

  /**
   * Download a file
   */
  download(fileId: string): Promise<Buffer>;

  /**
   * Delete a file
   */
  delete(fileId: string): Promise<boolean>;

  /**
   * Get file metadata
   */
  getMetadata(fileId: string): Promise<FileMetadata>;
}

/**
 * File upload interface
 */
export interface FileUpload {
  buffer: Buffer;
  originalName: string;
  mimeType: string;
  size: number;
  userId?: string;
  category?: string;
}

/**
 * File result interface
 */
export interface FileResult {
  id: string;
  url: string;
  metadata: FileMetadata;
}

/**
 * File metadata interface
 */
export interface FileMetadata {
  id: string;
  originalName: string;
  fileName: string;
  mimeType: string;
  size: number;
  uploadedAt: Date;
  userId?: string;
  category?: string;
}

/**
 * Notification service interface
 */
export interface INotificationService extends IBaseService {
  /**
   * Send email notification
   */
  sendEmail(to: string, subject: string, content: string, options?: EmailOptions): Promise<void>;

  /**
   * Send SMS notification
   */
  sendSMS(phoneNumber: string, message: string, options?: SMSOptions): Promise<void>;

  /**
   * Send push notification
   */
  sendPush(userId: string, title: string, body: string, options?: PushOptions): Promise<void>;
}

/**
 * Email options interface
 */
export interface EmailOptions {
  from?: string;
  html?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer;
    contentType?: string;
  }>;
}

/**
 * SMS options interface
 */
export interface SMSOptions {
  template?: string;
  variables?: Record<string, string>;
}

/**
 * Push notification options interface
 */
export interface PushOptions {
  data?: Record<string, any>;
  badge?: number;
  sound?: string;
  icon?: string;
}
