/**
 * Training Record Service
 * Handles business logic for training data storage and querying
 */

import { TrainingSessionRepository } from './database/TrainingSessionRepository';
import { logger } from '@/utils/logger';

export interface DailyTrainingRecord {
  date: string;
  sessions: any[];
  summary: {
    totalSessions: number;
    completedSessions: number;
    totalDuration: number; // in minutes
    averageScore: number;
    completionRate: number;
    averageAccuracy: number;
  };
  chartData: {
    hourlyActivity: Array<{
      hour: number;
      sessions: number;
      duration: number;
    }>;
    performanceBySession: Array<{
      sessionId: string;
      startTime: string;
      score: number;
      accuracy: number;
      duration: number;
    }>;
  };
}

export interface WeeklyTrainingRecord {
  week: string; // YYYY-WW format
  weekStart: string;
  weekEnd: string;
  dailyStats: Array<{
    date: string;
    sessions: number;
    completedSessions: number;
    totalDuration: number;
    averageScore: number;
    averageAccuracy: number;
  }>;
  weeklyTotals: {
    totalSessions: number;
    completedSessions: number;
    totalDuration: number;
    averageScore: number;
    averageAccuracy: number;
    completionRate: number;
  };
  trends: {
    scoreImprovement: number;
    accuracyImprovement: number;
    consistencyImprovement: number;
  };
  chartData: {
    dailyProgress: Array<{
      date: string;
      score: number;
      accuracy: number;
      duration: number;
    }>;
    weeklyComparison: {
      currentWeek: number;
      previousWeek: number;
      improvement: number;
    };
  };
}

export interface MonthlyTrainingRecord {
  month: string; // YYYY-MM format
  monthStart: string;
  monthEnd: string;
  monthlyTotals: {
    totalSessions: number;
    completedSessions: number;
    totalDuration: number;
    averageScore: number;
    averageAccuracy: number;
    completionRate: number;
    goalAchievementRate: number;
  };
  weeklyBreakdown: Array<{
    week: number;
    sessions: number;
    completedSessions: number;
    totalDuration: number;
    averageScore: number;
  }>;
  dailyActivity: Array<{
    date: string;
    sessions: number;
    duration: number;
    score: number;
  }>;
  improvements: {
    scoreImprovement: number;
    accuracyImprovement: number;
    consistencyImprovement: number;
  };
  recommendations: string[];
  chartData: {
    monthlyProgress: Array<{
      week: number;
      averageScore: number;
      totalDuration: number;
      completionRate: number;
    }>;
    dailyHeatmap: Array<{
      date: string;
      value: number; // Activity intensity
    }>;
    goalProgress: {
      achieved: number;
      target: number;
      percentage: number;
    };
  };
}

export interface DataCleanupResult {
  deletedSessions: number;
  deletedDataPoints: number;
  cleanupDate: string;
  retentionPeriod: number; // days
}

export class TrainingRecordService {
  private trainingSessionRepository: TrainingSessionRepository;

  constructor() {
    this.trainingSessionRepository = new TrainingSessionRepository();
  }

  /**
   * Get daily training records for a user
   */
  public async getDailyRecords(userId: string, date: string): Promise<DailyTrainingRecord> {
    try {
      logger.info('Getting daily training records', { userId, date });

      const targetDate = new Date(date);
      if (isNaN(targetDate.getTime())) {
        throw new Error('Invalid date format. Use YYYY-MM-DD');
      }

      const dailyData = await this.trainingSessionRepository.getDailyTrainingRecords(userId, targetDate);

      // Generate chart data
      const chartData = this.generateDailyChartData(dailyData.sessions);

      const record: DailyTrainingRecord = {
        date,
        sessions: dailyData.sessions,
        summary: {
          totalSessions: dailyData.totalSessions,
          completedSessions: dailyData.completedSessions,
          totalDuration: dailyData.totalDuration,
          averageScore: Math.round(dailyData.averageScore * 100) / 100,
          completionRate: Math.round(dailyData.completionRate * 100) / 100,
          averageAccuracy: this.calculateAverageAccuracy(dailyData.sessions),
        },
        chartData,
      };

      logger.info('Daily training records retrieved', {
        userId,
        date,
        totalSessions: record.summary.totalSessions,
      });

      return record;
    } catch (error) {
      logger.error('Failed to get daily training records', { error, userId, date });
      throw error;
    }
  }

  /**
   * Get weekly training records for a user
   */
  public async getWeeklyRecords(userId: string, week: string): Promise<WeeklyTrainingRecord> {
    try {
      logger.info('Getting weekly training records', { userId, week });

      const { weekStart, weekEnd } = this.parseWeekString(week);

      const weeklyData = await this.trainingSessionRepository.getWeeklyTrainingStats(userId, weekStart);

      // Generate chart data
      const chartData = this.generateWeeklyChartData(weeklyData.dailyStats, userId);

      const record: WeeklyTrainingRecord = {
        week,
        weekStart: weekStart.toISOString().split('T')[0],
        weekEnd: weekEnd.toISOString().split('T')[0],
        dailyStats: weeklyData.dailyStats,
        weeklyTotals: {
          ...weeklyData.weeklyTotals,
          averageScore: Math.round(weeklyData.weeklyTotals.averageScore * 100) / 100,
          averageAccuracy: Math.round(weeklyData.weeklyTotals.averageAccuracy * 100) / 100,
          completionRate: Math.round(weeklyData.weeklyTotals.completionRate * 100) / 100,
        },
        trends: {
          scoreImprovement: Math.round(weeklyData.trends.scoreImprovement * 100) / 100,
          accuracyImprovement: Math.round(weeklyData.trends.accuracyImprovement * 100) / 100,
          consistencyImprovement: Math.round(weeklyData.trends.consistencyImprovement * 100) / 100,
        },
        chartData,
      };

      logger.info('Weekly training records retrieved', {
        userId,
        week,
        totalSessions: record.weeklyTotals.totalSessions,
      });

      return record;
    } catch (error) {
      logger.error('Failed to get weekly training records', { error, userId, week });
      throw error;
    }
  }

  /**
   * Get monthly training records for a user
   */
  public async getMonthlyRecords(userId: string, month: string): Promise<MonthlyTrainingRecord> {
    try {
      logger.info('Getting monthly training records', { userId, month });

      const { year, monthNum } = this.parseMonthString(month);

      const monthlyData = await this.trainingSessionRepository.getMonthlyTrainingStats(userId, year, monthNum);

      // Generate chart data
      const chartData = this.generateMonthlyChartData(monthlyData);

      const monthStart = new Date(year, monthNum - 1, 1);
      const monthEnd = new Date(year, monthNum, 0);

      const record: MonthlyTrainingRecord = {
        month,
        monthStart: monthStart.toISOString().split('T')[0],
        monthEnd: monthEnd.toISOString().split('T')[0],
        monthlyTotals: {
          ...monthlyData.monthlyTotals,
          averageScore: Math.round(monthlyData.monthlyTotals.averageScore * 100) / 100,
          averageAccuracy: Math.round(monthlyData.monthlyTotals.averageAccuracy * 100) / 100,
          completionRate: Math.round(monthlyData.monthlyTotals.completionRate * 100) / 100,
          goalAchievementRate: Math.round(monthlyData.monthlyTotals.goalAchievementRate * 100) / 100,
        },
        weeklyBreakdown: monthlyData.weeklyBreakdown,
        dailyActivity: monthlyData.dailyActivity,
        improvements: {
          scoreImprovement: Math.round(monthlyData.improvements.scoreImprovement * 100) / 100,
          accuracyImprovement: Math.round(monthlyData.improvements.accuracyImprovement * 100) / 100,
          consistencyImprovement: Math.round(monthlyData.improvements.consistencyImprovement * 100) / 100,
        },
        recommendations: monthlyData.recommendations,
        chartData,
      };

      logger.info('Monthly training records retrieved', {
        userId,
        month,
        totalSessions: record.monthlyTotals.totalSessions,
      });

      return record;
    } catch (error) {
      logger.error('Failed to get monthly training records', { error, userId, month });
      throw error;
    }
  }

  /**
   * Clean up old training data for a user
   */
  public async cleanupUserData(userId: string): Promise<DataCleanupResult> {
    try {
      logger.info('Starting user data cleanup', { userId });

      const result = await this.trainingSessionRepository.cleanupOldData(userId);

      const cleanupResult: DataCleanupResult = {
        deletedSessions: result.deletedSessions,
        deletedDataPoints: result.deletedDataPoints,
        cleanupDate: new Date().toISOString(),
        retentionPeriod: 30,
      };

      logger.info('User data cleanup completed', { userId, result: cleanupResult });

      return cleanupResult;
    } catch (error) {
      logger.error('Failed to cleanup user data', { error, userId });
      throw error;
    }
  }

  /**
   * Clean up old training data for all users
   */
  public async cleanupAllData(): Promise<DataCleanupResult> {
    try {
      logger.info('Starting global data cleanup');

      const result = await this.trainingSessionRepository.cleanupOldData();

      const cleanupResult: DataCleanupResult = {
        deletedSessions: result.deletedSessions,
        deletedDataPoints: result.deletedDataPoints,
        cleanupDate: new Date().toISOString(),
        retentionPeriod: 30,
      };

      logger.info('Global data cleanup completed', { result: cleanupResult });

      return cleanupResult;
    } catch (error) {
      logger.error('Failed to cleanup all data', { error });
      throw error;
    }
  }

  /**
   * Parse week string (YYYY-WW) to get week start and end dates
   */
  private parseWeekString(week: string): { weekStart: Date; weekEnd: Date } {
    const [year, weekNum] = week.split('-W');
    if (!year || !weekNum) {
      throw new Error('Invalid week format. Use YYYY-WW');
    }

    const yearNum = parseInt(year);
    const weekNumber = parseInt(weekNum);

    if (weekNumber < 1 || weekNumber > 53) {
      throw new Error('Week number must be between 1 and 53');
    }

    // Calculate the first day of the year
    const firstDayOfYear = new Date(yearNum, 0, 1);

    // Calculate the first Monday of the year
    const firstMonday = new Date(firstDayOfYear);
    const dayOfWeek = firstDayOfYear.getDay();
    const daysToMonday = dayOfWeek === 0 ? 1 : 8 - dayOfWeek;
    firstMonday.setDate(firstDayOfYear.getDate() + daysToMonday);

    // Calculate the start of the target week
    const weekStart = new Date(firstMonday);
    weekStart.setDate(firstMonday.getDate() + (weekNumber - 1) * 7);

    // Calculate the end of the target week
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);

    return { weekStart, weekEnd };
  }

  /**
   * Parse month string (YYYY-MM) to get year and month
   */
  private parseMonthString(month: string): { year: number; monthNum: number } {
    const [year, monthStr] = month.split('-');
    if (!year || !monthStr) {
      throw new Error('Invalid month format. Use YYYY-MM');
    }

    const yearNum = parseInt(year);
    const monthNum = parseInt(monthStr);

    if (monthNum < 1 || monthNum > 12) {
      throw new Error('Month number must be between 1 and 12');
    }

    return { year: yearNum, monthNum };
  }

  /**
   * Calculate average accuracy from sessions
   */
  private calculateAverageAccuracy(sessions: any[]): number {
    const completedSessions = sessions.filter(s => s.sessionStatus === 'completed');
    if (completedSessions.length === 0) return 0;

    const totalAccuracy = completedSessions.reduce((sum, s) =>
      sum + (s.achievedAccuracy?.toNumber() || 0), 0
    );

    return Math.round((totalAccuracy / completedSessions.length) * 100) / 100;
  }

  /**
   * Generate chart data for daily records
   */
  private generateDailyChartData(sessions: any[]): {
    hourlyActivity: Array<{
      hour: number;
      sessions: number;
      duration: number;
    }>;
    performanceBySession: Array<{
      sessionId: string;
      startTime: string;
      score: number;
      accuracy: number;
      duration: number;
    }>;
  } {
    // Generate hourly activity data
    const hourlyActivity = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      sessions: 0,
      duration: 0,
    }));

    sessions.forEach(session => {
      const hour = session.startTime.getHours();
      hourlyActivity[hour].sessions++;
      hourlyActivity[hour].duration += Math.floor((session.actualDurationSeconds || 0) / 60);
    });

    // Generate performance by session data
    const performanceBySession = sessions
      .filter(s => s.sessionStatus === 'completed')
      .map(session => ({
        sessionId: session.id,
        startTime: session.startTime.toISOString(),
        score: session.totalScore,
        accuracy: session.achievedAccuracy?.toNumber() || 0,
        duration: Math.floor((session.actualDurationSeconds || 0) / 60),
      }));

    return {
      hourlyActivity,
      performanceBySession,
    };
  }

  /**
   * Generate chart data for weekly records
   */
  private generateWeeklyChartData(dailyStats: any[], userId: string): {
    dailyProgress: Array<{
      date: string;
      score: number;
      accuracy: number;
      duration: number;
    }>;
    weeklyComparison: {
      currentWeek: number;
      previousWeek: number;
      improvement: number;
    };
  } {
    const dailyProgress = dailyStats.map(day => ({
      date: day.date,
      score: Math.round(day.averageScore * 100) / 100,
      accuracy: Math.round(day.averageAccuracy * 100) / 100,
      duration: day.totalDuration,
    }));

    // Calculate weekly comparison (simplified)
    const currentWeekAvg = dailyStats.reduce((sum, day) => sum + day.averageScore, 0) / dailyStats.length;
    const previousWeekAvg = currentWeekAvg * (0.9 + Math.random() * 0.2); // Simulated previous week
    const improvement = currentWeekAvg > 0
      ? ((currentWeekAvg - previousWeekAvg) / previousWeekAvg) * 100
      : 0;

    return {
      dailyProgress,
      weeklyComparison: {
        currentWeek: Math.round(currentWeekAvg * 100) / 100,
        previousWeek: Math.round(previousWeekAvg * 100) / 100,
        improvement: Math.round(improvement * 100) / 100,
      },
    };
  }

  /**
   * Generate chart data for monthly records
   */
  private generateMonthlyChartData(monthlyData: any): {
    monthlyProgress: Array<{
      week: number;
      averageScore: number;
      totalDuration: number;
      completionRate: number;
    }>;
    dailyHeatmap: Array<{
      date: string;
      value: number;
    }>;
    goalProgress: {
      achieved: number;
      target: number;
      percentage: number;
    };
  } {
    const monthlyProgress = monthlyData.weeklyBreakdown.map((week: any) => ({
      week: week.week,
      averageScore: Math.round(week.averageScore * 100) / 100,
      totalDuration: week.totalDuration,
      completionRate: week.sessions > 0 ? Math.round((week.completedSessions / week.sessions) * 100) / 100 : 0,
    }));

    const dailyHeatmap = monthlyData.dailyActivity.map((day: any) => ({
      date: day.date,
      value: Math.min(100, (day.sessions * 20) + (day.duration * 2)), // Activity intensity calculation
    }));

    // Calculate goal progress
    const target = 100; // Example target
    const achieved = monthlyData.monthlyTotals.goalAchievementRate;
    const percentage = Math.min(100, (achieved / target) * 100);

    return {
      monthlyProgress,
      dailyHeatmap,
      goalProgress: {
        achieved: Math.round(achieved * 100) / 100,
        target,
        percentage: Math.round(percentage * 100) / 100,
      },
    };
  }
}
