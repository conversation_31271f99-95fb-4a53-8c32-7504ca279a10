import { PrismaClient, AdminUser } from '@prisma/client';
import { passwordService } from '@/utils/password';
import { jwtService, TokenPair } from '@/utils/jwt';
import { logger } from '@/utils/logger';
import { badRequest, conflict, notFound, unauthorized } from '@/middleware/errorHandler';
import crypto from 'crypto';

const prisma = new PrismaClient();

export interface AdminLoginData {
  username: string;
  password: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface AdminWithTokens {
  admin: Omit<AdminUser, 'passwordHash'>;
  tokens: TokenPair;
}

export interface CreateAdminData {
  username: string;
  password: string;
  email?: string;
  fullName?: string;
  role?: 'super_admin' | 'admin' | 'editor';
  permissions?: string[];
}

class AdminAuthService {
  /**
   * Authenticate admin with username and password
   */
  async login(loginData: AdminLoginData): Promise<AdminWithTokens> {
    try {
      // Find admin by username
      const admin = await prisma.adminUser.findUnique({
        where: { username: loginData.username },
      });

      if (!admin) {
        throw unauthorized('Invalid credentials');
      }

      if (!admin.isActive) {
        throw unauthorized('Admin account is inactive');
      }

      // Verify password
      const isValidPassword = await passwordService.verifyPassword(
        loginData.password,
        admin.passwordHash
      );

      if (!isValidPassword) {
        throw unauthorized('Invalid credentials');
      }

      // Generate tokens with admin-specific payload
      const tokens = jwtService.generateTokenPair({
        sub: admin.id,
        username: admin.username,
        email: admin.email ?? undefined,
        role: admin.role,
        permissions: admin.permissions as string[] || [],
        type: 'admin', // Mark as admin token
      });

      // Store refresh token
      await this.storeRefreshToken(admin.id, tokens.refreshToken, loginData.ipAddress, loginData.userAgent);

      // Update last login
      await prisma.adminUser.update({
        where: { id: admin.id },
        data: { lastLoginAt: new Date() },
      });

      logger.info('Admin logged in successfully', {
        adminId: admin.id,
        username: admin.username,
        role: admin.role,
        ipAddress: loginData.ipAddress,
      });

      // Remove password hash from response
      const { passwordHash: _, ...adminWithoutPassword } = admin;

      return {
        admin: adminWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('Admin login failed:', error);
      throw error;
    }
  }

  /**
   * Refresh admin access token
   */
  async refreshToken(refreshToken: string, ipAddress?: string): Promise<TokenPair> {
    try {
      // Verify refresh token
      const payload = jwtService.verifyRefreshToken(refreshToken);

      // Check if admin exists and is active
      const admin = await prisma.adminUser.findUnique({
        where: { id: payload.sub },
      });

      if (!admin || !admin.isActive) {
        throw unauthorized('Admin not found or inactive');
      }

      // Verify stored refresh token
      const tokenHash = this.hashToken(refreshToken);
      const storedToken = await prisma.adminToken.findFirst({
        where: {
          adminId: admin.id,
          tokenType: 'refresh',
          tokenHash,
          expiresAt: { gt: new Date() },
          usedAt: null,
        },
      });

      if (!storedToken) {
        throw unauthorized('Invalid or expired refresh token');
      }

      // Mark old token as used
      await prisma.adminToken.update({
        where: { id: storedToken.id },
        data: { usedAt: new Date() },
      });

      // Generate new token pair
      const newTokens = jwtService.generateTokenPair({
        sub: admin.id,
        username: admin.username,
        email: admin.email ?? undefined,
        role: admin.role,
        permissions: admin.permissions as string[] || [],
        type: 'admin',
      });

      // Store new refresh token
      await this.storeRefreshToken(admin.id, newTokens.refreshToken, ipAddress);

      logger.info('Admin token refreshed successfully', {
        adminId: admin.id,
        username: admin.username,
      });

      return newTokens;
    } catch (error) {
      logger.error('Admin token refresh failed:', error);
      throw error;
    }
  }

  /**
   * Logout admin (invalidate refresh token)
   */
  async logout(adminId: string, refreshToken?: string): Promise<void> {
    try {
      if (refreshToken) {
        const tokenHash = this.hashToken(refreshToken);
        await prisma.adminToken.updateMany({
          where: {
            adminId,
            tokenType: 'refresh',
            tokenHash,
            usedAt: null,
          },
          data: { usedAt: new Date() },
        });
      } else {
        // Invalidate all refresh tokens for this admin
        await prisma.adminToken.updateMany({
          where: {
            adminId,
            tokenType: 'refresh',
            usedAt: null,
          },
          data: { usedAt: new Date() },
        });
      }

      logger.info('Admin logged out successfully', { adminId });
    } catch (error) {
      logger.error('Admin logout failed:', error);
      throw error;
    }
  }

  /**
   * Create a new admin user
   */
  async createAdmin(adminData: CreateAdminData): Promise<Omit<AdminUser, 'passwordHash'>> {
    try {
      // Validate password strength
      const passwordValidation = passwordService.validatePasswordStrength(adminData.password);
      if (!passwordValidation.isValid) {
        throw badRequest('Password does not meet security requirements', {
          passwordErrors: passwordValidation.errors,
        });
      }

      // Check if username already exists
      const existingAdmin = await prisma.adminUser.findUnique({
        where: { username: adminData.username },
      });

      if (existingAdmin) {
        throw conflict('Admin username already exists');
      }

      // Hash password
      const passwordHash = await passwordService.hashPassword(adminData.password);

      // Set default permissions based on role
      const defaultPermissions = this.getDefaultPermissions(adminData.role || 'admin');

      // Create admin
      const admin = await prisma.adminUser.create({
        data: {
          username: adminData.username,
          passwordHash,
          email: adminData.email ?? null,
          fullName: adminData.fullName ?? null,
          role: adminData.role || 'admin',
          permissions: adminData.permissions || defaultPermissions,
        },
      });

      logger.info('Admin created successfully', {
        adminId: admin.id,
        username: admin.username,
        role: admin.role,
      });

      // Remove password hash from response
      const { passwordHash: _, ...adminWithoutPassword } = admin;
      return adminWithoutPassword;
    } catch (error) {
      logger.error('Admin creation failed:', error);
      throw error;
    }
  }

  /**
   * Get admin by ID
   */
  async getAdminById(adminId: string): Promise<Omit<AdminUser, 'passwordHash'> | null> {
    try {
      const admin = await prisma.adminUser.findUnique({
        where: { id: adminId },
      });

      if (!admin) {
        return null;
      }

      const { passwordHash: _, ...adminWithoutPassword } = admin;
      return adminWithoutPassword;
    } catch (error) {
      logger.error('Failed to get admin by ID:', error);
      throw error;
    }
  }

  /**
   * Store refresh token in database
   */
  private async storeRefreshToken(
    adminId: string,
    refreshToken: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    const tokenHash = this.hashToken(refreshToken);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    await prisma.adminToken.create({
      data: {
        adminId,
        tokenType: 'refresh',
        tokenHash,
        expiresAt,
        ipAddress: ipAddress?.substring(0, 45) || null,
        userAgent: userAgent?.substring(0, 500) || null,
      },
    });
  }

  /**
   * Hash token for secure storage
   */
  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Get default permissions based on role
   */
  private getDefaultPermissions(role: string): string[] {
    switch (role) {
    case 'super_admin':
      return ['read', 'write', 'delete', 'manage_users', 'manage_configs', 'view_logs'];
    case 'admin':
      return ['read', 'write', 'delete', 'view_logs'];
    case 'editor':
      return ['read', 'write'];
    default:
      return ['read'];
    }
  }
}

export const adminAuthService = new AdminAuthService();
export default adminAuthService;
