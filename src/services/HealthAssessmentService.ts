/**
 * Health Assessment Service
 * Handles business logic for health assessments
 */

import { HealthAssessmentRepository, CreateHealthAssessmentData, UpdateHealthAssessmentData, CreateAssessmentResultData, AssessmentFilters, AssessmentStats } from './database/HealthAssessmentRepository';
import { container, SERVICE_NAMES } from '@/core/Container';
import { logger } from '@/utils/logger';
import { HealthAssessment, AssessmentTemplate, AssessmentResult, AssessmentReport } from '@prisma/client';

export interface AssessmentTaskData {
  taskName: string;
  taskType: string;
  taskOrder: number;
  performanceData: any;
  gripStrengthData?: any;
  movementData?: any;
  startTime: Date;
  endTime: Date;
  accuracy?: number;
  notes?: string;
}

export interface AssessmentCompletionData {
  results: AssessmentTaskData[];
  overallNotes?: string;
  recommendations?: any;
}

export interface AssessmentReportData {
  reportType: string;
  title: string;
  summary: string;
  detailedAnalysis: string;
  recommendations: any;
  progressComparison?: any;
  visualData?: any;
  isPublic?: boolean;
}

export class HealthAssessmentService {
  private healthAssessmentRepository: HealthAssessmentRepository;

  constructor() {
    // Use lazy initialization to avoid circular dependency issues
    this.healthAssessmentRepository = new HealthAssessmentRepository();
  }

  /**
   * Create a new health assessment
   */
  public async createAssessment(data: CreateHealthAssessmentData): Promise<HealthAssessment> {
    try {
      logger.info('Creating health assessment', { userId: data.userId, type: data.assessmentType });

      // Validate template if provided
      if (data.templateId) {
        const template = await this.healthAssessmentRepository.getTemplateById(data.templateId);
        if (!template) {
          throw new Error('Assessment template not found');
        }
      }

      const assessment = await this.healthAssessmentRepository.createAssessment(data);

      logger.info('Health assessment created successfully', { assessmentId: assessment.id });
      return assessment;
    } catch (error) {
      logger.error('Failed to create health assessment', { error, data });
      throw error;
    }
  }

  /**
   * Get assessment by ID
   */
  public async getAssessmentById(id: string): Promise<HealthAssessment | null> {
    try {
      const assessment = await this.healthAssessmentRepository.getAssessmentById(id);

      if (!assessment) {
        logger.warn('Assessment not found', { assessmentId: id });
        return null;
      }

      return assessment;
    } catch (error) {
      logger.error('Failed to get assessment by ID', { error, id });
      throw error;
    }
  }

  /**
   * Get user assessments with pagination
   */
  public async getUserAssessments(
    userId: string,
    filters: AssessmentFilters = {},
    page: number = 1,
    limit: number = 10
  ): Promise<{ assessments: HealthAssessment[]; total: number; totalPages: number }> {
    try {
      const { assessments, total } = await this.healthAssessmentRepository.getUserAssessments(
        userId,
        filters,
        page,
        limit
      );

      const totalPages = Math.ceil(total / limit);

      return { assessments, total, totalPages };
    } catch (error) {
      logger.error('Failed to get user assessments', { error, userId, filters });
      throw error;
    }
  }

  /**
   * Start an assessment
   */
  public async startAssessment(id: string): Promise<HealthAssessment> {
    try {
      logger.info('Starting assessment', { assessmentId: id });

      const assessment = await this.healthAssessmentRepository.getAssessmentById(id);
      if (!assessment) {
        throw new Error('Assessment not found');
      }

      if (assessment.status !== 'pending') {
        throw new Error(`Cannot start assessment with status: ${assessment.status}`);
      }

      const updatedAssessment = await this.healthAssessmentRepository.startAssessment(id);

      logger.info('Assessment started successfully', { assessmentId: id });
      return updatedAssessment;
    } catch (error) {
      logger.error('Failed to start assessment', { error, id });
      throw error;
    }
  }

  /**
   * Complete an assessment with results
   */
  public async completeAssessment(id: string, completionData: AssessmentCompletionData): Promise<HealthAssessment> {
    try {
      logger.info('Completing assessment', { assessmentId: id, resultsCount: completionData.results.length });

      const assessment = await this.healthAssessmentRepository.getAssessmentById(id);
      if (!assessment) {
        throw new Error('Assessment not found');
      }

      if (assessment.status !== 'in_progress') {
        throw new Error(`Cannot complete assessment with status: ${assessment.status}`);
      }

      // Calculate overall scores and metrics
      const { overallScore, completionRate, totalDuration } = this.calculateAssessmentMetrics(
        completionData.results,
        assessment.template
      );

      // Add individual task results
      for (const taskData of completionData.results) {
        const taskScore = this.calculateTaskScore(taskData, assessment.template);

        await this.healthAssessmentRepository.addAssessmentResult({
          assessmentId: id,
          taskName: taskData.taskName,
          taskType: taskData.taskType,
          taskOrder: taskData.taskOrder,
          startTime: taskData.startTime,
          endTime: taskData.endTime,
          durationSeconds: Math.floor((taskData.endTime.getTime() - taskData.startTime.getTime()) / 1000),
          score: taskScore.score,
          maxScore: taskScore.maxScore,
          accuracy: taskData.accuracy,
          completionStatus: taskScore.score >= taskScore.maxScore * 0.7 ? 'completed' : 'partial',
          performanceData: taskData.performanceData,
          gripStrengthData: taskData.gripStrengthData,
          movementData: taskData.movementData,
          notes: taskData.notes,
        });
      }

      // Generate recommendations based on results
      const recommendations = this.generateRecommendations(completionData.results, assessment);

      // Complete the assessment
      const completedAssessment = await this.healthAssessmentRepository.completeAssessment(id, {
        overallScore,
        completionRate,
        durationSeconds: totalDuration,
        recommendations,
        notes: completionData.overallNotes,
      });

      logger.info('Assessment completed successfully', {
        assessmentId: id,
        overallScore,
        completionRate,
      });

      return completedAssessment;
    } catch (error) {
      logger.error('Failed to complete assessment', { error, id });
      throw error;
    }
  }

  /**
   * Update assessment
   */
  public async updateAssessment(id: string, data: UpdateHealthAssessmentData): Promise<HealthAssessment> {
    try {
      const assessment = await this.healthAssessmentRepository.updateAssessment(id, data);
      return assessment;
    } catch (error) {
      logger.error('Failed to update assessment', { error, id });
      throw error;
    }
  }

  /**
   * Get assessment templates
   */
  public async getAssessmentTemplates(templateType?: string): Promise<AssessmentTemplate[]> {
    try {
      const templates = await this.healthAssessmentRepository.getAssessmentTemplates(templateType);
      return templates;
    } catch (error) {
      logger.error('Failed to get assessment templates', { error, templateType });
      throw error;
    }
  }

  /**
   * Get assessment results
   */
  public async getAssessmentResults(assessmentId: string): Promise<AssessmentResult[]> {
    try {
      const results = await this.healthAssessmentRepository.getAssessmentResults(assessmentId);
      return results;
    } catch (error) {
      logger.error('Failed to get assessment results', { error, assessmentId });
      throw error;
    }
  }

  /**
   * Get user assessment statistics
   */
  public async getUserAssessmentStats(
    userId: string,
    assessmentType?: string,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<AssessmentStats> {
    try {
      const stats = await this.healthAssessmentRepository.getUserAssessmentStats(
        userId,
        assessmentType,
        dateFrom,
        dateTo
      );
      return stats;
    } catch (error) {
      logger.error('Failed to get user assessment stats', { error, userId });
      throw error;
    }
  }

  /**
   * Generate assessment report
   */
  public async generateAssessmentReport(
    assessmentId: string,
    reportData: AssessmentReportData,
    generatedBy?: string
  ): Promise<AssessmentReport> {
    try {
      logger.info('Generating assessment report', { assessmentId, reportType: reportData.reportType });

      const assessment = await this.healthAssessmentRepository.getAssessmentById(assessmentId);
      if (!assessment) {
        throw new Error('Assessment not found');
      }

      if (assessment.status !== 'completed') {
        throw new Error('Cannot generate report for incomplete assessment');
      }

      // Get assessment results for detailed analysis
      const results = await this.healthAssessmentRepository.getAssessmentResults(assessmentId);

      // Get progress comparison data
      const progressComparison = await this.healthAssessmentRepository.getAssessmentProgressComparison(
        assessment.userId,
        assessment.assessmentType
      );

      // Generate visual data for charts and graphs
      const visualData = this.generateVisualData(assessment, results, progressComparison);

      // Create comprehensive report data
      const comprehensiveReportData = {
        assessment,
        results,
        progressComparison,
        visualData,
        generatedAt: new Date(),
        ...reportData,
      };

      const report = await this.healthAssessmentRepository.createAssessmentReport({
        assessmentId,
        reportType: reportData.reportType,
        title: reportData.title,
        summary: reportData.summary,
        detailedAnalysis: reportData.detailedAnalysis,
        recommendations: reportData.recommendations,
        progressComparison: progressComparison,
        visualData: visualData,
        reportData: comprehensiveReportData,
        generatedBy,
        isPublic: reportData.isPublic || false,
      });

      logger.info('Assessment report generated successfully', { reportId: report.id });
      return report;
    } catch (error) {
      logger.error('Failed to generate assessment report', { error, assessmentId });
      throw error;
    }
  }

  /**
   * Get assessment reports
   */
  public async getAssessmentReports(assessmentId: string): Promise<AssessmentReport[]> {
    try {
      const reports = await this.healthAssessmentRepository.getAssessmentReports(assessmentId);
      return reports;
    } catch (error) {
      logger.error('Failed to get assessment reports', { error, assessmentId });
      throw error;
    }
  }

  /**
   * Delete assessment
   */
  public async deleteAssessment(id: string): Promise<HealthAssessment> {
    try {
      const assessment = await this.healthAssessmentRepository.deleteAssessment(id);
      return assessment;
    } catch (error) {
      logger.error('Failed to delete assessment', { error, id });
      throw error;
    }
  }

  /**
   * Calculate assessment metrics from task results
   */
  private calculateAssessmentMetrics(
    results: AssessmentTaskData[],
    template?: AssessmentTemplate | null
  ): { overallScore: number; completionRate: number; totalDuration: number } {
    if (results.length === 0) {
      return { overallScore: 0, completionRate: 0, totalDuration: 0 };
    }

    let totalScore = 0;
    let maxPossibleScore = 0;
    let completedTasks = 0;
    let totalDuration = 0;

    for (const taskData of results) {
      const taskScore = this.calculateTaskScore(taskData, template);
      totalScore += taskScore.score;
      maxPossibleScore += taskScore.maxScore;

      if (taskScore.score >= taskScore.maxScore * 0.7) {
        completedTasks++;
      }

      totalDuration += Math.floor((taskData.endTime.getTime() - taskData.startTime.getTime()) / 1000);
    }

    const overallScore = maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 100 : 0;
    const completionRate = results.length > 0 ? completedTasks / results.length : 0;

    return { overallScore, completionRate, totalDuration };
  }

  /**
   * Calculate score for individual task
   */
  private calculateTaskScore(
    taskData: AssessmentTaskData,
    template?: AssessmentTemplate | null
  ): { score: number; maxScore: number } {
    const baseMaxScore = 100;
    let score = 0;

    // Base scoring on accuracy if available
    if (taskData.accuracy !== undefined) {
      score += taskData.accuracy * 40; // 40% weight for accuracy
    }

    // Scoring based on grip strength consistency (if available)
    if (taskData.gripStrengthData) {
      const gripScore = this.calculateGripStrengthScore(taskData.gripStrengthData);
      score += gripScore * 30; // 30% weight for grip strength
    }

    // Scoring based on movement quality (if available)
    if (taskData.movementData) {
      const movementScore = this.calculateMovementScore(taskData.movementData);
      score += movementScore * 20; // 20% weight for movement quality
    }

    // Scoring based on task completion time
    const timeScore = this.calculateTimeScore(taskData, template);
    score += timeScore * 10; // 10% weight for completion time

    return { score: Math.min(score, baseMaxScore), maxScore: baseMaxScore };
  }

  /**
   * Calculate grip strength score
   */
  private calculateGripStrengthScore(gripStrengthData: any): number {
    if (!gripStrengthData || !Array.isArray(gripStrengthData.readings)) {
      return 0;
    }

    const readings = gripStrengthData.readings;
    if (readings.length === 0) return 0;

    // Calculate consistency (lower standard deviation = higher score)
    const mean = readings.reduce((sum: number, val: number) => sum + val, 0) / readings.length;
    const variance = readings.reduce((sum: number, val: number) => sum + Math.pow(val - mean, 2), 0) / readings.length;
    const stdDev = Math.sqrt(variance);

    // Normalize standard deviation to score (0-100)
    const consistencyScore = Math.max(0, 100 - (stdDev * 10));

    // Factor in average strength relative to target
    const targetStrength = gripStrengthData.target || mean;
    const strengthRatio = targetStrength > 0 ? Math.min(mean / targetStrength, 1.5) : 1;
    const strengthScore = Math.min(strengthRatio * 100, 100);

    // Combine consistency and strength scores
    return (consistencyScore * 0.6) + (strengthScore * 0.4);
  }

  /**
   * Calculate movement score
   */
  private calculateMovementScore(movementData: any): number {
    if (!movementData) return 0;

    let score = 0;
    let factors = 0;

    // Smoothness score
    if (movementData.smoothness !== undefined) {
      score += movementData.smoothness * 100;
      factors++;
    }

    // Precision score
    if (movementData.precision !== undefined) {
      score += movementData.precision * 100;
      factors++;
    }

    // Range of motion score
    if (movementData.rangeOfMotion !== undefined) {
      score += movementData.rangeOfMotion * 100;
      factors++;
    }

    // Tremor score (inverted - less tremor = higher score)
    if (movementData.tremorLevel !== undefined) {
      score += (1 - movementData.tremorLevel) * 100;
      factors++;
    }

    return factors > 0 ? score / factors : 0;
  }

  /**
   * Calculate time-based score
   */
  private calculateTimeScore(taskData: AssessmentTaskData, template?: AssessmentTemplate | null): number {
    const actualDuration = (taskData.endTime.getTime() - taskData.startTime.getTime()) / 1000;

    // If no template or expected duration, give neutral score
    if (!template || !template.estimatedDuration) {
      return 50;
    }

    const expectedDuration = template.estimatedDuration;
    const ratio = actualDuration / expectedDuration;

    // Optimal time range: 0.8x to 1.2x expected duration
    if (ratio >= 0.8 && ratio <= 1.2) {
      return 100;
    } else if (ratio < 0.8) {
      // Too fast might indicate rushing
      return Math.max(0, 100 - ((0.8 - ratio) * 200));
    } else {
      // Too slow
      return Math.max(0, 100 - ((ratio - 1.2) * 50));
    }
  }

  /**
   * Generate recommendations based on assessment results
   */
  private generateRecommendations(results: AssessmentTaskData[], assessment: HealthAssessment): any {
    const recommendations = {
      overall: [],
      specific: [],
      nextSteps: [],
      exercises: [],
    };

    // Analyze overall performance
    const averageAccuracy = results.reduce((sum, r) => sum + (r.accuracy || 0), 0) / results.length;

    if (averageAccuracy < 0.7) {
      recommendations.overall.push({
        type: 'accuracy',
        priority: 'high',
        message: 'Focus on improving movement accuracy through slower, more controlled exercises.',
        action: 'Practice precision-based tasks with visual feedback.',
      });
    }

    // Analyze grip strength patterns
    const gripStrengthTasks = results.filter(r => r.gripStrengthData);
    if (gripStrengthTasks.length > 0) {
      const avgConsistency = gripStrengthTasks.reduce((sum, r) => {
        const readings = r.gripStrengthData?.readings || [];
        if (readings.length === 0) return sum;
        const mean = readings.reduce((s: number, v: number) => s + v, 0) / readings.length;
        const variance = readings.reduce((s: number, v: number) => s + Math.pow(v - mean, 2), 0) / readings.length;
        return sum + Math.sqrt(variance);
      }, 0) / gripStrengthTasks.length;

      if (avgConsistency > 1.0) {
        recommendations.specific.push({
          type: 'grip_strength',
          priority: 'medium',
          message: 'Work on grip strength consistency through sustained holding exercises.',
          action: 'Practice isometric grip exercises for 10-15 seconds at a time.',
        });
      }
    }

    // Generate exercise recommendations
    recommendations.exercises = this.generateExerciseRecommendations(results, assessment);

    // Next steps based on assessment type
    recommendations.nextSteps = this.generateNextSteps(assessment, results);

    return recommendations;
  }

  /**
   * Generate exercise recommendations
   */
  private generateExerciseRecommendations(results: AssessmentTaskData[], assessment: HealthAssessment): any[] {
    const exercises = [];

    // Basic exercises for all assessment types
    exercises.push({
      name: 'Finger Flexion/Extension',
      description: 'Slowly open and close fingers, focusing on full range of motion.',
      duration: '5 minutes',
      frequency: 'Daily',
      difficulty: 'beginner',
    });

    // Add specific exercises based on performance
    const averageAccuracy = results.reduce((sum, r) => sum + (r.accuracy || 0), 0) / results.length;

    if (averageAccuracy < 0.8) {
      exercises.push({
        name: 'Precision Pinch Training',
        description: 'Practice picking up small objects with thumb and index finger.',
        duration: '10 minutes',
        frequency: '3 times per week',
        difficulty: 'intermediate',
      });
    }

    return exercises;
  }

  /**
   * Generate next steps recommendations
   */
  private generateNextSteps(assessment: HealthAssessment, results: AssessmentTaskData[]): any[] {
    const nextSteps = [];

    const overallPerformance = results.reduce((sum, r) => sum + (r.accuracy || 0), 0) / results.length;

    if (overallPerformance >= 0.8) {
      nextSteps.push({
        step: 'Progress to next difficulty level',
        timeframe: '1-2 weeks',
        description: 'Consider advancing to more challenging assessment tasks.',
      });
    } else if (overallPerformance >= 0.6) {
      nextSteps.push({
        step: 'Continue current level with focus areas',
        timeframe: '2-3 weeks',
        description: 'Maintain current difficulty while addressing specific weaknesses.',
      });
    } else {
      nextSteps.push({
        step: 'Reinforce foundational skills',
        timeframe: '3-4 weeks',
        description: 'Focus on basic movement patterns and strength building.',
      });
    }

    nextSteps.push({
      step: 'Schedule follow-up assessment',
      timeframe: '4-6 weeks',
      description: 'Track progress with a comprehensive re-assessment.',
    });

    return nextSteps;
  }

  /**
   * Generate visual data for reports
   */
  private generateVisualData(
    assessment: HealthAssessment,
    results: AssessmentResult[],
    progressComparison: any
  ): any {
    return {
      scoreChart: {
        type: 'bar',
        data: results.map(r => ({
          task: r.taskName,
          score: r.score.toNumber(),
          maxScore: r.maxScore.toNumber(),
        })),
      },
      progressChart: {
        type: 'line',
        data: progressComparison.previous.map((p: any, index: number) => ({
          assessment: index + 1,
          score: p.overallScore?.toNumber() || 0,
          completionRate: p.completionRate?.toNumber() || 0,
        })),
      },
      accuracyDistribution: {
        type: 'pie',
        data: results.map(r => ({
          task: r.taskName,
          accuracy: r.accuracy?.toNumber() || 0,
        })),
      },
      improvementMetrics: progressComparison.improvement,
    };
  }
}
