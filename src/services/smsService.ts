import { PrismaClient } from '@prisma/client';
import { logger, logSecurity } from '@/utils/logger';
import { passwordService } from '@/utils/password';
import { tooManyRequests, unauthorized } from '@/middleware/errorHandler';
import { createAliyunSmsProvider } from '@/services/providers/aliyunSmsProvider';
import { createTencentSmsProvider } from '@/services/providers/tencentSmsProvider';

const prisma = new PrismaClient();

export interface SmsVerificationData {
  phoneNumber: string;
  purpose: 'register' | 'login' | 'reset_password';
  ipAddress?: string | undefined;
  userAgent?: string | undefined;
}

export interface SmsVerificationResult {
  sent: boolean;
  expiresIn: number;
  retryAfter: number;
  remainingAttempts?: number;
}

export interface SmsVerifyData {
  phoneNumber: string;
  code: string;
  purpose: 'register' | 'login' | 'reset_password';
  ipAddress?: string;
}

class SmsService {
  private readonly codeLength = 6;
  private readonly expiryMinutes = 5;
  private readonly maxAttemptsPerCode = 3;
  private readonly maxCodesPerHour = 5;
  private readonly retryIntervalSeconds = 60;

  /**
   * Send SMS verification code
   */
  async sendVerificationCode(data: SmsVerificationData): Promise<SmsVerificationResult> {
    try {
      // Check rate limiting
      await this.checkRateLimit(data.phoneNumber, data.ipAddress);

      // Generate verification code
      const code = this.generateVerificationCode();
      const expiresAt = new Date(Date.now() + this.expiryMinutes * 60 * 1000);

      // Hash the code for storage
      const codeHash = await passwordService.hashPassword(code);

      // Store verification code in database
      await prisma.userToken.create({
        data: {
          tokenType: 'sms_verification',
          tokenHash: codeHash,
          expiresAt,
          phoneNumber: data.phoneNumber,
          purpose: data.purpose,
          ipAddress: data.ipAddress || null,
          userAgent: data.userAgent || null,
        },
      });

      // Send SMS (integrate with external service)
      const smsResult = await this.sendSms(data.phoneNumber, code, data.purpose);

      if (!smsResult.success) {
        throw new Error(`SMS sending failed: ${smsResult.error}`);
      }

      logger.info('SMS verification code sent', {
        phoneNumber: data.phoneNumber,
        purpose: data.purpose,
        ipAddress: data.ipAddress,
      });

      return {
        sent: true,
        expiresIn: this.expiryMinutes * 60,
        retryAfter: this.retryIntervalSeconds,
      };
    } catch (error) {
      logger.error('SMS verification code sending failed:', error);
      throw error;
    }
  }

  /**
   * Verify SMS code
   */
  async verifyCode(data: SmsVerifyData): Promise<boolean> {
    try {
      // Find the most recent unused verification code for this phone number and purpose
      const verificationRecord = await prisma.userToken.findFirst({
        where: {
          phoneNumber: data.phoneNumber,
          purpose: data.purpose,
          tokenType: 'sms_verification',
          usedAt: null,
          expiresAt: {
            gt: new Date(),
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      if (!verificationRecord) {
        // Enhanced logging for debugging
        const allRecords = await prisma.userToken.findMany({
          where: {
            phoneNumber: data.phoneNumber,
            tokenType: 'sms_verification',
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 5,
        });

        logSecurity('SMS verification attempt with no valid code', {
          phoneNumber: data.phoneNumber,
          purpose: data.purpose,
          ipAddress: data.ipAddress,
          totalRecords: allRecords.length,
          recentRecords: allRecords.map(r => ({
            id: r.id,
            purpose: r.purpose,
            createdAt: r.createdAt,
            expiresAt: r.expiresAt,
            usedAt: r.usedAt,
            attempts: r.attempts,
            isExpired: r.expiresAt < new Date(),
          })),
        });
        throw unauthorized('Invalid or expired verification code');
      }

      // Check if max attempts exceeded
      if (verificationRecord.attempts >= this.maxAttemptsPerCode) {
        logSecurity('SMS verification max attempts exceeded', {
          phoneNumber: data.phoneNumber,
          purpose: data.purpose,
          attempts: verificationRecord.attempts,
          ipAddress: data.ipAddress,
        });
        throw unauthorized('Maximum verification attempts exceeded');
      }

      // Verify the code
      const isValidCode = await passwordService.verifyPassword(data.code, verificationRecord.tokenHash);

      // Update attempts count
      await prisma.userToken.update({
        where: { id: verificationRecord.id },
        data: {
          attempts: verificationRecord.attempts + 1,
          ...(isValidCode && { usedAt: new Date() }),
        },
      });

      if (!isValidCode) {
        logSecurity('SMS verification failed - invalid code', {
          phoneNumber: data.phoneNumber,
          purpose: data.purpose,
          attempts: verificationRecord.attempts + 1,
          ipAddress: data.ipAddress,
        });
        throw unauthorized('Invalid verification code');
      }

      logger.info('SMS verification successful', {
        phoneNumber: data.phoneNumber,
        purpose: data.purpose,
        ipAddress: data.ipAddress,
      });

      return true;
    } catch (error) {
      logger.error('SMS verification failed:', error);
      throw error;
    }
  }

  /**
   * Check if phone number is already registered
   */
  async isPhoneRegistered(phoneNumber: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { phoneNumber },
      select: { id: true },
    });
    return !!user;
  }

  /**
   * Generate random verification code
   */
  private generateVerificationCode(): string {
    const min = Math.pow(10, this.codeLength - 1);
    const max = Math.pow(10, this.codeLength) - 1;
    return Math.floor(Math.random() * (max - min + 1) + min).toString();
  }

  /**
   * Check rate limiting for SMS sending
   */
  private async checkRateLimit(phoneNumber: string, ipAddress?: string): Promise<void> {
    // Test phone numbers that bypass rate limiting
    const testPhoneNumbers = [
      '+8618535158150',
      '18535158150',
      '+86 18535158150',
      '+86-18535158150',
    ];

    // Check if this is a test phone number (bypass rate limiting)
    const isTestPhone = testPhoneNumbers.some(testPhone =>
      phoneNumber.replace(/[\s\-]/g, '') === testPhone.replace(/[\s\-]/g, '')
    );

    if (isTestPhone) {
      logger.info('Rate limiting bypassed for test phone number', {
        phoneNumber,
        ipAddress,
      });
      return; // Skip all rate limiting checks
    }

    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    // Check phone number rate limit
    const phoneCodeCount = await prisma.userToken.count({
      where: {
        phoneNumber,
        tokenType: 'sms_verification',
        createdAt: {
          gte: oneHourAgo,
        },
      },
    });

    if (phoneCodeCount >= this.maxCodesPerHour) {
      logSecurity('SMS rate limit exceeded for phone number', {
        phoneNumber,
        count: phoneCodeCount,
        ipAddress,
      });
      throw tooManyRequests('Too many SMS requests for this phone number. Try again later.');
    }

    // Check IP address rate limit (if provided)
    if (ipAddress) {
      const ipCodeCount = await prisma.userToken.count({
        where: {
          ipAddress,
          tokenType: 'sms_verification',
          createdAt: {
            gte: oneHourAgo,
          },
        },
      });

      if (ipCodeCount >= this.maxCodesPerHour * 3) {
        // Allow more codes per IP for multiple users
        logSecurity('SMS rate limit exceeded for IP address', {
          ipAddress,
          count: ipCodeCount,
          phoneNumber,
        });
        throw tooManyRequests('Too many SMS requests from this IP address. Try again later.');
      }
    }

    // Check for recent code (prevent spam)
    const recentCode = await prisma.userToken.findFirst({
      where: {
        phoneNumber,
        tokenType: 'sms_verification',
        createdAt: {
          gte: new Date(Date.now() - this.retryIntervalSeconds * 1000),
        },
      },
    });

    if (recentCode) {
      throw tooManyRequests(`Please wait ${this.retryIntervalSeconds} seconds before requesting another code.`);
    }
  }

  /**
   * Send SMS using external service
   */
  private async sendSms(
    phoneNumber: string,
    code: string,
    purpose: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // For development, log the code instead of sending SMS
      if (process.env['NODE_ENV'] === 'development' || process.env['NODE_ENV'] === 'test') {
        logger.info('SMS Code (Development Mode)', {
          phoneNumber,
          code,
          purpose,
        });
        return { success: true };
      }

      // Try Aliyun SMS first
      const aliyunProvider = createAliyunSmsProvider();
      if (aliyunProvider) {
        logger.info('Using Aliyun SMS provider');
        const result = await aliyunProvider.sendSms({
          phoneNumber,
          templateParam: { code },
        });
        if (result.error) {
          return { success: result.success, error: result.error };
        }
        return { success: result.success };
      }

      // Try Tencent Cloud SMS as fallback
      const tencentProvider = createTencentSmsProvider();
      if (tencentProvider) {
        logger.info('Using Tencent SMS provider');
        const result = await tencentProvider.sendSms({
          phoneNumber,
          templateParamSet: [code],
        });
        if (result.error) {
          return { success: result.success, error: result.error };
        }
        return { success: result.success };
      }

      // No SMS provider configured
      logger.warn('No SMS service provider configured - using mock implementation');
      return { success: true };
    } catch (error) {
      logger.error('SMS sending error:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Clean up expired verification codes
   */
  async cleanupExpiredCodes(): Promise<void> {
    try {
      const result = await prisma.userToken.deleteMany({
        where: {
          tokenType: 'sms_verification',
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      logger.info('Cleaned up expired SMS verification codes', { deletedCount: result.count });
    } catch (error) {
      logger.error('SMS cleanup failed:', error);
    }
  }
}

export const smsService = new SmsService();
export default smsService;
