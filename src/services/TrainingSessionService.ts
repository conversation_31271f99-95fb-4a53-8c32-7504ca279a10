/**
 * Training Session Service
 * Implements the core business logic for training sessions as specified in the comprehensive guide
 */

import { Injectable } from '@/core/Container';
import { logger } from '@/utils/logger';
import { TrainingSessionRepository } from './database/TrainingSessionRepository';
import { container, SERVICE_NAMES } from '@/core/Container';
import {
  TrainingSession,
  TrainingRecord,
  RealTimeTrainingData,
  UserGoal,
  CreateTrainingSessionDTO,
  UpdateTrainingSessionDTO,
  RealTimeDataPoint,
  TodayTrainingData,
  WeeklyTrainingData,
  TrainingRecordsResponse,
} from '@/models/Training';

@Injectable('TrainingSessionService')
export class TrainingSessionService {
  public readonly serviceName = 'TrainingSessionService';
  private trainingSessionRepository: TrainingSessionRepository;

  constructor() {
    // Use lazy initialization to avoid circular dependency issues
    this.trainingSessionRepository = new TrainingSessionRepository();
  }

  /**
   * Create a new training session
   */
  public async createSession(data: CreateTrainingSessionDTO): Promise<TrainingSession> {
    try {
      logger.info('Creating training session', { userId: data.userId, planName: data.trainingPlanName });

      const sessionData = {
        userId: data.userId,
        rehabilitationPlanId: data.trainingPlanId,
        sessionType: data.trainingType,
        sessionName: data.trainingPlanName,
        startTime: new Date(),
        sessionData: {},
      };

      const session = await this.trainingSessionRepository.createSession(sessionData);

      // Convert Prisma model to our interface
      const result: TrainingSession = {
        id: session.id,
        userId: session.userId,
        trainingPlanId: session.rehabilitationPlanId,
        trainingPlanName: session.sessionName || data.trainingPlanName,
        trainingType: session.sessionType as any,
        startTime: session.startTime,
        endTime: session.endTime,
        durationSeconds: session.actualDurationSeconds || 0,
        status: session.sessionStatus as any,
        averageGripStrength: session.averageGripStrength?.toNumber() || 0,
        maxGripStrength: session.maxGripStrength?.toNumber() || 0,
        averageAccuracy: session.achievedAccuracy?.toNumber() || 0,
        totalScore: session.totalScore,
        completedActions: session.completedActions,
        completionRate: session.completedActions > 0 ? 1.0 : 0.0,
        metadata: session.sessionData || {},
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
      };

      logger.info('Training session created successfully', { sessionId: session.id });
      return result;
    } catch (error) {
      logger.error('Failed to create training session', { error, data });
      throw error;
    }
  }

  /**
   * Save real-time training data points
   */
  public async saveRealTimeData(sessionId: string, dataPoints: RealTimeDataPoint[]): Promise<void> {
    try {
      logger.info('Saving real-time training data', { sessionId, pointCount: dataPoints.length });

      await this.trainingSessionRepository.addDataPoints(sessionId, dataPoints);

      logger.info('Real-time training data saved successfully', { sessionId, savedPoints: dataPoints.length });
    } catch (error) {
      logger.error('Failed to save real-time training data', { error, sessionId });
      throw error;
    }
  }

  /**
   * Complete a training session and create training record
   */
  public async completeSession(sessionId: string, completionData: UpdateTrainingSessionDTO): Promise<TrainingRecord> {
    try {
      logger.info('Completing training session', { sessionId });

      // Save real-time data if provided
      if (completionData.dataPoints && completionData.dataPoints.length > 0) {
        await this.saveRealTimeData(sessionId, completionData.dataPoints);
      }

      // Update session with completion data
      const updateData = {
        endTime: completionData.endTime,
        actualDurationSeconds: completionData.duration,
        averageGripStrength: completionData.averageGripStrength,
        maxGripStrength: completionData.maxGripStrength,
        achievedAccuracy: completionData.averageAccuracy,
        totalScore: completionData.totalScore,
        completedActions: completionData.completedActions,
        sessionStatus: 'completed',
        endReason: 'completed_successfully',
      };

      const updatedSession = await this.trainingSessionRepository.completeSession(sessionId, updateData);

      // Create training record
      const record = await this.createTrainingRecord(updatedSession);

      // Update today's goal
      await this.updateTodayGoal(updatedSession.userId, record);

      logger.info('Training session completed successfully', { sessionId, recordId: record.id });
      return record;
    } catch (error) {
      logger.error('Failed to complete training session', { error, sessionId });
      throw error;
    }
  }

  /**
   * Create a training record from completed session
   */
  private async createTrainingRecord(session: any): Promise<TrainingRecord> {
    try {
      // Convert Prisma session to TrainingRecord format
      const record: TrainingRecord = {
        id: `record_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: session.userId,
        sessionId: session.id,
        trainingPlanId: session.rehabilitationPlanId,
        trainingPlanName: session.sessionName || 'Training Session',
        trainingDate: new Date(session.startTime.toDateString()),
        startTime: session.startTime,
        endTime: session.endTime,
        durationMinutes: Math.floor((session.actualDurationSeconds || 0) / 60),
        completionRate: session.completedActions > 0 ? 1.0 : 0.0,
        accuracyRate: session.achievedAccuracy?.toNumber() || 0,
        averageGripStrength: session.averageGripStrength?.toNumber() || 0,
        maxGripStrength: session.maxGripStrength?.toNumber() || 0,
        totalScore: session.totalScore || 0,
        completedActions: session.completedActions || 0,
        status: 'completed',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Note: In a full implementation, we would create a TrainingRecord table
      // For now, we return the constructed record
      logger.info('Training record created', { recordId: record.id, userId: record.userId });
      return record;
    } catch (error) {
      logger.error('Failed to create training record', { error, sessionId: session.id });
      throw error;
    }
  }

  /**
   * Update today's goal based on completed training
   */
  private async updateTodayGoal(userId: string, record: TrainingRecord): Promise<void> {
    try {
      const today = new Date().toDateString();

      // In a real implementation, this would fetch from database
      let goal = await this.getTodayGoal(userId);

      if (!goal) {
        // Create default goal if none exists
        goal = await this.createDefaultGoal(userId, today);
      }

      // Calculate today's total stats
      const todayStats = await this.calculateTodayStats(userId);

      // Update goal progress
      const updatedGoal: UserGoal = {
        ...goal,
        currentDuration: todayStats.totalDuration,
        currentActions: todayStats.totalActions,
        currentAccuracy: todayStats.averageAccuracy,
        isCompleted: todayStats.totalDuration >= goal.targetDuration &&
                     todayStats.totalActions >= goal.targetActions &&
                     todayStats.averageAccuracy >= goal.targetAccuracy,
        updatedAt: new Date(),
      };

      // In a real implementation, this would update in database
      // await this.userGoalRepository.update(updatedGoal);

      logger.info('Today goal updated', { userId, goalCompleted: updatedGoal.isCompleted });
    } catch (error) {
      logger.error('Failed to update today goal', { error, userId });
      throw error;
    }
  }

  /**
   * Calculate today's training statistics
   */
  public async calculateTodayStats(userId: string): Promise<{
    totalDuration: number;
    totalActions: number;
    averageAccuracy: number;
    totalScore: number;
  }> {
    try {
      const todaySessions = await this.trainingSessionRepository.getTodaySessions(userId);

      if (todaySessions.length === 0) {
        return {
          totalDuration: 0,
          totalActions: 0,
          averageAccuracy: 0,
          totalScore: 0,
        };
      }

      const totalDuration = Math.floor(
        todaySessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0) / 60
      );
      const totalActions = todaySessions.reduce((sum, s) => sum + s.completedActions, 0);
      const totalScore = todaySessions.reduce((sum, s) => sum + s.totalScore, 0);
      const averageAccuracy = todaySessions.reduce((sum, s) =>
        sum + (s.achievedAccuracy?.toNumber() || 0), 0
      ) / todaySessions.length;

      return {
        totalDuration,
        totalActions,
        averageAccuracy,
        totalScore,
      };
    } catch (error) {
      logger.error('Failed to calculate today stats', { error, userId });
      throw error;
    }
  }

  /**
   * Get today's training data for user
   */
  public async getTodayTrainingData(userId: string): Promise<TodayTrainingData> {
    try {
      const todaySessions = await this.trainingSessionRepository.getTodaySessions(userId);

      if (todaySessions.length === 0) {
        return {
          trainingDuration: 0,
          completedActions: 0,
          accuracy: 0.0,
          score: 0,
          averageGripStrength: 0.0,
          maxGripStrength: 0.0,
          trainingCount: 0,
        };
      }

      const maxGripStrength = Math.max(...todaySessions.map(s => s.maxGripStrength?.toNumber() || 0));

      return {
        trainingDuration: Math.floor(
          todaySessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0) / 60
        ),
        completedActions: todaySessions.reduce((sum, s) => sum + s.completedActions, 0),
        accuracy: todaySessions.reduce((sum, s) => sum + (s.achievedAccuracy?.toNumber() || 0), 0) / todaySessions.length,
        score: todaySessions.reduce((sum, s) => sum + s.totalScore, 0),
        averageGripStrength: todaySessions.reduce((sum, s) => sum + (s.averageGripStrength?.toNumber() || 0), 0) / todaySessions.length,
        maxGripStrength: maxGripStrength,
        trainingCount: todaySessions.length,
      };
    } catch (error) {
      logger.error('Failed to get today training data', { error, userId });
      throw error;
    }
  }

  /**
   * Get weekly training data for user
   */
  public async getWeeklyTrainingData(userId: string): Promise<WeeklyTrainingData[]> {
    try {
      const weekStart = this.getWeekStart(new Date());
      const weeklySessions = await this.trainingSessionRepository.getWeeklySessions(userId, weekStart);

      const dailyStats: WeeklyTrainingData[] = [];
      for (let i = 0; i < 7; i++) {
        const date = new Date(weekStart);
        date.setDate(date.getDate() + i);

        const daySessions = weeklySessions.filter(s =>
          s.startTime.toDateString() === date.toDateString()
        );

        dailyStats.push({
          date: date.toISOString().split('T')[0],
          trainingDuration: Math.floor(
            daySessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0) / 60
          ),
          completedActions: daySessions.reduce((sum, s) => sum + s.completedActions, 0),
          accuracy: daySessions.length > 0
            ? daySessions.reduce((sum, s) => sum + (s.achievedAccuracy?.toNumber() || 0), 0) / daySessions.length
            : 0,
          score: daySessions.reduce((sum, s) => sum + s.totalScore, 0),
          goalAchieved: daySessions.length > 0,
        });
      }

      return dailyStats;
    } catch (error) {
      logger.error('Failed to get weekly training data', { error, userId });
      throw error;
    }
  }

  // Helper methods
  private async getTodayGoal(userId: string): Promise<UserGoal | null> {
    // TODO: Implement UserGoal repository when needed
    return null;
  }

  private async createDefaultGoal(userId: string, goalDate: string): Promise<UserGoal> {
    // TODO: Implement UserGoal repository when needed
    return {
      id: `goal_${Date.now()}`,
      userId,
      goalDate: new Date(goalDate),
      targetDuration: 30,
      targetActions: 20,
      targetAccuracy: 0.8,
      currentDuration: 0,
      currentActions: 0,
      currentAccuracy: 0,
      isCompleted: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  private getWeekStart(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Monday as first day
    return new Date(d.setDate(diff));
  }

  private getWeekEnd(date: Date): Date {
    const weekStart = this.getWeekStart(date);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    return weekEnd;
  }
}

export const trainingSessionService = new TrainingSessionService();
