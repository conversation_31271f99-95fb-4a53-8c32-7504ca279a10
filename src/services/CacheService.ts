import { ICacheService } from './interfaces/IBaseService';
import { logger } from '@/utils/logger';
import { Injectable } from '@/core/Container';

/**
 * In-memory cache service implementation
 * For production, this should be replaced with Redis
 */
@Injectable('CacheService')
export class CacheService implements ICacheService {
  public readonly serviceName = 'CacheService';

  private cache = new Map<string, CacheEntry>();
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startCleanupInterval();
  }

  /**
   * Initialize the cache service
   */
  public async initialize(): Promise<void> {
    logger.info('Cache service initialized', {
      type: 'in-memory',
      cleanupInterval: '60s',
    });
  }

  /**
   * Cleanup resources
   */
  public async destroy(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    this.cache.clear();
    logger.info('Cache service destroyed');
  }

  /**
   * Get value from cache
   */
  public async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);

    if (!entry) {
      logger.debug('Cache miss', { key });
      return null;
    }

    // Check if entry has expired
    if (entry.expiresAt && Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      logger.debug('Cache entry expired', { key });
      return null;
    }

    logger.debug('Cache hit', { key });
    return entry.value as T;
  }

  /**
   * Set value in cache with optional TTL
   */
  public async set<T>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    const expiresAt = ttlSeconds ? Date.now() + (ttlSeconds * 1000) : undefined;

    this.cache.set(key, {
      value,
      createdAt: Date.now(),
      expiresAt,
    });

    logger.debug('Cache entry set', {
      key,
      ttlSeconds,
      expiresAt: expiresAt ? new Date(expiresAt).toISOString() : 'never',
    });
  }

  /**
   * Delete value from cache
   */
  public async delete(key: string): Promise<boolean> {
    const deleted = this.cache.delete(key);

    if (deleted) {
      logger.debug('Cache entry deleted', { key });
    }

    return deleted;
  }

  /**
   * Check if key exists in cache
   */
  public async exists(key: string): Promise<boolean> {
    const entry = this.cache.get(key);

    if (!entry) {
      return false;
    }

    // Check if entry has expired
    if (entry.expiresAt && Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Clear all cache entries
   */
  public async clear(): Promise<void> {
    const size = this.cache.size;
    this.cache.clear();

    logger.info('Cache cleared', { entriesRemoved: size });
  }

  /**
   * Get multiple values from cache
   */
  public async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const results: (T | null)[] = [];

    for (const key of keys) {
      results.push(await this.get<T>(key));
    }

    return results;
  }

  /**
   * Set multiple values in cache
   */
  public async mset<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
    for (const entry of entries) {
      await this.set(entry.key, entry.value, entry.ttl);
    }
  }

  /**
   * Get cache statistics
   */
  public getStats(): CacheStats {
    let expiredCount = 0;
    const now = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt && now > entry.expiresAt) {
        expiredCount++;
      }
    }

    return {
      totalEntries: this.cache.size,
      expiredEntries: expiredCount,
      activeEntries: this.cache.size - expiredCount,
      memoryUsage: this.estimateMemoryUsage(),
    };
  }

  /**
   * Get all cache keys (for debugging)
   */
  public getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Start cleanup interval to remove expired entries
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries();
    }, 60000); // Run every minute
  }

  /**
   * Remove expired entries from cache
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt && now > entry.expiresAt) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      logger.debug('Cache cleanup completed', {
        removedEntries: removedCount,
        remainingEntries: this.cache.size,
      });
    }
  }

  /**
   * Estimate memory usage (rough calculation)
   */
  private estimateMemoryUsage(): number {
    let totalSize = 0;

    for (const [key, entry] of this.cache.entries()) {
      totalSize += key.length * 2; // UTF-16 characters
      totalSize += JSON.stringify(entry.value).length * 2;
      totalSize += 24; // Approximate overhead for entry object
    }

    return totalSize;
  }
}

/**
 * Cache entry interface
 */
interface CacheEntry {
  value: any;
  createdAt: number;
  expiresAt?: number;
}

/**
 * Cache statistics interface
 */
export interface CacheStats {
  totalEntries: number;
  expiredEntries: number;
  activeEntries: number;
  memoryUsage: number; // in bytes
}

/**
 * Cache key builder utility
 */
export class CacheKeyBuilder {
  private parts: string[] = [];

  public static create(): CacheKeyBuilder {
    return new CacheKeyBuilder();
  }

  public add(part: string | number): CacheKeyBuilder {
    this.parts.push(String(part));
    return this;
  }

  public addUser(userId: string): CacheKeyBuilder {
    return this.add(`user:${userId}`);
  }

  public addResource(resource: string, id?: string): CacheKeyBuilder {
    if (id) {
      return this.add(`${resource}:${id}`);
    }
    return this.add(resource);
  }

  public addTimestamp(timestamp?: Date): CacheKeyBuilder {
    const ts = timestamp || new Date();
    return this.add(ts.toISOString().split('T')[0]); // YYYY-MM-DD
  }

  public build(): string {
    return this.parts.join(':');
  }
}

// Export singleton instance
export const cacheService = new CacheService();
