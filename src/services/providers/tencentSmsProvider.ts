import { logger } from '@/utils/logger';

export interface TencentSmsConfig {
  secretId: string;
  secretKey: string;
  sdkAppId: string;
  signName: string;
  templateId: string;
  region?: string;
}

export interface TencentSmsParams {
  phoneNumber: string;
  templateParamSet: string[];
}

export interface TencentSmsResult {
  success: boolean;
  error?: string;
  requestId?: string;
  serialNo?: string;
}

/**
 * Tencent Cloud SMS Service Provider
 * 
 * To use this provider, install the Tencent Cloud SDK:
 * npm install tencentcloud-sdk-nodejs
 * 
 * Environment variables required:
 * - TENCENT_SMS_SECRET_ID
 * - TENCENT_SMS_SECRET_KEY
 * - TENCENT_SMS_SDK_APP_ID
 * - TENCENT_SMS_SIGN_NAME
 * - TENCENT_SMS_TEMPLATE_ID
 */
export class TencentSmsProvider {
  constructor(private readonly config: TencentSmsConfig) {
    // Config is stored as a private readonly property
  }

  /**
   * Send SMS using Tencent Cloud SMS service
   */
  async sendSms(params: TencentSmsParams): Promise<TencentSmsResult> {
    try {
      // Note: This is a placeholder implementation
      // To use actual Tencent Cloud SMS, uncomment and install the required packages:
      
      /*
      const tencentcloud = require('tencentcloud-sdk-nodejs');
      
      const SmsClient = tencentcloud.sms.v20210111.Client;
      
      const clientConfig = {
        credential: {
          secretId: this.config.secretId,
          secretKey: this.config.secretKey,
        },
        region: this.config.region || 'ap-beijing',
        profile: {
          httpProfile: {
            endpoint: 'sms.tencentcloudapi.com',
          },
        },
      };
      
      const client = new SmsClient(clientConfig);
      
      const sendSmsParams = {
        PhoneNumberSet: [params.phoneNumber],
        SmsSdkAppId: this.config.sdkAppId,
        SignName: this.config.signName,
        TemplateId: this.config.templateId,
        TemplateParamSet: params.templateParamSet,
      };
      
      const response = await client.SendSms(sendSmsParams);
      
      if (response.SendStatusSet && response.SendStatusSet[0]) {
        const status = response.SendStatusSet[0];
        
        if (status.Code === 'Ok') {
          logger.info('Tencent SMS sent successfully', {
            phoneNumber: params.phoneNumber,
            requestId: response.RequestId,
            serialNo: status.SerialNo,
          });
          
          return {
            success: true,
            requestId: response.RequestId,
            serialNo: status.SerialNo,
          };
        } else {
          logger.error('Tencent SMS sending failed', {
            phoneNumber: params.phoneNumber,
            code: status.Code,
            message: status.Message,
          });
          
          return {
            success: false,
            error: `${status.Code}: ${status.Message}`,
          };
        }
      } else {
        return {
          success: false,
          error: 'No response from Tencent SMS service',
        };
      }
      */

      // For now, return a mock success response
      logger.warn('Tencent SMS provider not configured - using mock implementation', {
        phoneNumber: params.phoneNumber,
        templateParamSet: params.templateParamSet,
        signName: this.config.signName,
        templateId: this.config.templateId,
      });

      return {
        success: true,
        requestId: 'mock-request-id',
        serialNo: 'mock-serial-no',
      };
    } catch (error) {
      logger.error('Tencent SMS error:', error);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Validate phone number format for China
   */
  static validateChinesePhoneNumber(phoneNumber: string): boolean {
    // Chinese mobile phone number format: +86 followed by 1[3-9]xxxxxxxxx
    const chinesePhoneRegex = /^\+861[3-9]\d{9}$/;
    return chinesePhoneRegex.test(phoneNumber);
  }

  /**
   * Format phone number for Tencent SMS (ensure +86 prefix)
   */
  static formatPhoneNumber(phoneNumber: string): string {
    if (phoneNumber.startsWith('+86')) {
      return phoneNumber;
    }
    if (phoneNumber.startsWith('86')) {
      return '+' + phoneNumber;
    }
    if (phoneNumber.match(/^1[3-9]\d{9}$/)) {
      return '+86' + phoneNumber;
    }
    return phoneNumber;
  }
}

/**
 * Factory function to create Tencent SMS provider from environment variables
 */
export function createTencentSmsProvider(): TencentSmsProvider | null {
  const secretId = process.env['TENCENT_SMS_SECRET_ID'];
  const secretKey = process.env['TENCENT_SMS_SECRET_KEY'];
  const sdkAppId = process.env['TENCENT_SMS_SDK_APP_ID'];
  const signName = process.env['TENCENT_SMS_SIGN_NAME'];
  const templateId = process.env['TENCENT_SMS_TEMPLATE_ID'];

  if (!secretId || !secretKey || !sdkAppId || !signName || !templateId) {
    logger.warn('Tencent SMS configuration incomplete, provider not available');
    return null;
  }

  const config: TencentSmsConfig = {
    secretId,
    secretKey,
    sdkAppId,
    signName,
    templateId,
  };

  if (process.env['TENCENT_SMS_REGION']) {
    config.region = process.env['TENCENT_SMS_REGION'];
  }

  return new TencentSmsProvider(config);
}
