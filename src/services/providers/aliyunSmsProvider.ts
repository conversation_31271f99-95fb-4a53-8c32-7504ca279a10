import { logger } from '@/utils/logger';

export interface AliyunSmsConfig {
  accessKeyId: string;
  accessKeySecret: string;
  signName: string;
  templateCode: string;
  endpoint?: string;
}

export interface AliyunSmsParams {
  phoneNumber: string;
  templateParam: Record<string, string>;
}

export interface AliyunSmsResult {
  success: boolean;
  error?: string;
  requestId?: string;
}

/**
 * Aliyun SMS Service Provider
 * 
 * To use this provider, install the Aliyun SDK:
 * npm install @alicloud/dysmsapi20170525 @alicloud/openapi-client
 * 
 * Environment variables required:
 * - ALI_SMS_ACCESS_KEY_ID
 * - ALI_SMS_ACCESS_KEY_SECRET
 * - ALI_SMS_SIGN_NAME
 * - ALI_SMS_TEMPLATE_CODE
 */
export class AliyunSmsProvider {
  constructor(private readonly config: AliyunSmsConfig) {
    // Config is stored as a private readonly property
  }

  /**
   * Send SMS using <PERSON>yun SMS service
   */
  async sendSms(params: AliyunSmsParams): Promise<AliyunSmsResult> {
    try {
      // Note: This is a placeholder implementation
      // To use actual Aliyun SMS, uncomment and install the required packages:
      
      /*
      import Dysmsapi20170525, * as $Dysmsapi20170525 from '@alicloud/dysmsapi20170525';
      import * as $OpenApi from '@alicloud/openapi-client';
      import * as $Util from '@alicloud/tea-util';

      const config = new $OpenApi.Config({
        accessKeyId: this.config.accessKeyId,
        accessKeySecret: this.config.accessKeySecret,
      });
      
      config.endpoint = this.config.endpoint || 'dysmsapi.aliyuncs.com';
      
      const client = new Dysmsapi20170525(config);
      
      const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
        phoneNumbers: params.phoneNumber,
        signName: this.config.signName,
        templateCode: this.config.templateCode,
        templateParam: JSON.stringify(params.templateParam),
      });
      
      const runtime = new $Util.RuntimeOptions({});
      const response = await client.sendSmsWithOptions(sendSmsRequest, runtime);
      
      if (response.body.code === 'OK') {
        logger.info('Aliyun SMS sent successfully', {
          phoneNumber: params.phoneNumber,
          requestId: response.body.requestId,
        });
        
        return {
          success: true,
          requestId: response.body.requestId,
        };
      } else {
        logger.error('Aliyun SMS sending failed', {
          phoneNumber: params.phoneNumber,
          code: response.body.code,
          message: response.body.message,
        });
        
        return {
          success: false,
          error: `${response.body.code}: ${response.body.message}`,
        };
      }
      */

      // For now, return a mock success response
      logger.warn('Aliyun SMS provider not configured - using mock implementation', {
        phoneNumber: params.phoneNumber,
        templateParam: params.templateParam,
        signName: this.config.signName,
        templateCode: this.config.templateCode,
      });

      return {
        success: true,
        requestId: 'mock-request-id',
      };
    } catch (error) {
      logger.error('Aliyun SMS error:', error);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Validate phone number format for China
   */
  static validateChinesePhoneNumber(phoneNumber: string): boolean {
    // Chinese mobile phone number format: 1[3-9]xxxxxxxxx
    const chinesePhoneRegex = /^1[3-9]\d{9}$/;
    return chinesePhoneRegex.test(phoneNumber.replace(/^\+86/, ''));
  }

  /**
   * Format phone number for Aliyun SMS (remove +86 prefix if present)
   */
  static formatPhoneNumber(phoneNumber: string): string {
    return phoneNumber.replace(/^\+86/, '');
  }
}

/**
 * Factory function to create Aliyun SMS provider from environment variables
 */
export function createAliyunSmsProvider(): AliyunSmsProvider | null {
  const accessKeyId = process.env['ALI_SMS_ACCESS_KEY_ID'];
  const accessKeySecret = process.env['ALI_SMS_ACCESS_KEY_SECRET'];
  const signName = process.env['ALI_SMS_SIGN_NAME'];
  const templateCode = process.env['ALI_SMS_TEMPLATE_CODE'];

  if (!accessKeyId || !accessKeySecret || !signName || !templateCode) {
    logger.warn('Aliyun SMS configuration incomplete, provider not available');
    return null;
  }

  const config: AliyunSmsConfig = {
    accessKeyId,
    accessKeySecret,
    signName,
    templateCode,
  };

  if (process.env['ALI_SMS_ENDPOINT']) {
    config.endpoint = process.env['ALI_SMS_ENDPOINT'];
  }

  return new AliyunSmsProvider(config);
}
