/**
 * Health Assessment Repository
 * Handles database operations for health assessments
 */

import { BaseRepository } from './BaseRepository';
import { logger } from '@/utils/logger';
import { HealthAssessment, AssessmentTemplate, AssessmentResult, AssessmentReport, Prisma } from '@prisma/client';

export interface CreateHealthAssessmentData {
  userId: string;
  templateId?: string;
  assessmentType: string;
  assessmentName: string;
  description?: string;
  assessmentData?: any;
  conductedBy?: string;
}

export interface UpdateHealthAssessmentData {
  status?: string;
  startTime?: Date;
  endTime?: Date;
  durationSeconds?: number;
  overallScore?: number;
  completionRate?: number;
  assessmentData?: any;
  recommendations?: any;
  notes?: string;
  reviewedBy?: string;
  reviewedAt?: Date;
}

export interface CreateAssessmentResultData {
  assessmentId: string;
  taskName: string;
  taskType: string;
  taskOrder: number;
  startTime: Date;
  endTime?: Date;
  durationSeconds?: number;
  score: number;
  maxScore: number;
  accuracy?: number;
  completionStatus: string;
  performanceData?: any;
  gripStrengthData?: any;
  movementData?: any;
  errorAnalysis?: any;
  notes?: string;
}

export interface AssessmentFilters {
  userId?: string;
  assessmentType?: string;
  status?: string;
  dateFrom?: Date;
  dateTo?: Date;
  templateId?: string;
}

export interface AssessmentStats {
  totalAssessments: number;
  completedAssessments: number;
  averageScore: number;
  averageCompletionRate: number;
  averageDuration: number;
  improvementTrend: number;
}

export class HealthAssessmentRepository extends BaseRepository {
  /**
   * Create a new health assessment
   */
  public async createAssessment(data: CreateHealthAssessmentData): Promise<HealthAssessment> {
    try {
      logger.info('Creating health assessment', { userId: data.userId, type: data.assessmentType });

      const assessment = await this.prisma.healthAssessment.create({
        data: {
          userId: data.userId,
          templateId: data.templateId,
          assessmentType: data.assessmentType,
          assessmentName: data.assessmentName,
          description: data.description,
          assessmentData: data.assessmentData,
          conductedBy: data.conductedBy,
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          template: true,
        },
      });

      logger.info('Health assessment created successfully', { assessmentId: assessment.id });
      return assessment;
    } catch (error) {
      logger.error('Failed to create health assessment', { error, data });
      throw error;
    }
  }

  /**
   * Get assessment by ID
   */
  public async getAssessmentById(id: string): Promise<HealthAssessment | null> {
    try {
      const assessment = await this.prisma.healthAssessment.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          template: true,
          results: {
            orderBy: { taskOrder: 'asc' },
          },
          reports: {
            orderBy: { generatedAt: 'desc' },
          },
        },
      });

      return assessment;
    } catch (error) {
      logger.error('Failed to get assessment by ID', { error, id });
      throw error;
    }
  }

  /**
   * Get assessments for a user
   */
  public async getUserAssessments(
    userId: string,
    filters: AssessmentFilters = {},
    page: number = 1,
    limit: number = 10
  ): Promise<{ assessments: HealthAssessment[]; total: number }> {
    try {
      const where: Prisma.HealthAssessmentWhereInput = {
        userId,
        isActive: true,
        ...(filters.assessmentType && { assessmentType: filters.assessmentType }),
        ...(filters.status && { status: filters.status }),
        ...(filters.templateId && { templateId: filters.templateId }),
        ...(filters.dateFrom || filters.dateTo) && {
          createdAt: {
            ...(filters.dateFrom && { gte: filters.dateFrom }),
            ...(filters.dateTo && { lte: filters.dateTo }),
          },
        },
      };

      const [assessments, total] = await Promise.all([
        this.prisma.healthAssessment.findMany({
          where,
          include: {
            template: true,
            results: {
              select: {
                id: true,
                taskName: true,
                score: true,
                maxScore: true,
                completionStatus: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
        }),
        this.prisma.healthAssessment.count({ where }),
      ]);

      return { assessments, total };
    } catch (error) {
      logger.error('Failed to get user assessments', { error, userId, filters });
      throw error;
    }
  }

  /**
   * Update assessment
   */
  public async updateAssessment(id: string, data: UpdateHealthAssessmentData): Promise<HealthAssessment> {
    try {
      logger.info('Updating health assessment', { assessmentId: id });

      const assessment = await this.prisma.healthAssessment.update({
        where: { id },
        data,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          template: true,
          results: true,
        },
      });

      logger.info('Health assessment updated successfully', { assessmentId: id });
      return assessment;
    } catch (error) {
      logger.error('Failed to update health assessment', { error, id, data });
      throw error;
    }
  }

  /**
   * Start assessment (update status and start time)
   */
  public async startAssessment(id: string): Promise<HealthAssessment> {
    return this.updateAssessment(id, {
      status: 'in_progress',
      startTime: new Date(),
    });
  }

  /**
   * Complete assessment
   */
  public async completeAssessment(
    id: string,
    completionData: {
      overallScore: number;
      completionRate: number;
      durationSeconds: number;
      recommendations?: any;
      notes?: string;
    }
  ): Promise<HealthAssessment> {
    return this.updateAssessment(id, {
      status: 'completed',
      endTime: new Date(),
      overallScore: completionData.overallScore,
      completionRate: completionData.completionRate,
      durationSeconds: completionData.durationSeconds,
      recommendations: completionData.recommendations,
      notes: completionData.notes,
    });
  }

  /**
   * Add assessment result
   */
  public async addAssessmentResult(data: CreateAssessmentResultData): Promise<AssessmentResult> {
    try {
      logger.info('Adding assessment result', { assessmentId: data.assessmentId, taskName: data.taskName });

      const result = await this.prisma.assessmentResult.create({
        data,
      });

      logger.info('Assessment result added successfully', { resultId: result.id });
      return result;
    } catch (error) {
      logger.error('Failed to add assessment result', { error, data });
      throw error;
    }
  }

  /**
   * Get assessment results
   */
  public async getAssessmentResults(assessmentId: string): Promise<AssessmentResult[]> {
    try {
      const results = await this.prisma.assessmentResult.findMany({
        where: { assessmentId },
        orderBy: { taskOrder: 'asc' },
      });

      return results;
    } catch (error) {
      logger.error('Failed to get assessment results', { error, assessmentId });
      throw error;
    }
  }

  /**
   * Get assessment statistics for a user
   */
  public async getUserAssessmentStats(
    userId: string,
    assessmentType?: string,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<AssessmentStats> {
    try {
      const where: Prisma.HealthAssessmentWhereInput = {
        userId,
        isActive: true,
        status: 'completed',
        ...(assessmentType && { assessmentType }),
        ...(dateFrom || dateTo) && {
          createdAt: {
            ...(dateFrom && { gte: dateFrom }),
            ...(dateTo && { lte: dateTo }),
          },
        },
      };

      const assessments = await this.prisma.healthAssessment.findMany({
        where,
        select: {
          overallScore: true,
          completionRate: true,
          durationSeconds: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'asc' },
      });

      const totalAssessments = assessments.length;
      const completedAssessments = assessments.filter(a => a.completionRate && a.completionRate >= 1.0).length;

      if (totalAssessments === 0) {
        return {
          totalAssessments: 0,
          completedAssessments: 0,
          averageScore: 0,
          averageCompletionRate: 0,
          averageDuration: 0,
          improvementTrend: 0,
        };
      }

      const averageScore = assessments.reduce((sum, a) => sum + (a.overallScore?.toNumber() || 0), 0) / totalAssessments;
      const averageCompletionRate = assessments.reduce((sum, a) => sum + (a.completionRate?.toNumber() || 0), 0) / totalAssessments;
      const averageDuration = assessments.reduce((sum, a) => sum + (a.durationSeconds || 0), 0) / totalAssessments;

      // Calculate improvement trend (comparing first half vs second half)
      let improvementTrend = 0;
      if (totalAssessments >= 4) {
        const midPoint = Math.floor(totalAssessments / 2);
        const firstHalf = assessments.slice(0, midPoint);
        const secondHalf = assessments.slice(midPoint);

        const firstHalfAvg = firstHalf.reduce((sum, a) => sum + (a.overallScore?.toNumber() || 0), 0) / firstHalf.length;
        const secondHalfAvg = secondHalf.reduce((sum, a) => sum + (a.overallScore?.toNumber() || 0), 0) / secondHalf.length;

        improvementTrend = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;
      }

      return {
        totalAssessments,
        completedAssessments,
        averageScore,
        averageCompletionRate,
        averageDuration,
        improvementTrend,
      };
    } catch (error) {
      logger.error('Failed to get user assessment stats', { error, userId });
      throw error;
    }
  }

  /**
   * Get assessment templates
   */
  public async getAssessmentTemplates(
    templateType?: string,
    isActive: boolean = true
  ): Promise<AssessmentTemplate[]> {
    try {
      const templates = await this.prisma.assessmentTemplate.findMany({
        where: {
          isActive,
          ...(templateType && { templateType }),
        },
        orderBy: [
          { templateType: 'asc' },
          { difficultyLevel: 'asc' },
          { templateName: 'asc' },
        ],
      });

      return templates;
    } catch (error) {
      logger.error('Failed to get assessment templates', { error, templateType });
      throw error;
    }
  }

  /**
   * Get template by ID
   */
  public async getTemplateById(id: string): Promise<AssessmentTemplate | null> {
    try {
      const template = await this.prisma.assessmentTemplate.findUnique({
        where: { id },
      });

      return template;
    } catch (error) {
      logger.error('Failed to get template by ID', { error, id });
      throw error;
    }
  }

  /**
   * Create assessment report
   */
  public async createAssessmentReport(data: {
    assessmentId: string;
    reportType: string;
    title: string;
    summary: string;
    detailedAnalysis: string;
    recommendations: any;
    progressComparison?: any;
    visualData?: any;
    reportData: any;
    generatedBy?: string;
    isPublic?: boolean;
  }): Promise<AssessmentReport> {
    try {
      logger.info('Creating assessment report', { assessmentId: data.assessmentId, reportType: data.reportType });

      const report = await this.prisma.assessmentReport.create({
        data,
      });

      logger.info('Assessment report created successfully', { reportId: report.id });
      return report;
    } catch (error) {
      logger.error('Failed to create assessment report', { error, data });
      throw error;
    }
  }

  /**
   * Get assessment reports
   */
  public async getAssessmentReports(assessmentId: string): Promise<AssessmentReport[]> {
    try {
      const reports = await this.prisma.assessmentReport.findMany({
        where: { assessmentId },
        orderBy: { generatedAt: 'desc' },
      });

      return reports;
    } catch (error) {
      logger.error('Failed to get assessment reports', { error, assessmentId });
      throw error;
    }
  }

  /**
   * Get latest assessment report
   */
  public async getLatestAssessmentReport(assessmentId: string, reportType?: string): Promise<AssessmentReport | null> {
    try {
      const report = await this.prisma.assessmentReport.findFirst({
        where: {
          assessmentId,
          ...(reportType && { reportType }),
        },
        orderBy: { generatedAt: 'desc' },
      });

      return report;
    } catch (error) {
      logger.error('Failed to get latest assessment report', { error, assessmentId, reportType });
      throw error;
    }
  }

  /**
   * Delete assessment (soft delete)
   */
  public async deleteAssessment(id: string): Promise<HealthAssessment> {
    try {
      logger.info('Deleting health assessment', { assessmentId: id });

      const assessment = await this.prisma.healthAssessment.update({
        where: { id },
        data: { isActive: false },
      });

      logger.info('Health assessment deleted successfully', { assessmentId: id });
      return assessment;
    } catch (error) {
      logger.error('Failed to delete health assessment', { error, id });
      throw error;
    }
  }

  /**
   * Get assessment progress comparison
   */
  public async getAssessmentProgressComparison(
    userId: string,
    assessmentType: string,
    limit: number = 5
  ): Promise<{
    current: HealthAssessment | null;
    previous: HealthAssessment[];
    improvement: {
      scoreImprovement: number;
      completionRateImprovement: number;
      durationImprovement: number;
    };
  }> {
    try {
      const assessments = await this.prisma.healthAssessment.findMany({
        where: {
          userId,
          assessmentType,
          status: 'completed',
          isActive: true,
        },
        orderBy: { createdAt: 'desc' },
        take: limit + 1,
        select: {
          id: true,
          overallScore: true,
          completionRate: true,
          durationSeconds: true,
          createdAt: true,
        },
      });

      if (assessments.length === 0) {
        return {
          current: null,
          previous: [],
          improvement: {
            scoreImprovement: 0,
            completionRateImprovement: 0,
            durationImprovement: 0,
          },
        };
      }

      const [current, ...previous] = assessments;

      let improvement = {
        scoreImprovement: 0,
        completionRateImprovement: 0,
        durationImprovement: 0,
      };

      if (previous.length > 0) {
        const previousAssessment = previous[0];
        const currentScore = current.overallScore?.toNumber() || 0;
        const previousScore = previousAssessment.overallScore?.toNumber() || 0;
        const currentCompletionRate = current.completionRate?.toNumber() || 0;
        const previousCompletionRate = previousAssessment.completionRate?.toNumber() || 0;
        const currentDuration = current.durationSeconds || 0;
        const previousDuration = previousAssessment.durationSeconds || 0;

        improvement = {
          scoreImprovement: previousScore > 0 ? ((currentScore - previousScore) / previousScore) * 100 : 0,
          completionRateImprovement: previousCompletionRate > 0 ? ((currentCompletionRate - previousCompletionRate) / previousCompletionRate) * 100 : 0,
          durationImprovement: previousDuration > 0 ? ((previousDuration - currentDuration) / previousDuration) * 100 : 0, // Negative means faster completion
        };
      }

      return {
        current: current as any,
        previous: previous as any,
        improvement,
      };
    } catch (error) {
      logger.error('Failed to get assessment progress comparison', { error, userId, assessmentType });
      throw error;
    }
  }
}
