/**
 * Training Session Repository
 * Handles database operations for training sessions
 */

import { TrainingSession, TrainingDataPoint } from '@prisma/client';
import { BaseRepository, PaginationOptions, FilterOptions } from './BaseRepository';
import { Injectable } from '@/core/Container';
import { logger } from '@/utils/logger';

export interface TrainingSessionWithRelations extends TrainingSession {
  user?: any;
  device?: any;
  rehabilitationPlan?: any;
  dataPoints?: TrainingDataPoint[];
  gameRecords?: any[];
}

export interface CreateTrainingSessionData {
  userId: string;
  deviceId?: string;
  rehabilitationPlanId?: string;
  sessionType: string;
  sessionName?: string;
  startTime: Date;
  plannedDurationSeconds?: number;
  targetActions?: number;
  targetAccuracy?: number;
  sessionData?: any;
  notes?: string;
}

export interface UpdateTrainingSessionData {
  endTime?: Date;
  actualDurationSeconds?: number;
  completedActions?: number;
  achievedAccuracy?: number;
  averageGripStrength?: number;
  maxGripStrength?: number;
  minGripStrength?: number;
  totalScore?: number;
  sessionStatus?: string;
  endReason?: string;
  sessionData?: any;
  notes?: string;
}

export interface TrainingSessionFilters {
  userId?: string;
  deviceId?: string;
  rehabilitationPlanId?: string;
  sessionType?: string;
  sessionStatus?: string;
  startDate?: Date;
  endDate?: Date;
}

@Injectable('TrainingSessionRepository')
export class TrainingSessionRepository extends BaseRepository<TrainingSession> {
  constructor() {
    super('trainingSession');
  }

  /**
   * Create a new training session
   */
  public async createSession(data: CreateTrainingSessionData): Promise<TrainingSessionWithRelations> {
    try {
      const session = await this.create({
        ...data,
        sessionStatus: 'active',
        completedActions: 0,
        totalScore: 0,
      });

      return this.findSessionById(session.id);
    } catch (error) {
      logger.error('Failed to create training session', { error, data });
      throw error;
    }
  }

  /**
   * Find training session by ID with relations
   */
  public async findSessionById(id: string): Promise<TrainingSessionWithRelations | null> {
    return this.findById(id, {
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true,
            avatarUrl: true,
          },
        },
        device: {
          select: {
            id: true,
            deviceName: true,
            deviceType: true,
            deviceStatus: true,
          },
        },
        rehabilitationPlan: {
          select: {
            id: true,
            planName: true,
            recoveryStage: true,
            difficultyLevel: true,
          },
        },
        dataPoints: {
          orderBy: { timestamp: 'asc' },
          take: 1000, // Limit data points for performance
        },
        gameRecords: {
          select: {
            id: true,
            gameType: true,
            score: true,
            level: true,
          },
        },
      },
    });
  }

  /**
   * Find user's training sessions with filters
   */
  public async findUserSessions(
    userId: string,
    filters: TrainingSessionFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<any> {
    const where: any = { userId };

    if (filters.deviceId) where.deviceId = filters.deviceId;
    if (filters.rehabilitationPlanId) where.rehabilitationPlanId = filters.rehabilitationPlanId;
    if (filters.sessionType) where.sessionType = filters.sessionType;
    if (filters.sessionStatus) where.sessionStatus = filters.sessionStatus;

    if (filters.startDate || filters.endDate) {
      where.startTime = {};
      if (filters.startDate) where.startTime.gte = filters.startDate;
      if (filters.endDate) where.startTime.lte = filters.endDate;
    }

    return this.findMany(
      {
        where,
        include: {
          device: {
            select: {
              id: true,
              deviceName: true,
              deviceType: true,
            },
          },
          rehabilitationPlan: {
            select: {
              id: true,
              planName: true,
              recoveryStage: true,
            },
          },
        },
      },
      pagination
    );
  }

  /**
   * Update training session
   */
  public async updateSession(
    id: string,
    data: UpdateTrainingSessionData
  ): Promise<TrainingSessionWithRelations> {
    try {
      await this.update(id, data);
      return this.findSessionById(id);
    } catch (error) {
      logger.error('Failed to update training session', { error, id, data });
      throw error;
    }
  }

  /**
   * Complete training session
   */
  public async completeSession(
    id: string,
    data: UpdateTrainingSessionData
  ): Promise<TrainingSessionWithRelations> {
    try {
      const updateData = {
        ...data,
        sessionStatus: 'completed',
        endTime: data.endTime || new Date(),
      };

      await this.update(id, updateData);
      return this.findSessionById(id);
    } catch (error) {
      logger.error('Failed to complete training session', { error, id, data });
      throw error;
    }
  }

  /**
   * Get today's training sessions for user
   */
  public async getTodaySessions(userId: string): Promise<TrainingSession[]> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const result = await this.findMany({
        where: {
          userId,
          startTime: {
            gte: today,
            lt: tomorrow,
          },
        },
      });

      return result.data;
    } catch (error) {
      logger.error('Failed to get today sessions', { error, userId });
      throw error;
    }
  }

  /**
   * Get weekly training sessions for user
   */
  public async getWeeklySessions(userId: string, weekStart?: Date): Promise<TrainingSession[]> {
    try {
      const start = weekStart || this.getWeekStart(new Date());
      const end = new Date(start);
      end.setDate(end.getDate() + 7);

      const result = await this.findMany({
        where: {
          userId,
          startTime: {
            gte: start,
            lt: end,
          },
        },
      });

      return result.data;
    } catch (error) {
      logger.error('Failed to get weekly sessions', { error, userId, weekStart });
      throw error;
    }
  }

  /**
   * Get training statistics for user
   */
  public async getUserStats(userId: string, startDate?: Date, endDate?: Date): Promise<any> {
    try {
      const where: any = { userId, sessionStatus: 'completed' };

      if (startDate || endDate) {
        where.startTime = {};
        if (startDate) where.startTime.gte = startDate;
        if (endDate) where.startTime.lte = endDate;
      }

      const sessions = await this.getModel().findMany({
        where,
        select: {
          actualDurationSeconds: true,
          completedActions: true,
          achievedAccuracy: true,
          totalScore: true,
          averageGripStrength: true,
          maxGripStrength: true,
          sessionType: true,
          startTime: true,
        },
      });

      if (sessions.length === 0) {
        return {
          totalSessions: 0,
          totalDuration: 0,
          totalActions: 0,
          averageAccuracy: 0,
          averageScore: 0,
          averageGripStrength: 0,
          maxGripStrength: 0,
        };
      }

      const totalDuration = sessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0);
      const totalActions = sessions.reduce((sum, s) => sum + s.completedActions, 0);
      const totalScore = sessions.reduce((sum, s) => sum + s.totalScore, 0);
      const accuracySum = sessions.reduce((sum, s) => sum + (s.achievedAccuracy?.toNumber() || 0), 0);
      const gripStrengthSum = sessions.reduce((sum, s) => sum + (s.averageGripStrength?.toNumber() || 0), 0);
      const maxGripStrength = Math.max(...sessions.map(s => s.maxGripStrength?.toNumber() || 0));

      return {
        totalSessions: sessions.length,
        totalDuration: Math.floor(totalDuration / 60), // Convert to minutes
        totalActions,
        averageAccuracy: accuracySum / sessions.length,
        averageScore: totalScore / sessions.length,
        averageGripStrength: gripStrengthSum / sessions.length,
        maxGripStrength,
        byType: this.groupStatsByType(sessions),
      };
    } catch (error) {
      logger.error('Failed to get user stats', { error, userId, startDate, endDate });
      throw error;
    }
  }

  /**
   * Add training data points to session
   */
  public async addDataPoints(sessionId: string, dataPoints: any[]): Promise<void> {
    try {
      const data = dataPoints.map((point, index) => ({
        sessionId,
        timestamp: point.timestamp,
        sequenceNumber: point.sequenceNumber || index + 1,
        gripStrength: point.gripStrength,
        fingerPositions: point.fingerPositions,
        handPose: point.handPose,
        accuracy: point.accuracy,
        actionType: point.actionType,
        gestureType: point.gestureType,
        confidence: point.confidence,
        sensorData: point.sensorData,
        processedData: point.processedData,
        qualityScore: point.qualityScore,
        anomalyFlags: point.anomalyFlags,
      }));

      await this.prisma.trainingDataPoint.createMany({ data });
      logger.info('Training data points added successfully', { 
        sessionId, 
        count: dataPoints.length 
      });
    } catch (error) {
      logger.error('Failed to add training data points', { error, sessionId });
      throw error;
    }
  }

  /**
   * Helper method to get week start
   */
  private getWeekStart(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Monday as first day
    d.setDate(diff);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Helper method to group stats by session type
   */
  private groupStatsByType(sessions: any[]): any {
    const byType: any = {};

    sessions.forEach(session => {
      const type = session.sessionType;
      if (!byType[type]) {
        byType[type] = {
          sessions: 0,
          totalDuration: 0,
          totalActions: 0,
          totalScore: 0,
          accuracySum: 0,
        };
      }

      byType[type].sessions++;
      byType[type].totalDuration += session.actualDurationSeconds || 0;
      byType[type].totalActions += session.completedActions;
      byType[type].totalScore += session.totalScore;
      byType[type].accuracySum += session.achievedAccuracy?.toNumber() || 0;
    });

    // Calculate averages
    Object.keys(byType).forEach(type => {
      const stats = byType[type];
      stats.averageDuration = Math.floor(stats.totalDuration / stats.sessions / 60); // minutes
      stats.averageActions = stats.totalActions / stats.sessions;
      stats.averageScore = stats.totalScore / stats.sessions;
      stats.averageAccuracy = stats.accuracySum / stats.sessions;
    });

    return byType;
  }

  /**
   * Get daily training records for a user
   */
  public async getDailyTrainingRecords(userId: string, date: Date): Promise<{
    sessions: TrainingSession[];
    totalDuration: number;
    averageScore: number;
    completionRate: number;
    totalSessions: number;
    completedSessions: number;
  }> {
    try {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      const sessions = await this.getModel().findMany({
        where: {
          userId,
          startTime: {
            gte: startOfDay,
            lte: endOfDay,
          },
        },
        include: {
          rehabilitationPlan: {
            select: {
              planName: true,
              recoveryStage: true,
            },
          },
        },
        orderBy: { startTime: 'asc' },
      });

      const totalSessions = sessions.length;
      const completedSessions = sessions.filter(s => s.sessionStatus === 'completed').length;
      const completionRate = totalSessions > 0 ? completedSessions / totalSessions : 0;

      const completedSessionsData = sessions.filter(s => s.sessionStatus === 'completed');
      const totalDuration = Math.floor(
        completedSessionsData.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0) / 60
      );
      const averageScore = completedSessionsData.length > 0
        ? completedSessionsData.reduce((sum, s) => sum + s.totalScore, 0) / completedSessionsData.length
        : 0;

      return {
        sessions,
        totalDuration,
        averageScore,
        completionRate,
        totalSessions,
        completedSessions,
      };
    } catch (error) {
      logger.error('Failed to get daily training records', { error, userId, date });
      throw error;
    }
  }

  /**
   * Get weekly training statistics
   */
  public async getWeeklyTrainingStats(userId: string, weekStart: Date): Promise<{
    dailyStats: Array<{
      date: string;
      sessions: number;
      completedSessions: number;
      totalDuration: number;
      averageScore: number;
      averageAccuracy: number;
    }>;
    weeklyTotals: {
      totalSessions: number;
      completedSessions: number;
      totalDuration: number;
      averageScore: number;
      averageAccuracy: number;
      completionRate: number;
    };
    trends: {
      scoreImprovement: number;
      accuracyImprovement: number;
      consistencyImprovement: number;
    };
  }> {
    try {
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekEnd.getDate() + 7);

      const sessions = await this.getModel().findMany({
        where: {
          userId,
          startTime: {
            gte: weekStart,
            lt: weekEnd,
          },
        },
        orderBy: { startTime: 'asc' },
      });

      // Calculate daily statistics
      const dailyStats = [];
      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(weekStart);
        currentDate.setDate(currentDate.getDate() + i);

        const dayStart = new Date(currentDate);
        dayStart.setHours(0, 0, 0, 0);

        const dayEnd = new Date(currentDate);
        dayEnd.setHours(23, 59, 59, 999);

        const daySessions = sessions.filter(s =>
          s.startTime >= dayStart && s.startTime <= dayEnd
        );

        const completedDaySessions = daySessions.filter(s => s.sessionStatus === 'completed');

        dailyStats.push({
          date: currentDate.toISOString().split('T')[0],
          sessions: daySessions.length,
          completedSessions: completedDaySessions.length,
          totalDuration: Math.floor(
            completedDaySessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0) / 60
          ),
          averageScore: completedDaySessions.length > 0
            ? completedDaySessions.reduce((sum, s) => sum + s.totalScore, 0) / completedDaySessions.length
            : 0,
          averageAccuracy: completedDaySessions.length > 0
            ? completedDaySessions.reduce((sum, s) => sum + (s.achievedAccuracy?.toNumber() || 0), 0) / completedDaySessions.length
            : 0,
        });
      }

      // Calculate weekly totals
      const completedSessions = sessions.filter(s => s.sessionStatus === 'completed');
      const weeklyTotals = {
        totalSessions: sessions.length,
        completedSessions: completedSessions.length,
        totalDuration: Math.floor(
          completedSessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0) / 60
        ),
        averageScore: completedSessions.length > 0
          ? completedSessions.reduce((sum, s) => sum + s.totalScore, 0) / completedSessions.length
          : 0,
        averageAccuracy: completedSessions.length > 0
          ? completedSessions.reduce((sum, s) => sum + (s.achievedAccuracy?.toNumber() || 0), 0) / completedSessions.length
          : 0,
        completionRate: sessions.length > 0 ? completedSessions.length / sessions.length : 0,
      };

      // Calculate trends (simplified)
      const trends = this.calculateWeeklyTrends(dailyStats);

      return {
        dailyStats,
        weeklyTotals,
        trends,
      };
    } catch (error) {
      logger.error('Failed to get weekly training stats', { error, userId, weekStart });
      throw error;
    }
  }

  /**
   * Get monthly training statistics
   */
  public async getMonthlyTrainingStats(userId: string, year: number, month: number): Promise<{
    monthlyTotals: {
      totalSessions: number;
      completedSessions: number;
      totalDuration: number;
      averageScore: number;
      averageAccuracy: number;
      completionRate: number;
      goalAchievementRate: number;
    };
    weeklyBreakdown: Array<{
      week: number;
      sessions: number;
      completedSessions: number;
      totalDuration: number;
      averageScore: number;
    }>;
    dailyActivity: Array<{
      date: string;
      sessions: number;
      duration: number;
      score: number;
    }>;
    improvements: {
      scoreImprovement: number;
      accuracyImprovement: number;
      consistencyImprovement: number;
    };
    recommendations: string[];
  }> {
    try {
      const monthStart = new Date(year, month - 1, 1);
      const monthEnd = new Date(year, month, 0, 23, 59, 59, 999);

      const sessions = await this.getModel().findMany({
        where: {
          userId,
          startTime: {
            gte: monthStart,
            lte: monthEnd,
          },
        },
        orderBy: { startTime: 'asc' },
      });

      // Calculate monthly totals
      const completedSessions = sessions.filter(s => s.sessionStatus === 'completed');
      const monthlyTotals = {
        totalSessions: sessions.length,
        completedSessions: completedSessions.length,
        totalDuration: Math.floor(
          completedSessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0) / 60
        ),
        averageScore: completedSessions.length > 0
          ? completedSessions.reduce((sum, s) => sum + s.totalScore, 0) / completedSessions.length
          : 0,
        averageAccuracy: completedSessions.length > 0
          ? completedSessions.reduce((sum, s) => sum + (s.achievedAccuracy?.toNumber() || 0), 0) / completedSessions.length
          : 0,
        completionRate: sessions.length > 0 ? completedSessions.length / sessions.length : 0,
        goalAchievementRate: this.calculateGoalAchievementRate(completedSessions),
      };

      // Calculate weekly breakdown
      const weeklyBreakdown = this.calculateWeeklyBreakdown(sessions, monthStart);

      // Calculate daily activity
      const dailyActivity = this.calculateDailyActivity(sessions, monthStart, monthEnd);

      // Calculate improvements
      const improvements = this.calculateMonthlyImprovements(sessions);

      // Generate recommendations
      const recommendations = this.generateMonthlyRecommendations(monthlyTotals, improvements);

      return {
        monthlyTotals,
        weeklyBreakdown,
        dailyActivity,
        improvements,
        recommendations,
      };
    } catch (error) {
      logger.error('Failed to get monthly training stats', { error, userId, year, month });
      throw error;
    }
  }

  /**
   * Clean up old training data (older than 30 days)
   */
  public async cleanupOldData(userId?: string): Promise<{
    deletedSessions: number;
    deletedDataPoints: number;
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 30);

      logger.info('Starting data cleanup', { cutoffDate, userId });

      const where: any = {
        startTime: {
          lt: cutoffDate,
        },
      };

      if (userId) {
        where.userId = userId;
      }

      // Get sessions to be deleted for counting
      const sessionsToDelete = await this.getModel().findMany({
        where,
        select: { id: true },
      });

      // Delete training sessions (this will cascade to related data)
      const deleteResult = await this.getModel().deleteMany({
        where,
      });

      logger.info('Data cleanup completed', {
        deletedSessions: deleteResult.count,
        cutoffDate,
        userId,
      });

      return {
        deletedSessions: deleteResult.count,
        deletedDataPoints: deleteResult.count * 10, // Estimated data points
      };
    } catch (error) {
      logger.error('Failed to cleanup old data', { error, userId });
      throw error;
    }
  }

  /**
   * Calculate weekly trends from daily stats
   */
  private calculateWeeklyTrends(dailyStats: any[]): {
    scoreImprovement: number;
    accuracyImprovement: number;
    consistencyImprovement: number;
  } {
    if (dailyStats.length < 2) {
      return {
        scoreImprovement: 0,
        accuracyImprovement: 0,
        consistencyImprovement: 0,
      };
    }

    const firstHalf = dailyStats.slice(0, Math.floor(dailyStats.length / 2));
    const secondHalf = dailyStats.slice(Math.floor(dailyStats.length / 2));

    const firstHalfAvgScore = firstHalf.reduce((sum, day) => sum + day.averageScore, 0) / firstHalf.length;
    const secondHalfAvgScore = secondHalf.reduce((sum, day) => sum + day.averageScore, 0) / secondHalf.length;

    const firstHalfAvgAccuracy = firstHalf.reduce((sum, day) => sum + day.averageAccuracy, 0) / firstHalf.length;
    const secondHalfAvgAccuracy = secondHalf.reduce((sum, day) => sum + day.averageAccuracy, 0) / secondHalf.length;

    const scoreImprovement = firstHalfAvgScore > 0
      ? ((secondHalfAvgScore - firstHalfAvgScore) / firstHalfAvgScore) * 100
      : 0;

    const accuracyImprovement = firstHalfAvgAccuracy > 0
      ? ((secondHalfAvgAccuracy - firstHalfAvgAccuracy) / firstHalfAvgAccuracy) * 100
      : 0;

    // Calculate consistency as variance in daily performance
    const scoreVariance = this.calculateVariance(dailyStats.map(d => d.averageScore));
    const consistencyImprovement = Math.max(0, 100 - scoreVariance);

    return {
      scoreImprovement,
      accuracyImprovement,
      consistencyImprovement,
    };
  }

  /**
   * Calculate goal achievement rate
   */
  private calculateGoalAchievementRate(sessions: any[]): number {
    if (sessions.length === 0) return 0;

    // Simplified goal achievement calculation
    // In a real implementation, this would check against specific goals
    const highPerformanceSessions = sessions.filter(s => s.totalScore >= 80);
    return (highPerformanceSessions.length / sessions.length) * 100;
  }

  /**
   * Calculate weekly breakdown for monthly stats
   */
  private calculateWeeklyBreakdown(sessions: any[], monthStart: Date): Array<{
    week: number;
    sessions: number;
    completedSessions: number;
    totalDuration: number;
    averageScore: number;
  }> {
    const weeks = [];
    const monthEnd = new Date(monthStart.getFullYear(), monthStart.getMonth() + 1, 0);
    const totalDays = monthEnd.getDate();
    const weeksInMonth = Math.ceil(totalDays / 7);

    for (let week = 1; week <= weeksInMonth; week++) {
      const weekStart = new Date(monthStart);
      weekStart.setDate((week - 1) * 7 + 1);

      const weekEnd = new Date(monthStart);
      weekEnd.setDate(Math.min(week * 7, totalDays));
      weekEnd.setHours(23, 59, 59, 999);

      const weekSessions = sessions.filter(s =>
        s.startTime >= weekStart && s.startTime <= weekEnd
      );

      const completedWeekSessions = weekSessions.filter(s => s.sessionStatus === 'completed');

      weeks.push({
        week,
        sessions: weekSessions.length,
        completedSessions: completedWeekSessions.length,
        totalDuration: Math.floor(
          completedWeekSessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0) / 60
        ),
        averageScore: completedWeekSessions.length > 0
          ? completedWeekSessions.reduce((sum, s) => sum + s.totalScore, 0) / completedWeekSessions.length
          : 0,
      });
    }

    return weeks;
  }

  /**
   * Calculate daily activity for monthly stats
   */
  private calculateDailyActivity(sessions: any[], monthStart: Date, monthEnd: Date): Array<{
    date: string;
    sessions: number;
    duration: number;
    score: number;
  }> {
    const dailyActivity = [];
    const currentDate = new Date(monthStart);

    while (currentDate <= monthEnd) {
      const dayStart = new Date(currentDate);
      dayStart.setHours(0, 0, 0, 0);

      const dayEnd = new Date(currentDate);
      dayEnd.setHours(23, 59, 59, 999);

      const daySessions = sessions.filter(s =>
        s.startTime >= dayStart && s.startTime <= dayEnd
      );

      const completedDaySessions = daySessions.filter(s => s.sessionStatus === 'completed');

      dailyActivity.push({
        date: currentDate.toISOString().split('T')[0],
        sessions: daySessions.length,
        duration: Math.floor(
          completedDaySessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0) / 60
        ),
        score: completedDaySessions.length > 0
          ? completedDaySessions.reduce((sum, s) => sum + s.totalScore, 0) / completedDaySessions.length
          : 0,
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dailyActivity;
  }

  /**
   * Calculate monthly improvements
   */
  private calculateMonthlyImprovements(sessions: any[]): {
    scoreImprovement: number;
    accuracyImprovement: number;
    consistencyImprovement: number;
  } {
    if (sessions.length < 2) {
      return {
        scoreImprovement: 0,
        accuracyImprovement: 0,
        consistencyImprovement: 0,
      };
    }

    const completedSessions = sessions.filter(s => s.sessionStatus === 'completed');
    if (completedSessions.length < 2) {
      return {
        scoreImprovement: 0,
        accuracyImprovement: 0,
        consistencyImprovement: 0,
      };
    }

    // Sort by date
    completedSessions.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());

    const firstWeek = completedSessions.slice(0, Math.ceil(completedSessions.length * 0.25));
    const lastWeek = completedSessions.slice(-Math.ceil(completedSessions.length * 0.25));

    const firstWeekAvgScore = firstWeek.reduce((sum, s) => sum + s.totalScore, 0) / firstWeek.length;
    const lastWeekAvgScore = lastWeek.reduce((sum, s) => sum + s.totalScore, 0) / lastWeek.length;

    const firstWeekAvgAccuracy = firstWeek.reduce((sum, s) => sum + (s.achievedAccuracy?.toNumber() || 0), 0) / firstWeek.length;
    const lastWeekAvgAccuracy = lastWeek.reduce((sum, s) => sum + (s.achievedAccuracy?.toNumber() || 0), 0) / lastWeek.length;

    const scoreImprovement = firstWeekAvgScore > 0
      ? ((lastWeekAvgScore - firstWeekAvgScore) / firstWeekAvgScore) * 100
      : 0;

    const accuracyImprovement = firstWeekAvgAccuracy > 0
      ? ((lastWeekAvgAccuracy - firstWeekAvgAccuracy) / firstWeekAvgAccuracy) * 100
      : 0;

    // Calculate consistency as inverse of score variance
    const scores = completedSessions.map(s => s.totalScore);
    const scoreVariance = this.calculateVariance(scores);
    const consistencyImprovement = Math.max(0, 100 - scoreVariance);

    return {
      scoreImprovement,
      accuracyImprovement,
      consistencyImprovement,
    };
  }

  /**
   * Generate monthly recommendations
   */
  private generateMonthlyRecommendations(totals: any, improvements: any): string[] {
    const recommendations = [];

    // Completion rate recommendations
    if (totals.completionRate < 0.6) {
      recommendations.push('Focus on consistency - try to complete more training sessions');
    } else if (totals.completionRate > 0.9) {
      recommendations.push('Excellent consistency! Consider increasing training intensity');
    }

    // Score improvement recommendations
    if (improvements.scoreImprovement > 10) {
      recommendations.push('Outstanding progress! Your scores have improved significantly');
    } else if (improvements.scoreImprovement < -5) {
      recommendations.push('Consider reviewing your training approach or reducing difficulty');
    }

    // Goal achievement recommendations
    if (totals.goalAchievementRate < 50) {
      recommendations.push('Work on achieving your training goals more consistently');
    } else if (totals.goalAchievementRate > 80) {
      recommendations.push('Great goal achievement! Consider setting more challenging targets');
    }

    // Duration recommendations
    if (totals.totalDuration < 300) { // Less than 5 hours per month
      recommendations.push('Try to increase your training duration for better results');
    }

    // Accuracy recommendations
    if (totals.averageAccuracy < 0.7) {
      recommendations.push('Focus on accuracy over speed in your training sessions');
    }

    return recommendations;
  }

  /**
   * Calculate variance for consistency metrics
   */
  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;

    return Math.sqrt(variance);
  }
}

export const trainingSessionRepository = new TrainingSessionRepository();
