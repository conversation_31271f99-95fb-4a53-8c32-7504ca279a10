/**
 * Base Repository Class
 * Provides common database operations using Repository Pattern
 */

import { PrismaClient } from '@prisma/client';
import { databaseService } from './DatabaseService';
import { logger } from '@/utils/logger';

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface FilterOptions {
  where?: any;
  include?: any;
  select?: any;
}

export abstract class BaseRepository<T> {
  protected prisma: PrismaClient;
  protected modelName: string;

  constructor(modelName: string) {
    this.prisma = databaseService.getClient();
    this.modelName = modelName;
  }

  /**
   * Get model delegate
   */
  protected getModel(): any {
    return (this.prisma as any)[this.modelName];
  }

  /**
   * Create a new record
   */
  public async create(data: any): Promise<T> {
    try {
      logger.debug(`Creating ${this.modelName}`, { data });
      const result = await this.getModel().create({ data });
      logger.info(`${this.modelName} created successfully`, { id: result.id });
      return result;
    } catch (error) {
      logger.error(`Failed to create ${this.modelName}`, { error, data });
      throw error;
    }
  }

  /**
   * Find record by ID
   */
  public async findById(id: string, options?: FilterOptions): Promise<T | null> {
    try {
      logger.debug(`Finding ${this.modelName} by ID`, { id });
      const result = await this.getModel().findUnique({
        where: { id },
        ...options,
      });
      return result;
    } catch (error) {
      logger.error(`Failed to find ${this.modelName} by ID`, { error, id });
      throw error;
    }
  }

  /**
   * Find first record matching criteria
   */
  public async findFirst(options: FilterOptions): Promise<T | null> {
    try {
      logger.debug(`Finding first ${this.modelName}`, { options });
      const result = await this.getModel().findFirst(options);
      return result;
    } catch (error) {
      logger.error(`Failed to find first ${this.modelName}`, { error, options });
      throw error;
    }
  }

  /**
   * Find many records with pagination
   */
  public async findMany(
    options: FilterOptions = {},
    pagination: PaginationOptions = {}
  ): Promise<PaginatedResult<T>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = pagination;

      const skip = (page - 1) * limit;

      logger.debug(`Finding many ${this.modelName}`, { options, pagination });

      const [data, total] = await Promise.all([
        this.getModel().findMany({
          ...options,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.getModel().count({ where: options.where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error(`Failed to find many ${this.modelName}`, { error, options, pagination });
      throw error;
    }
  }

  /**
   * Update record by ID
   */
  public async update(id: string, data: any, options?: FilterOptions): Promise<T> {
    try {
      logger.debug(`Updating ${this.modelName}`, { id, data });
      const result = await this.getModel().update({
        where: { id },
        data,
        ...options,
      });
      logger.info(`${this.modelName} updated successfully`, { id });
      return result;
    } catch (error) {
      logger.error(`Failed to update ${this.modelName}`, { error, id, data });
      throw error;
    }
  }

  /**
   * Delete record by ID
   */
  public async delete(id: string): Promise<T> {
    try {
      logger.debug(`Deleting ${this.modelName}`, { id });
      const result = await this.getModel().delete({
        where: { id },
      });
      logger.info(`${this.modelName} deleted successfully`, { id });
      return result;
    } catch (error) {
      logger.error(`Failed to delete ${this.modelName}`, { error, id });
      throw error;
    }
  }

  /**
   * Check if record exists
   */
  public async exists(id: string): Promise<boolean> {
    try {
      const result = await this.getModel().findUnique({
        where: { id },
        select: { id: true },
      });
      return !!result;
    } catch (error) {
      logger.error(`Failed to check if ${this.modelName} exists`, { error, id });
      throw error;
    }
  }

  /**
   * Count records
   */
  public async count(where?: any): Promise<number> {
    try {
      const result = await this.getModel().count({ where });
      return result;
    } catch (error) {
      logger.error(`Failed to count ${this.modelName}`, { error, where });
      throw error;
    }
  }

  /**
   * Upsert record
   */
  public async upsert(
    where: any,
    create: any,
    update: any,
    options?: FilterOptions
  ): Promise<T> {
    try {
      logger.debug(`Upserting ${this.modelName}`, { where, create, update });
      const result = await this.getModel().upsert({
        where,
        create,
        update,
        ...options,
      });
      logger.info(`${this.modelName} upserted successfully`, { id: result.id });
      return result;
    } catch (error) {
      logger.error(`Failed to upsert ${this.modelName}`, { error, where, create, update });
      throw error;
    }
  }

  /**
   * Bulk create records
   */
  public async createMany(data: any[]): Promise<{ count: number }> {
    try {
      logger.debug(`Bulk creating ${this.modelName}`, { count: data.length });
      const result = await this.getModel().createMany({ data });
      logger.info(`${this.modelName} bulk created successfully`, { count: result.count });
      return result;
    } catch (error) {
      logger.error(`Failed to bulk create ${this.modelName}`, { error, count: data.length });
      throw error;
    }
  }

  /**
   * Execute raw query
   */
  public async executeRaw(query: string, params?: any[]): Promise<any> {
    try {
      logger.debug('Executing raw query', { query, params });
      const result = await this.prisma.$executeRawUnsafe(query, ...(params || []));
      return result;
    } catch (error) {
      logger.error('Failed to execute raw query', { error, query, params });
      throw error;
    }
  }

  /**
   * Query raw
   */
  public async queryRaw(query: string, params?: any[]): Promise<any> {
    try {
      logger.debug('Executing raw query', { query, params });
      const result = await this.prisma.$queryRawUnsafe(query, ...(params || []));
      return result;
    } catch (error) {
      logger.error('Failed to execute raw query', { error, query, params });
      throw error;
    }
  }
}
