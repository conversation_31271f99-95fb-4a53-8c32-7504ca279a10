/**
 * Database Service
 * Manages Prisma client connection and provides database utilities
 */

import { PrismaClient } from '@prisma/client';
import { Injectable } from '@/core/Container';
import { logger } from '@/utils/logger';
import { env } from '@/config/env';

@Injectable('DatabaseService')
export class DatabaseService {
  public readonly serviceName = 'DatabaseService';
  private static instance: DatabaseService;
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient({
      log: env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
      errorFormat: 'pretty',
    });
  }

  /**
   * Get Prisma client instance
   */
  public getClient(): PrismaClient {
    return this.prisma;
  }

  /**
   * Initialize database connection
   */
  public async initialize(): Promise<void> {
    try {
      await this.prisma.$connect();
      logger.info('Database connected successfully', {
        database: 'PostgreSQL',
        provider: 'Prisma'
      });
    } catch (error) {
      logger.error('Failed to connect to database', { error });
      throw error;
    }
  }

  /**
   * Cleanup database connection
   */
  public async destroy(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error('Failed to disconnect from database', { error });
      throw error;
    }
  }

  /**
   * Check database health
   */
  public async healthCheck(): Promise<{ status: string; latency: number }> {
    const start = Date.now();
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      const latency = Date.now() - start;
      return { status: 'healthy', latency };
    } catch (error) {
      logger.error('Database health check failed', { error });
      return { status: 'unhealthy', latency: Date.now() - start };
    }
  }

  /**
   * Execute database transaction
   */
  public async transaction<T>(
    fn: (prisma: PrismaClient) => Promise<T>
  ): Promise<T> {
    return this.prisma.$transaction(fn);
  }

  /**
   * Get database statistics
   */
  public async getStats(): Promise<{
    users: number;
    trainingSessions: number;
    gameRecords: number;
    posts: number;
  }> {
    try {
      const [users, trainingSessions, gameRecords, posts] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.trainingSession.count(),
        this.prisma.gameRecord.count(),
        this.prisma.post.count(),
      ]);

      return {
        users,
        trainingSessions,
        gameRecords,
        posts,
      };
    } catch (error) {
      logger.error('Failed to get database stats', { error });
      throw error;
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
