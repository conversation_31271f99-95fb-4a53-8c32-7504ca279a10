/**
 * Training Plan Repository
 * Handles database operations for training plans and recommendations
 */

import { BaseRepository } from './BaseRepository';
import { logger } from '@/utils/logger';
import { RehabilitationPlan, Prisma } from '@prisma/client';

export interface CreateTrainingPlanData {
  userId: string;
  planName: string;
  description?: string;
  targetCondition: string;
  difficultyLevel: number;
  estimatedDurationWeeks: number;
  sessionsPerWeek: number;
  sessionDurationMinutes: number;
  planConfig: any;
  goals: any;
  createdBy?: string;
  approvedBy?: string;
}

export interface UpdateTrainingPlanData {
  planName?: string;
  description?: string;
  difficultyLevel?: number;
  estimatedDurationWeeks?: number;
  sessionsPerWeek?: number;
  sessionDurationMinutes?: number;
  planConfig?: any;
  goals?: any;
  status?: string;
  approvedBy?: string;
  approvedAt?: Date;
  notes?: string;
}

export interface TrainingPlanFilters {
  userId?: string;
  status?: string;
  targetCondition?: string;
  difficultyLevel?: number;
  createdBy?: string;
  approvedBy?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface TrainingPlanStats {
  totalPlans: number;
  activePlans: number;
  completedPlans: number;
  averageCompletionRate: number;
  averageDuration: number;
  popularTargetConditions: Array<{
    condition: string;
    count: number;
  }>;
}

export interface RecommendationCriteria {
  userId: string;
  currentCondition: string;
  difficultyPreference: number;
  timeAvailability: number; // minutes per week
  previousPlans?: string[];
  assessmentResults?: any;
  userPreferences?: any;
}

export class TrainingPlanRepository extends BaseRepository<RehabilitationPlan> {
  constructor() {
    super('rehabilitationPlan');
  }

  /**
   * Create a new training plan
   */
  public async createTrainingPlan(data: CreateTrainingPlanData): Promise<RehabilitationPlan> {
    try {
      logger.info('Creating training plan', { userId: data.userId, planName: data.planName });

      const plan = await this.prisma.rehabilitationPlan.create({
        data: {
          userId: data.userId,
          planName: data.planName,
          description: data.description,
          targetCondition: data.targetCondition,
          difficultyLevel: data.difficultyLevel,
          estimatedDurationWeeks: data.estimatedDurationWeeks,
          sessionsPerWeek: data.sessionsPerWeek,
          sessionDurationMinutes: data.sessionDurationMinutes,
          planConfig: data.planConfig,
          goals: data.goals,
          status: 'draft',
          createdBy: data.createdBy,
          approvedBy: data.approvedBy,
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          approver: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
        },
      });

      logger.info('Training plan created successfully', { planId: plan.id });
      return plan;
    } catch (error) {
      logger.error('Failed to create training plan', { error, data });
      throw error;
    }
  }

  /**
   * Get training plan by ID
   */
  public async getTrainingPlanById(id: string): Promise<RehabilitationPlan | null> {
    try {
      const plan = await this.prisma.rehabilitationPlan.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          approver: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          trainingSessions: {
            select: {
              id: true,
              sessionName: true,
              sessionStatus: true,
              startTime: true,
              endTime: true,
              actualDurationSeconds: true,
              achievedAccuracy: true,
              totalScore: true,
            },
            orderBy: { startTime: 'desc' },
            take: 10,
          },
        },
      });

      return plan;
    } catch (error) {
      logger.error('Failed to get training plan by ID', { error, id });
      throw error;
    }
  }

  /**
   * Get user training plans with pagination and filters
   */
  public async getUserTrainingPlans(
    userId: string,
    filters: TrainingPlanFilters = {},
    page: number = 1,
    limit: number = 10
  ): Promise<{ plans: RehabilitationPlan[]; total: number }> {
    try {
      const where: Prisma.RehabilitationPlanWhereInput = {
        userId,
        isActive: true,
        ...(filters.status && { status: filters.status }),
        ...(filters.targetCondition && { targetCondition: filters.targetCondition }),
        ...(filters.difficultyLevel && { difficultyLevel: filters.difficultyLevel }),
        ...(filters.createdBy && { createdBy: filters.createdBy }),
        ...(filters.approvedBy && { approvedBy: filters.approvedBy }),
        ...(filters.dateFrom || filters.dateTo) && {
          createdAt: {
            ...(filters.dateFrom && { gte: filters.dateFrom }),
            ...(filters.dateTo && { lte: filters.dateTo }),
          },
        },
      };

      const [plans, total] = await Promise.all([
        this.prisma.rehabilitationPlan.findMany({
          where,
          include: {
            creator: {
              select: {
                id: true,
                username: true,
                fullName: true,
              },
            },
            approver: {
              select: {
                id: true,
                username: true,
                fullName: true,
              },
            },
            trainingSessions: {
              select: {
                id: true,
                sessionStatus: true,
                totalScore: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
        }),
        this.prisma.rehabilitationPlan.count({ where }),
      ]);

      return { plans, total };
    } catch (error) {
      logger.error('Failed to get user training plans', { error, userId, filters });
      throw error;
    }
  }

  /**
   * Update training plan
   */
  public async updateTrainingPlan(id: string, data: UpdateTrainingPlanData): Promise<RehabilitationPlan> {
    try {
      logger.info('Updating training plan', { planId: id });

      const plan = await this.prisma.rehabilitationPlan.update({
        where: { id },
        data,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          approver: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
        },
      });

      logger.info('Training plan updated successfully', { planId: id });
      return plan;
    } catch (error) {
      logger.error('Failed to update training plan', { error, id, data });
      throw error;
    }
  }

  /**
   * Approve training plan
   */
  public async approveTrainingPlan(id: string, approvedBy: string): Promise<RehabilitationPlan> {
    return this.updateTrainingPlan(id, {
      status: 'approved',
      approvedBy,
      approvedAt: new Date(),
    });
  }

  /**
   * Activate training plan
   */
  public async activateTrainingPlan(id: string): Promise<RehabilitationPlan> {
    return this.updateTrainingPlan(id, {
      status: 'active',
    });
  }

  /**
   * Complete training plan
   */
  public async completeTrainingPlan(id: string, completionData?: any): Promise<RehabilitationPlan> {
    return this.updateTrainingPlan(id, {
      status: 'completed',
      notes: completionData?.notes,
    });
  }

  /**
   * Get training plan statistics for a user
   */
  public async getUserTrainingPlanStats(
    userId: string,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<TrainingPlanStats> {
    try {
      const where: Prisma.RehabilitationPlanWhereInput = {
        userId,
        isActive: true,
        ...(dateFrom || dateTo) && {
          createdAt: {
            ...(dateFrom && { gte: dateFrom }),
            ...(dateTo && { lte: dateTo }),
          },
        },
      };

      const plans = await this.prisma.rehabilitationPlan.findMany({
        where,
        include: {
          trainingSessions: {
            select: {
              sessionStatus: true,
              actualDurationSeconds: true,
            },
          },
        },
      });

      const totalPlans = plans.length;
      const activePlans = plans.filter(p => p.status === 'active').length;
      const completedPlans = plans.filter(p => p.status === 'completed').length;

      // Calculate average completion rate based on sessions
      let totalCompletionRate = 0;
      let totalDuration = 0;
      let plansWithSessions = 0;

      for (const plan of plans) {
        if (plan.trainingSessions.length > 0) {
          const completedSessions = plan.trainingSessions.filter(s => s.sessionStatus === 'completed').length;
          const completionRate = completedSessions / plan.trainingSessions.length;
          totalCompletionRate += completionRate;
          
          const planDuration = plan.trainingSessions.reduce((sum, s) => sum + (s.actualDurationSeconds || 0), 0);
          totalDuration += planDuration;
          plansWithSessions++;
        }
      }

      const averageCompletionRate = plansWithSessions > 0 ? totalCompletionRate / plansWithSessions : 0;
      const averageDuration = plansWithSessions > 0 ? Math.floor(totalDuration / plansWithSessions / 60) : 0; // in minutes

      // Get popular target conditions
      const conditionCounts = plans.reduce((acc, plan) => {
        const condition = plan.targetCondition;
        acc[condition] = (acc[condition] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const popularTargetConditions = Object.entries(conditionCounts)
        .map(([condition, count]) => ({ condition, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      return {
        totalPlans,
        activePlans,
        completedPlans,
        averageCompletionRate,
        averageDuration,
        popularTargetConditions,
      };
    } catch (error) {
      logger.error('Failed to get user training plan stats', { error, userId });
      throw error;
    }
  }

  /**
   * Get recommended training plans based on criteria
   */
  public async getRecommendedTrainingPlans(
    criteria: RecommendationCriteria,
    limit: number = 5
  ): Promise<RehabilitationPlan[]> {
    try {
      logger.info('Getting recommended training plans', { userId: criteria.userId, condition: criteria.currentCondition });

      // Build recommendation query based on criteria
      const where: Prisma.RehabilitationPlanWhereInput = {
        isActive: true,
        status: 'approved',
        targetCondition: criteria.currentCondition,
        difficultyLevel: {
          gte: Math.max(1, criteria.difficultyPreference - 1),
          lte: Math.min(5, criteria.difficultyPreference + 1),
        },
        // Exclude plans the user already has
        ...(criteria.previousPlans && criteria.previousPlans.length > 0) && {
          id: {
            notIn: criteria.previousPlans,
          },
        },
      };

      // Calculate time compatibility
      const maxSessionDuration = Math.floor(criteria.timeAvailability / 3); // Assume 3 sessions per week minimum
      if (maxSessionDuration > 0) {
        where.sessionDurationMinutes = {
          lte: maxSessionDuration,
        };
      }

      const recommendedPlans = await this.prisma.rehabilitationPlan.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              fullName: true,
            },
          },
          trainingSessions: {
            select: {
              id: true,
              sessionStatus: true,
              totalScore: true,
              achievedAccuracy: true,
            },
          },
        },
        orderBy: [
          { difficultyLevel: 'asc' },
          { createdAt: 'desc' },
        ],
        take: limit * 2, // Get more to allow for filtering
      });

      // Score and rank plans based on user preferences and success rates
      const scoredPlans = recommendedPlans.map(plan => {
        let score = 0;

        // Difficulty match score (higher is better)
        const difficultyDiff = Math.abs(plan.difficultyLevel - criteria.difficultyPreference);
        score += Math.max(0, 10 - difficultyDiff * 2);

        // Time compatibility score
        const timeRatio = criteria.timeAvailability / (plan.sessionsPerWeek * plan.sessionDurationMinutes);
        if (timeRatio >= 1) {
          score += 10;
        } else if (timeRatio >= 0.8) {
          score += 8;
        } else if (timeRatio >= 0.6) {
          score += 5;
        }

        // Success rate score based on other users' performance
        if (plan.trainingSessions.length > 0) {
          const completedSessions = plan.trainingSessions.filter(s => s.sessionStatus === 'completed').length;
          const successRate = completedSessions / plan.trainingSessions.length;
          score += successRate * 10;

          // Average accuracy score
          const avgAccuracy = plan.trainingSessions.reduce((sum, s) =>
            sum + (s.achievedAccuracy?.toNumber() || 0), 0
          ) / plan.trainingSessions.length;
          score += avgAccuracy * 5;
        }

        return { plan, score };
      });

      // Sort by score and return top plans
      return scoredPlans
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)
        .map(item => item.plan);
    } catch (error) {
      logger.error('Failed to get recommended training plans', { error, criteria });
      throw error;
    }
  }

  /**
   * Delete training plan (soft delete)
   */
  public async deleteTrainingPlan(id: string): Promise<RehabilitationPlan> {
    try {
      logger.info('Deleting training plan', { planId: id });

      const plan = await this.prisma.rehabilitationPlan.update({
        where: { id },
        data: { isActive: false },
      });

      logger.info('Training plan deleted successfully', { planId: id });
      return plan;
    } catch (error) {
      logger.error('Failed to delete training plan', { error, id });
      throw error;
    }
  }
}
