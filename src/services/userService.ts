import { PrismaClient, User } from '@prisma/client';
import { passwordService } from '@/utils/password';
import { jwtService, TokenPair } from '@/utils/jwt';
import { smsService } from '@/services/smsService';
import { logger } from '@/utils/logger';
import { badRequest, conflict, notFound, unauthorized } from '@/middleware/errorHandler';
import { normalizePhoneNumber } from '@/utils/validation';

const prisma = new PrismaClient();

export interface CreateUserData {
  username: string;
  email?: string;
  phoneNumber?: string;
  password: string;
  fullName: string;
  age?: number;
  gender?: string;
}

export interface UpdateUserData {
  fullName?: string;
  age?: number;
  gender?: string;
  recoveryPhase?: string;
  healthMetrics?: {
    height?: number;
    weight?: number;
  };
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface PhoneLoginData {
  phoneNumber: string;
  smsCode: string;
  ipAddress?: string;
}

export interface PhoneRegisterData {
  phoneNumber: string;
  smsCode: string;
  fullName: string;
  password?: string;
  age?: number;
  gender?: string;
  ipAddress?: string;
}

export interface PasswordLoginData {
  identifier: string; // username or phoneNumber
  password: string;
}

export interface ResetPasswordData {
  phoneNumber: string;
  smsCode: string;
  newPassword: string;
  ipAddress?: string;
}

export interface UserWithTokens {
  user: Omit<User, 'passwordHash'>;
  tokens: TokenPair;
}

class UserService {
  /**
   * Create a new user
   */
  async createUser(userData: CreateUserData): Promise<UserWithTokens> {
    try {
      // Validate password strength
      const passwordValidation = passwordService.validatePasswordStrength(userData.password);
      if (!passwordValidation.isValid) {
        throw badRequest('Password does not meet security requirements', {
          passwordErrors: passwordValidation.errors,
        });
      }

      // Check if username already exists
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { username: userData.username },
            ...(userData.email ? [{ email: userData.email }] : []),
            ...(userData.phoneNumber ? [{ phoneNumber: userData.phoneNumber }] : []),
          ],
        },
      });

      if (existingUser) {
        if (existingUser.username === userData.username) {
          throw conflict('Username already exists');
        }
        if (existingUser.email === userData.email) {
          throw conflict('Email already exists');
        }
        if (existingUser.phoneNumber === userData.phoneNumber) {
          throw conflict('Phone number already exists');
        }
      }

      // Hash password
      const passwordHash = await passwordService.hashPassword(userData.password);

      // Create user
      const user = await prisma.user.create({
        data: {
          username: userData.username,
          email: userData.email ?? null,
          phoneNumber: userData.phoneNumber ?? null,
          passwordHash,
          fullName: userData.fullName,
          age: userData.age ?? null,
          gender: userData.gender ?? null,
        },
      });

      // Generate tokens
      const tokens = jwtService.generateTokenPair({
        sub: user.id,
        username: user.username,
        email: user.email ?? undefined,
      });

      // Store refresh token
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      logger.info('User created successfully', {
        userId: user.id,
        username: user.username,
      });

      // Remove password hash from response
      const { passwordHash: _, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('User creation failed:', error);
      throw error;
    }
  }



  /**
   * Authenticate user with phone number and SMS code
   */
  async loginWithPhone(phoneData: PhoneLoginData): Promise<UserWithTokens> {
    try {
      // Verify SMS code
      const isValidCode = await smsService.verifyCode({
        phoneNumber: phoneData.phoneNumber,
        code: phoneData.smsCode,
        purpose: 'login',
        ipAddress: phoneData.ipAddress,
      });

      if (!isValidCode) {
        throw unauthorized('Invalid SMS verification code');
      }

      // Find user by phone number
      const user = await prisma.user.findUnique({
        where: { phoneNumber: phoneData.phoneNumber },
      });

      if (!user) {
        throw unauthorized('Phone number not registered');
      }

      if (!user.isActive) {
        throw unauthorized('Account is inactive');
      }

      // Generate tokens
      const tokens = jwtService.generateTokenPair({
        sub: user.id,
        username: user.username,
        email: user.email ?? undefined,
      });

      // Store refresh token
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      logger.info('User logged in with phone successfully', {
        userId: user.id,
        username: user.username,
        phoneNumber: phoneData.phoneNumber,
      });

      // Remove password hash from response
      const { passwordHash: _, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('Phone login failed:', error);
      throw error;
    }
  }

  /**
   * Register user with phone number and SMS code
   */
  async registerWithPhone(phoneData: PhoneRegisterData): Promise<UserWithTokens> {
    try {
      // Verify SMS code
      const isValidCode = await smsService.verifyCode({
        phoneNumber: phoneData.phoneNumber,
        code: phoneData.smsCode,
        purpose: 'register',
        ipAddress: phoneData.ipAddress,
      });

      if (!isValidCode) {
        throw unauthorized('Invalid SMS verification code');
      }

      // Check if phone number is already registered
      const existingUser = await prisma.user.findUnique({
        where: { phoneNumber: phoneData.phoneNumber },
      });

      if (existingUser) {
        throw conflict('Phone number already registered');
      }

      // Generate username from phone number (can be changed later)
      const username = `user_${phoneData.phoneNumber.slice(-8)}`;

      // Check if generated username exists and make it unique
      let finalUsername = username;
      let counter = 1;
      while (await prisma.user.findUnique({ where: { username: finalUsername } })) {
        finalUsername = `${username}_${counter}`;
        counter++;
      }

      // Hash the provided password, or generate a temporary one if not provided
      const passwordToHash = phoneData.password || passwordService.generateTemporaryPassword();
      const passwordHash = await passwordService.hashPassword(passwordToHash);

      // Create user
      const user = await prisma.user.create({
        data: {
          username: finalUsername,
          phoneNumber: phoneData.phoneNumber,
          passwordHash,
          fullName: phoneData.fullName,
          age: phoneData.age ?? null,
          gender: phoneData.gender ?? null,
        },
      });

      // Generate tokens
      const tokens = jwtService.generateTokenPair({
        sub: user.id,
        username: user.username,
        email: user.email ?? undefined,
      });

      // Store refresh token
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      logger.info('User registered with phone successfully', {
        userId: user.id,
        username: user.username,
        phoneNumber: phoneData.phoneNumber,
      });

      // Remove password hash from response and transform data types
      const { passwordHash: _, ...userWithoutPassword } = user;
      const transformedUser = this.transformUserData(userWithoutPassword);

      return {
        user: transformedUser,
        tokens,
      };
    } catch (error) {
      logger.error('Phone registration failed:', error);
      throw error;
    }
  }

  /**
   * Authenticate user with identifier (username or phone) and password
   */
  async loginWithPassword(loginData: PasswordLoginData): Promise<UserWithTokens> {
    try {
      // Normalize phone number if the identifier looks like a phone number
      const normalizedIdentifier = /^\+?[1-9]\d{1,14}$/.test(loginData.identifier.replace(/\D/g, ''))
        ? normalizePhoneNumber(loginData.identifier)
        : loginData.identifier;

      // Find user by username or phone number
      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { username: loginData.identifier },
            { phoneNumber: loginData.identifier },
            { phoneNumber: normalizedIdentifier }, // Try normalized phone number
          ],
        },
      });

      if (!user) {
        throw unauthorized('Invalid credentials');
      }

      if (!user.isActive) {
        throw unauthorized('Account is inactive');
      }

      // Verify password
      const isValidPassword = await passwordService.verifyPassword(
        loginData.password.trim(),
        user.passwordHash
      );

      if (!isValidPassword) {
        throw unauthorized('Invalid credentials');
      }

      // Generate tokens
      const tokens = jwtService.generateTokenPair({
        sub: user.id,
        username: user.username,
        email: user.email ?? undefined,
      });

      // Store refresh token
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      logger.info('User logged in with password successfully', {
        userId: user.id,
        username: user.username,
        identifier: loginData.identifier,
      });

      // Remove password hash from response and transform data types
      const { passwordHash: _, ...userWithoutPassword } = user;
      const transformedUser = this.transformUserData(userWithoutPassword);

      return {
        user: transformedUser,
        tokens,
      };
    } catch (error) {
      logger.error('Password login failed:', error);
      throw error;
    }
  }

  /**
   * Reset password using SMS verification
   */
  async resetPassword(resetData: ResetPasswordData): Promise<void> {
    try {
      // 1. Find the user first. If not found, no need to verify code.
      const user = await prisma.user.findUnique({
        where: { phoneNumber: resetData.phoneNumber },
      });

      if (!user) {
        throw notFound('User not found');
      }

      // 2. Now, verify the SMS code.
      const isValidCode = await smsService.verifyCode({
        phoneNumber: resetData.phoneNumber,
        code: resetData.smsCode,
        purpose: 'reset_password',
        ipAddress: resetData.ipAddress,
      });

      if (!isValidCode) {
        // The error is thrown inside verifyCode, so we just need to re-throw.
        throw unauthorized('Invalid or expired verification code');
      }

      // 3. Hash new password
      const passwordHash = await passwordService.hashPassword(resetData.newPassword);

      // 4. Update password
      await prisma.user.update({
        where: { id: user.id },
        data: { passwordHash },
      });

      // 5. Invalidate all existing refresh tokens for security
      await prisma.userToken.deleteMany({
        where: {
          userId: user.id,
          tokenType: 'refresh',
        },
      });

      logger.info('Password reset successfully', {
        userId: user.id,
        phoneNumber: resetData.phoneNumber,
      });
    } catch (error) {
      // Re-throw the original error to be handled by the controller
      if (error instanceof Error && 'code' in error) {
      throw error;
      }
      logger.error('Password reset failed with an unexpected error:', error);
      throw new Error('Password reset failed');
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<TokenPair> {
    try {
      // Verify refresh token
      const payload = jwtService.verifyRefreshToken(refreshToken);

      // Check if refresh token exists in database
      const storedToken = await prisma.userToken.findFirst({
        where: {
          userId: payload.sub,
          tokenType: 'refresh',
          usedAt: null,
        },
      });

      if (!storedToken) {
        throw unauthorized('Invalid refresh token');
      }

      // Check if user is still active
      const user = await prisma.user.findUnique({
        where: { id: payload.sub },
        select: { id: true, username: true, email: true, isActive: true },
      });

      if (!user || !user.isActive) {
        throw unauthorized('User not found or inactive');
      }

      // Generate new tokens
      const tokens = jwtService.generateTokenPair({
        sub: user.id,
        username: user.username,
        email: user.email ?? undefined,
      });

      // Mark old refresh token as used
      await prisma.userToken.update({
        where: { id: storedToken.id },
        data: { usedAt: new Date() },
      });

      // Store new refresh token
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      logger.info('Token refreshed successfully', {
        userId: user.id,
        username: user.username,
      });

      return tokens;
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<Omit<User, 'passwordHash'> | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return null;
      }

      // Remove password hash from response and transform data types
      const { passwordHash: _, ...userWithoutPassword } = user;
      return this.transformUserData(userWithoutPassword);
    } catch (error) {
      logger.error('Get user by ID failed:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateUser(userId: string, updateData: UpdateUserData): Promise<Omit<User, 'passwordHash'>> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw notFound('User not found');
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: updateData,
      });

      logger.info('User updated successfully', {
        userId,
        updatedFields: Object.keys(updateData),
      });

      // Remove password hash from response and transform data types
      const { passwordHash: _, ...userWithoutPassword } = updatedUser;
      return this.transformUserData(userWithoutPassword);
    } catch (error) {
      logger.error('User update failed:', error);
      throw error;
    }
  }

  /**
   * Logout user (invalidate refresh tokens)
   */
  async logout(userId: string): Promise<void> {
    try {
      // Mark all refresh tokens as used
      await prisma.userToken.updateMany({
        where: {
          userId,
          tokenType: 'refresh',
          usedAt: null,
        },
        data: {
          usedAt: new Date(),
        },
      });

      logger.info('User logged out successfully', { userId });
    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  }

  /**
   * Store refresh token in database
   */
  private async storeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    try {
      const tokenHash = await passwordService.hashPassword(refreshToken);
      const expiresAt = jwtService.getTokenExpiry(refreshToken);

      if (!expiresAt) {
        throw new Error('Invalid refresh token expiry');
      }

      await prisma.userToken.create({
        data: {
          userId,
          tokenType: 'refresh',
          tokenHash,
          expiresAt,
        },
      });
    } catch (error) {
      logger.error('Store refresh token failed:', error);
      throw error;
    }
  }

  /**
   * Clean up expired tokens
   */
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const result = await prisma.userToken.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      logger.info('Expired tokens cleaned up', { deletedCount: result.count });
    } catch (error) {
      logger.error('Token cleanup failed:', error);
    }
  }

  /**
   * Transform user data to ensure correct data types for API responses
   */
  private transformUserData(user: any): any {
    return {
      ...user,
      // Convert Prisma Decimal to number for recoveryProgress
      recoveryProgress: user.recoveryProgress ? Number(user.recoveryProgress) : null,
    };
  }
}

export const userService = new UserService();
export default userService;
