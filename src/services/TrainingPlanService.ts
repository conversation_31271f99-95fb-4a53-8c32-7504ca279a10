/**
 * Training Plan Service
 * Handles business logic for training plan management and recommendations
 */

import { TrainingPlanRepository, CreateTrainingPlanData, UpdateTrainingPlanData, TrainingPlanFilters, TrainingPlanStats, RecommendationCriteria } from './database/TrainingPlanRepository';
import { logger } from '@/utils/logger';
import { RehabilitationPlan } from '@prisma/client';

export interface TrainingPlanRecommendation {
  plan: RehabilitationPlan;
  score: number;
  reasons: string[];
  compatibility: {
    difficulty: number;
    timeCommitment: number;
    targetMatch: number;
  };
}

export interface ExecutionTrackingData {
  planId: string;
  sessionId: string;
  completionStatus: 'completed' | 'partial' | 'skipped';
  performanceMetrics: {
    accuracy: number;
    duration: number;
    score: number;
  };
  notes?: string;
}

export interface ProgressAnalysis {
  overallProgress: number;
  weeklyProgress: Array<{
    week: number;
    sessionsCompleted: number;
    averageScore: number;
    averageAccuracy: number;
  }>;
  trends: {
    scoreImprovement: number;
    accuracyImprovement: number;
    consistencyImprovement: number;
  };
  recommendations: string[];
}

export class TrainingPlanService {
  private trainingPlanRepository: TrainingPlanRepository;

  constructor() {
    // Use lazy initialization to avoid circular dependency issues
    this.trainingPlanRepository = new TrainingPlanRepository();
  }

  /**
   * Create a new training plan
   */
  public async createTrainingPlan(data: CreateTrainingPlanData): Promise<RehabilitationPlan> {
    try {
      logger.info('Creating training plan', { userId: data.userId, planName: data.planName });

      // Validate plan configuration
      this.validatePlanConfig(data.planConfig);

      // Set default values if not provided
      const planData = {
        ...data,
        difficultyLevel: data.difficultyLevel || 1,
        sessionsPerWeek: data.sessionsPerWeek || 3,
        sessionDurationMinutes: data.sessionDurationMinutes || 30,
        estimatedDurationWeeks: data.estimatedDurationWeeks || 4,
      };

      const plan = await this.trainingPlanRepository.createTrainingPlan(planData);

      logger.info('Training plan created successfully', { planId: plan.id });
      return plan;
    } catch (error) {
      logger.error('Failed to create training plan', { error, data });
      throw error;
    }
  }

  /**
   * Get training plan by ID
   */
  public async getTrainingPlanById(id: string): Promise<RehabilitationPlan | null> {
    try {
      const plan = await this.trainingPlanRepository.getTrainingPlanById(id);

      if (!plan) {
        logger.warn('Training plan not found', { planId: id });
        return null;
      }

      return plan;
    } catch (error) {
      logger.error('Failed to get training plan by ID', { error, id });
      throw error;
    }
  }

  /**
   * Get user training plans with pagination
   */
  public async getUserTrainingPlans(
    userId: string,
    filters: TrainingPlanFilters = {},
    page: number = 1,
    limit: number = 10
  ): Promise<{ plans: RehabilitationPlan[]; total: number; totalPages: number }> {
    try {
      const { plans, total } = await this.trainingPlanRepository.getUserTrainingPlans(
        userId,
        filters,
        page,
        limit
      );

      const totalPages = Math.ceil(total / limit);

      return { plans, total, totalPages };
    } catch (error) {
      logger.error('Failed to get user training plans', { error, userId, filters });
      throw error;
    }
  }

  /**
   * Update training plan
   */
  public async updateTrainingPlan(id: string, data: UpdateTrainingPlanData): Promise<RehabilitationPlan> {
    try {
      // Validate plan configuration if provided
      if (data.planConfig) {
        this.validatePlanConfig(data.planConfig);
      }

      const plan = await this.trainingPlanRepository.updateTrainingPlan(id, data);
      return plan;
    } catch (error) {
      logger.error('Failed to update training plan', { error, id });
      throw error;
    }
  }

  /**
   * Get personalized training plan recommendations
   */
  public async getPersonalizedRecommendations(
    criteria: RecommendationCriteria
  ): Promise<TrainingPlanRecommendation[]> {
    try {
      logger.info('Getting personalized recommendations', { userId: criteria.userId });

      // Get base recommendations from repository
      const recommendedPlans = await this.trainingPlanRepository.getRecommendedTrainingPlans(criteria);

      // Enhance recommendations with detailed analysis
      const enhancedRecommendations: TrainingPlanRecommendation[] = [];

      for (const plan of recommendedPlans) {
        const analysis = this.analyzeTrainingPlanCompatibility(plan, criteria);
        const reasons = this.generateRecommendationReasons(plan, criteria, analysis);

        enhancedRecommendations.push({
          plan,
          score: analysis.overallScore,
          reasons,
          compatibility: {
            difficulty: analysis.difficultyMatch,
            timeCommitment: analysis.timeCompatibility,
            targetMatch: analysis.targetMatch,
          },
        });
      }

      // Sort by overall score
      enhancedRecommendations.sort((a, b) => b.score - a.score);

      logger.info('Generated personalized recommendations', {
        userId: criteria.userId,
        count: enhancedRecommendations.length,
      });

      return enhancedRecommendations;
    } catch (error) {
      logger.error('Failed to get personalized recommendations', { error, criteria });
      throw error;
    }
  }

  /**
   * Approve training plan
   */
  public async approveTrainingPlan(id: string, approvedBy: string): Promise<RehabilitationPlan> {
    try {
      const plan = await this.trainingPlanRepository.approveTrainingPlan(id, approvedBy);
      return plan;
    } catch (error) {
      logger.error('Failed to approve training plan', { error, id });
      throw error;
    }
  }

  /**
   * Activate training plan
   */
  public async activateTrainingPlan(id: string): Promise<RehabilitationPlan> {
    try {
      const plan = await this.trainingPlanRepository.activateTrainingPlan(id);
      return plan;
    } catch (error) {
      logger.error('Failed to activate training plan', { error, id });
      throw error;
    }
  }

  /**
   * Complete training plan
   */
  public async completeTrainingPlan(id: string, completionData?: any): Promise<RehabilitationPlan> {
    try {
      const plan = await this.trainingPlanRepository.completeTrainingPlan(id, completionData);
      return plan;
    } catch (error) {
      logger.error('Failed to complete training plan', { error, id });
      throw error;
    }
  }

  /**
   * Get training plan statistics
   */
  public async getUserTrainingPlanStats(
    userId: string,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<TrainingPlanStats> {
    try {
      const stats = await this.trainingPlanRepository.getUserTrainingPlanStats(userId, dateFrom, dateTo);
      return stats;
    } catch (error) {
      logger.error('Failed to get user training plan stats', { error, userId });
      throw error;
    }
  }

  /**
   * Track training plan execution
   */
  public async trackExecution(data: ExecutionTrackingData): Promise<void> {
    try {
      logger.info('Tracking training plan execution', {
        planId: data.planId,
        sessionId: data.sessionId,
        status: data.completionStatus,
      });

      // This would typically update session records and plan progress
      // For now, we'll log the tracking data
      logger.info('Training execution tracked', { data });
    } catch (error) {
      logger.error('Failed to track training execution', { error, data });
      throw error;
    }
  }

  /**
   * Analyze training plan progress
   */
  public async analyzeProgress(planId: string): Promise<ProgressAnalysis> {
    try {
      logger.info('Analyzing training plan progress', { planId });

      const progress = await this.trainingPlanRepository.getTrainingPlanProgress(planId);

      // Calculate weekly progress (simplified for demo)
      const weeklyProgress = this.calculateWeeklyProgress(progress);

      // Calculate trends
      const trends = this.calculateProgressTrends(progress);

      // Generate recommendations
      const recommendations = this.generateProgressRecommendations(progress, trends);

      const analysis: ProgressAnalysis = {
        overallProgress: progress.completionRate * 100,
        weeklyProgress,
        trends,
        recommendations,
      };

      logger.info('Training plan progress analyzed', { planId, overallProgress: analysis.overallProgress });
      return analysis;
    } catch (error) {
      logger.error('Failed to analyze training plan progress', { error, planId });
      throw error;
    }
  }

  /**
   * Delete training plan
   */
  public async deleteTrainingPlan(id: string): Promise<RehabilitationPlan> {
    try {
      const plan = await this.trainingPlanRepository.deleteTrainingPlan(id);
      return plan;
    } catch (error) {
      logger.error('Failed to delete training plan', { error, id });
      throw error;
    }
  }

  /**
   * Validate training plan configuration
   */
  private validatePlanConfig(planConfig: any): void {
    if (!planConfig) {
      throw new Error('Plan configuration is required');
    }

    if (!planConfig.exercises || !Array.isArray(planConfig.exercises)) {
      throw new Error('Plan configuration must include exercises array');
    }

    if (planConfig.exercises.length === 0) {
      throw new Error('Plan must include at least one exercise');
    }

    // Validate each exercise
    for (const exercise of planConfig.exercises) {
      if (!exercise.name || !exercise.type) {
        throw new Error('Each exercise must have a name and type');
      }

      if (!exercise.duration && !exercise.repetitions) {
        throw new Error('Each exercise must specify duration or repetitions');
      }
    }
  }

  /**
   * Analyze training plan compatibility with user criteria
   */
  private analyzeTrainingPlanCompatibility(
    plan: RehabilitationPlan,
    criteria: RecommendationCriteria
  ): {
    overallScore: number;
    difficultyMatch: number;
    timeCompatibility: number;
    targetMatch: number;
  } {
    // Difficulty match (0-100)
    const difficultyDiff = Math.abs(plan.difficultyLevel - criteria.difficultyPreference);
    const difficultyMatch = Math.max(0, 100 - (difficultyDiff * 20));

    // Time compatibility (0-100)
    const requiredTime = plan.sessionsPerWeek * plan.sessionDurationMinutes;
    const timeRatio = criteria.timeAvailability / requiredTime;
    let timeCompatibility = 0;
    if (timeRatio >= 1) {
      timeCompatibility = 100;
    } else if (timeRatio >= 0.8) {
      timeCompatibility = 80;
    } else if (timeRatio >= 0.6) {
      timeCompatibility = 60;
    } else if (timeRatio >= 0.4) {
      timeCompatibility = 40;
    } else {
      timeCompatibility = 20;
    }

    // Target condition match (0-100)
    const targetMatch = plan.targetCondition === criteria.currentCondition ? 100 : 50;

    // Calculate overall score
    const overallScore = (difficultyMatch * 0.4) + (timeCompatibility * 0.4) + (targetMatch * 0.2);

    return {
      overallScore,
      difficultyMatch,
      timeCompatibility,
      targetMatch,
    };
  }

  /**
   * Generate recommendation reasons
   */
  private generateRecommendationReasons(
    plan: RehabilitationPlan,
    criteria: RecommendationCriteria,
    analysis: any
  ): string[] {
    const reasons: string[] = [];

    // Difficulty reasons
    if (analysis.difficultyMatch >= 80) {
      reasons.push(`Perfect difficulty match for your current level (${criteria.difficultyPreference})`);
    } else if (analysis.difficultyMatch >= 60) {
      reasons.push('Good difficulty match with room for growth');
    }

    // Time compatibility reasons
    if (analysis.timeCompatibility >= 80) {
      reasons.push(`Fits well within your available time (${criteria.timeAvailability} minutes/week)`);
    } else if (analysis.timeCompatibility >= 60) {
      reasons.push('Manageable time commitment with some flexibility needed');
    }

    // Target condition reasons
    if (analysis.targetMatch === 100) {
      reasons.push(`Specifically designed for ${criteria.currentCondition}`);
    }

    // Plan-specific reasons
    if (plan.estimatedDurationWeeks <= 4) {
      reasons.push('Short-term plan for quick results');
    } else if (plan.estimatedDurationWeeks >= 8) {
      reasons.push('Comprehensive long-term rehabilitation program');
    }

    if (plan.sessionsPerWeek <= 3) {
      reasons.push('Moderate session frequency for sustainable progress');
    } else {
      reasons.push('Intensive training schedule for accelerated improvement');
    }

    return reasons;
  }

  /**
   * Calculate weekly progress breakdown
   */
  private calculateWeeklyProgress(progress: any): Array<{
    week: number;
    sessionsCompleted: number;
    averageScore: number;
    averageAccuracy: number;
  }> {
    // Simplified weekly progress calculation
    const weeks = Math.max(1, Math.ceil(progress.totalSessions / 3)); // Assume 3 sessions per week
    const weeklyProgress = [];

    for (let week = 1; week <= weeks; week++) {
      weeklyProgress.push({
        week,
        sessionsCompleted: Math.min(3, progress.completedSessions - ((week - 1) * 3)),
        averageScore: progress.averageScore + (Math.random() - 0.5) * 10, // Add some variation
        averageAccuracy: progress.averageAccuracy + (Math.random() - 0.5) * 0.1,
      });
    }

    return weeklyProgress;
  }

  /**
   * Generate progress-based recommendations
   */
  private generateProgressRecommendations(progress: any, trends: any): string[] {
    const recommendations: string[] = [];

    // Completion rate recommendations
    if (progress.completionRate < 0.5) {
      recommendations.push('Consider reducing session frequency or duration to improve consistency');
    } else if (progress.completionRate > 0.8) {
      recommendations.push('Excellent consistency! Consider increasing difficulty level');
    }

    // Score trend recommendations
    if (trends.scoreImprovement > 5) {
      recommendations.push('Great improvement in performance scores! Keep up the excellent work');
    } else if (trends.scoreImprovement < -5) {
      recommendations.push('Performance scores declining. Consider reviewing technique or reducing intensity');
    }

    return recommendations;
  }
}
