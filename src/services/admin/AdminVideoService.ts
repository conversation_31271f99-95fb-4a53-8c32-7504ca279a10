import { PrismaClient, TrainingVideo } from '@prisma/client';
import { notFound, conflict } from '@/middleware/errorHandler';
import { videoQueue, VideoJobData } from '@/queues/videoQueue';
import { logger } from '@/utils/logger';

const prisma = new PrismaClient();

export interface CreateVideoData {
  title: string;
  description?: string;
  fileName: string;
  filePath: string; // Temp file path
  fileSize: number;
  category?: string;
  relatedId?: string;
  uploaderId: string;
}

export class AdminVideoService {
  /**
   * Creates a new video record and queues it for processing.
   */
  public async createAndQueueVideo(videoData: CreateVideoData): Promise<TrainingVideo> {
    try {
      // 1. Create a video record in the database with 'uploading' status
      const video = await prisma.trainingVideo.create({
        data: {
          title: videoData.title,
          description: videoData.description || null,
          fileName: videoData.fileName,
          filePath: videoData.filePath, // Store temp path initially
          fileSize: videoData.fileSize,
          category: videoData.category || 'general',
          relatedId: videoData.relatedId || null,
          uploadStatus: 'uploading',
          createdBy: videoData.uploaderId,
          isActive: false, // Will be activated by the worker upon successful processing
        },
      });

      logger.info(`Video record created with id: ${video.id}. Queuing for processing...`);

      // 2. Add a job to the video processing queue
      const jobData: VideoJobData = {
        videoId: video.id,
        filePath: video.filePath,
      };
      await videoQueue.add('process-video', jobData);

      logger.info(`Video id: ${video.id} has been added to the processing queue.`);
      
      return video;

    } catch (error) {
      logger.error('Error creating and queuing video:', error);
      // We might need to implement a cleanup for the stored temp file here
      // if the database transaction fails.
      throw error;
    }
  }
} 