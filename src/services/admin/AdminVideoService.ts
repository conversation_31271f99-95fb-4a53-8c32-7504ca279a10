import { PrismaClient, TrainingVideo, Prisma } from '@prisma/client';
import { notFound, conflict } from '@/middleware/errorHandler';
import { videoQueue, VideoJobData } from '@/queues/videoQueue';
import { logger } from '@/utils/logger';

const prisma = new PrismaClient();

export interface CreateVideoData {
  title: string;
  description?: string;
  fileName: string;
  filePath: string; // Temp file path
  fileSize: number;
  category?: string;
  relatedId?: string;
  uploaderId: string;
}

export interface FindVideosQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  category?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class AdminVideoService {
  /**
   * Creates a new video record and queues it for processing.
   */
  public async createAndQueueVideo(videoData: CreateVideoData): Promise<TrainingVideo> {
    try {
      // 1. Create a video record in the database with 'uploading' status
      const video = await prisma.trainingVideo.create({
        data: {
          title: videoData.title,
          fileName: videoData.fileName,
          filePath: videoData.filePath, // Store temp path initially
          fileType: videoData.fileName.split('.').pop() || 'mp4',
          fileSize: videoData.fileSize,
          status: 'pending', // pending, processing, ready, error
          uploadedBy: videoData.uploaderId,
        },
      });

      logger.info(`Video record created with id: ${video.id}. Queuing for processing...`);

      // 2. Add a job to the video processing queue
      const jobData: VideoJobData = {
        videoId: video.id,
        filePath: video.filePath,
      };
      await videoQueue.add('process-video', jobData);

      logger.info(`Video id: ${video.id} has been added to the processing queue.`);
      
      return video;

    } catch (error) {
      logger.error('Error creating and queuing video:', error);
      // We might need to implement a cleanup for the stored temp file here
      // if the database transaction fails.
      throw error;
    }
  }

  /**
   * Get videos with pagination and filtering
   */
  public async findAllVideos(query: FindVideosQuery) {
    const {
      page: pageParam = 1,
      pageSize: pageSizeParam = 10,
      search,
      category,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = query;

    // Convert string parameters to numbers
    const page = typeof pageParam === 'string' ? parseInt(pageParam, 10) : pageParam;
    const pageSize = typeof pageSizeParam === 'string' ? parseInt(pageSizeParam, 10) : pageSizeParam;

    const where: Prisma.TrainingVideoWhereInput = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { fileName: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (status) {
      where.status = status;
    }

    const orderBy: Prisma.TrainingVideoOrderByWithRelationInput = {};
    if (sortBy === 'createdAt') {
      orderBy.createdAt = sortOrder;
    } else if (sortBy === 'title') {
      orderBy.title = sortOrder;
    } else if (sortBy === 'fileSize') {
      orderBy.fileSize = sortOrder;
    } else {
      orderBy.createdAt = sortOrder;
    }

    const [videos, total] = await Promise.all([
      prisma.trainingVideo.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy,
        include: {
          uploader: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          updater: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        }
      }),
      prisma.trainingVideo.count({ where })
    ]);

    return {
      data: videos,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  /**
   * Get video by ID
   */
  public async findVideoById(id: string): Promise<TrainingVideo> {
    const video = await prisma.trainingVideo.findUnique({
      where: { id },
      include: {
        uploader: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        updater: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    });

    if (!video) {
      throw notFound('Video not found');
    }

    return video;
  }

  /**
   * Delete video
   */
  public async deleteVideo(id: string): Promise<void> {
    try {
      await prisma.trainingVideo.delete({ where: { id } });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw notFound('Video not found');
      }
      throw error;
    }
  }
}