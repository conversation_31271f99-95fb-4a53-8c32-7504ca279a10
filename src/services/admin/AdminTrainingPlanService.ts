import { PrismaClient, TrainingPlanAdmin, Prisma } from '@prisma/client';
import { notFound, conflict } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

const prisma = new PrismaClient();

export interface ActionPointInput {
  order: number;
  description: string;
}

export interface CreatePlanData {
  name: string;
  description?: string;
  difficultyLevel?: 'beginner' | 'intermediate' | 'advanced';
  durationMinutes?: number;
  isActive?: boolean;
  sortOrder?: number;
  adminId: string;
  // New fields for the "Training Package"
  videoId?: string;
  actionPoints?: ActionPointInput[];
}

export interface UpdatePlanData extends Partial<Omit<CreatePlanData, 'adminId'>> {
  adminId: string;
}

export interface FindPlansQuery {
  page?: number;
  limit?: number;
  search?: string;
  difficulty?: string;
  status?: 'active' | 'inactive';
}

export class AdminTrainingPlanService {
  public async createPlan(planData: CreatePlanData): Promise<TrainingPlanAdmin> {
    const { adminId, videoId, actionPoints, ...rest } = planData;

    return prisma.$transaction(async (tx) => {
      // Step 1: Create the core training plan
      const plan = await tx.trainingPlanAdmin.create({
        data: {
          ...rest,
          createdBy: adminId,
          updatedBy: adminId,
          videoId: videoId,
        },
      });

      // Step 2: Create associated action points, if they exist
      if (actionPoints && actionPoints.length > 0) {
        await tx.trainingActionPoint.createMany({
          data: actionPoints.map((ap) => ({
            ...ap,
            planId: plan.id,
            createdBy: adminId, // Assuming admin who creates the plan creates the points
            updatedBy: adminId,
          })),
        });
      }

      // Step 3: Return the complete plan with relations
      // We need to query again to get the included relations after creation
      const completePlan = await tx.trainingPlanAdmin.findUnique({
        where: { id: plan.id },
        include: {
          actionPoints: true,
          video: true,
        },
      });

      if (!completePlan) {
        // This should not happen in a transaction, but as a safeguard
        throw new Error('Failed to retrieve the created plan.');
      }

      return completePlan;
    });
  }

  public async findAllPlans(query: FindPlansQuery) {
    const { page = 1, limit = 10, search, difficulty, status } = query;
    const where: Prisma.TrainingPlanAdminWhereInput = {};

    if (search) {
      where.name = { contains: search, mode: 'insensitive' };
    }
    if (difficulty) {
      where.difficultyLevel = difficulty;
    }
    if (status) {
      where.isActive = status === 'active';
    }

    const [plans, total] = await Promise.all([
      prisma.trainingPlanAdmin.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { sortOrder: 'asc' },
        include: {
          video: true, // Include related video
          _count: {
            select: { actionPoints: true }, // Count related action points
          },
        },
      }),
      prisma.trainingPlanAdmin.count({ where }),
    ]);

    return {
      data: plans,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  public async findPlanById(id: string): Promise<TrainingPlanAdmin> {
    const plan = await prisma.trainingPlanAdmin.findUnique({
      where: { id },
      include: {
        actionPoints: {
          orderBy: { order: 'asc' },
        },
        video: true,
      },
    });
    if (!plan) {
      throw notFound('Training plan not found');
    }
    return plan;
  }

  public async updatePlan(id: string, planData: UpdatePlanData): Promise<TrainingPlanAdmin> {
    const { adminId, videoId, actionPoints, ...rest } = planData;

    return prisma.$transaction(async (tx) => {
      // Step 1: Update the core training plan details
      await tx.trainingPlanAdmin.update({
        where: { id },
        data: {
          ...rest,
          updatedBy: adminId,
          videoId: videoId,
        },
      });

      // Step 2: Handle action points - remove old ones and create new ones
      if (actionPoints) {
        // First, delete all existing action points for this plan
        await tx.trainingActionPoint.deleteMany({
          where: { planId: id },
        });

        // Then, create the new ones if there are any
        if (actionPoints.length > 0) {
          await tx.trainingActionPoint.createMany({
            data: actionPoints.map((ap) => ({
              ...ap,
              planId: id,
              createdBy: adminId,
              updatedBy: adminId,
            })),
          });
        }
      }

      // Step 3: Return the complete updated plan with relations
      const completePlan = await tx.trainingPlanAdmin.findUnique({
        where: { id },
        include: {
          actionPoints: { orderBy: { order: 'asc' } },
          video: true,
        },
      });

      if (!completePlan) {
        throw notFound('Training plan not found after update.');
      }

      return completePlan;
    });
  }

  public async deletePlan(id: string): Promise<void> {
    try {
      await prisma.trainingPlanAdmin.delete({ where: { id } });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw notFound('Training plan not found');
      }
      throw error;
    }
  }
} 