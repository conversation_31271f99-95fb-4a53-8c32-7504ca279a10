import { Request, Response, NextFunction } from 'express';
import { PrismaClient, AdminUser } from '@prisma/client';
import { passwordService } from '@/utils/password';
import { jwtService, TokenPair } from '@/utils/jwt';
import { logger } from '@/utils/logger';
import { badRequest, conflict, notFound, unauthorized } from '@/middleware/errorHandler';
import crypto, { createHash } from 'crypto';

const prisma = new PrismaClient();

export interface AdminLoginData {
  username: string;
  password: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface AdminWithTokens {
  admin: Omit<AdminUser, 'passwordHash'>;
  tokens: TokenPair;
}

export interface CreateAdminData {
  username: string;
  password: string;
  email?: string;
  fullName?: string;
  role?: 'super_admin' | 'admin' | 'editor';
  permissions?: string[];
}

export class AdminAuthService {
  /**
   * Authenticate admin with username and password
   */
  async login(loginData: AdminLoginData): Promise<AdminWithTokens> {
    try {
      // Find admin by username
      const admin = await prisma.adminUser.findUnique({
        where: { username: loginData.username },
      });

      if (!admin) {
        throw unauthorized('Invalid credentials');
      }

      if (!admin.isActive) {
        throw unauthorized('Admin account is inactive');
      }

      // Verify password
      const isValidPassword = await passwordService.verifyPassword(
        loginData.password.trim(),
        admin.passwordHash
      );

      if (!isValidPassword) {
        throw unauthorized('Invalid credentials');
      }

      // Generate tokens with admin-specific payload
      const tokens = jwtService.generateTokenPair({
        sub: admin.id,
        username: admin.username,
        email: admin.email ?? undefined,
        role: admin.role,
        permissions: admin.permissions as string[] || [],
        type: 'admin', // Mark as admin token
      });

      // Store the refresh token securely
      await this.storeRefreshToken(admin.id, tokens.refreshToken, {
        ipAddress: loginData.ipAddress,
        userAgent: loginData.userAgent,
      });

      // Update last login
      await prisma.adminUser.update({
        where: { id: admin.id },
        data: { lastLoginAt: new Date() },
      });

      logger.info('Admin logged in successfully', {
        adminId: admin.id,
        username: admin.username,
        role: admin.role,
        ipAddress: loginData.ipAddress,
      });

      // Remove password hash from response
      const { passwordHash: _, ...adminWithoutPassword } = admin;

      return {
        admin: adminWithoutPassword,
        tokens,
      };
    } catch (error) {
      logger.error('Admin login failed:', error);
      throw error;
    }
  }

  /**
   * Refresh admin access token
   */
  async refreshToken(refreshToken: string, ipAddress?: string): Promise<TokenPair> {
    try {
      // 1. Verify and decode refresh token
      const payload = jwtService.verifyRefreshToken(refreshToken);

      if (payload.type !== 'admin') {
        throw unauthorized('Invalid token type for admin refresh');
      }

      // 2. Check if token exists in DB and is not used
      const storedToken = await prisma.adminToken.findFirst({
        where: {
          adminId: payload.sub,
          tokenHash: this.hashToken(refreshToken),
          usedAt: null,
          expiresAt: { gt: new Date() },
        },
      });

      if (!storedToken) {
        throw unauthorized('Refresh token not found or already used');
      }

      // 3. Mark the refresh token as used
      await prisma.adminToken.update({
        where: { id: storedToken.id },
        data: { usedAt: new Date() },
      });

      // 4. Find admin to generate new tokens
      const admin = await prisma.adminUser.findUnique({ where: { id: payload.sub } });
      if (!admin || !admin.isActive) {
        throw unauthorized('Admin not found or inactive');
      }

      // 5. Generate new token pair
      const newTokens = jwtService.generateTokenPair({
        sub: admin.id,
        username: admin.username,
        email: admin.email ?? undefined,
        role: admin.role,
        permissions: admin.permissions as string[] || [],
        type: 'admin',
      });

      // 6. Store new refresh token
      await this.storeRefreshToken(admin.id, newTokens.refreshToken, { ipAddress, userAgent: undefined });

      logger.info(`Admin token refreshed for ${admin.username}`, { adminId: admin.id });
      return newTokens;

    } catch (error) {
      logger.error('Admin token refresh failed:', error);
      throw unauthorized('Invalid or expired refresh token');
    }
  }

  /**
   * Log out an admin by invalidating their refresh token
   */
  async logout(adminId: string, refreshToken?: string): Promise<void> {
    try {
      if (!refreshToken) {
        // If no refresh token is provided, invalidate all for the user
        await prisma.adminToken.updateMany({
          where: { adminId, usedAt: null },
          data: { usedAt: new Date() },
        });
        logger.info(`All refresh tokens invalidated for admin ${adminId} on logout.`);
      } else {
        const tokenHash = this.hashToken(refreshToken);
        await prisma.adminToken.updateMany({
          where: { adminId, tokenHash, usedAt: null },
          data: { usedAt: new Date() },
        });
        logger.info(`Refresh token invalidated for admin ${adminId} on logout.`);
      }
    } catch (error) {
      logger.error(`Admin logout failed for adminId ${adminId}:`, error);
      // Don't throw error on logout failure, just log it
    }
  }

  /**
   * Create a new admin user
   */
  async createAdmin(adminData: CreateAdminData): Promise<Omit<AdminUser, 'passwordHash'>> {
    try {
      // Check for existing username
      const existingAdmin = await prisma.adminUser.findUnique({
        where: { username: adminData.username },
      });
      if (existingAdmin) {
        throw conflict('Admin with this username already exists');
      }

      const passwordHash = await passwordService.hashPassword(adminData.password);
      
      const newAdmin = await prisma.adminUser.create({
        data: {
          username: adminData.username,
          passwordHash,
          email: adminData.email,
          fullName: adminData.fullName,
          role: adminData.role || 'admin',
          permissions: adminData.permissions || this.getDefaultPermissions(adminData.role || 'admin'),
        },
      });

      logger.info('New admin created', { username: newAdmin.username, role: newAdmin.role });
      const { passwordHash: _, ...adminWithoutPassword } = newAdmin;
      return adminWithoutPassword;
    } catch (error) {
      logger.error('Failed to create admin:', error);
      if (error instanceof Error && 'code' in error && error.code === 'P2002') {
        throw conflict('Admin with this username or email already exists');
      }
      throw error;
    }
  }

  /**
   * Get an admin user by ID
   */
  async getAdminById(adminId: string): Promise<Omit<AdminUser, 'passwordHash'> | null> {
    const admin = await prisma.adminUser.findUnique({
      where: { id: adminId },
      select: {
        id: true,
        username: true,
        email: true,
        fullName: true,
        role: true,
        permissions: true,
        isActive: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });
    if (!admin) {
      throw notFound('Admin not found');
    }
    return admin;
  }

  /**
   * Stores the refresh token hash in the database
   */
  private async storeRefreshToken(
    adminId: string,
    refreshToken: string,
    { ipAddress, userAgent }: { ipAddress?: string; userAgent?: string },
  ): Promise<void> {
    const refreshTokenExpiry = jwtService.getTokenExpiry(refreshToken);
    if (!refreshTokenExpiry) {
      throw new Error('Could not determine refresh token expiry');
    }

    const tokenHash = createHash('sha256').update(refreshToken).digest('hex');

    await prisma.adminToken.create({
      data: {
        adminId,
        token: refreshToken,
        tokenHash,
        type: 'refresh_token',
        expiresAt: refreshTokenExpiry,
        ipAddress: ipAddress,
        userAgent: userAgent,
      },
    });
  }

  /**
   * Hashes a token using SHA256 for storage
   */
  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Get default permissions based on role
   */
  private getDefaultPermissions(role: string): string[] {
    switch (role) {
      case 'super_admin':
        return ['*'];
      case 'admin':
        return ['read', 'write', 'delete'];
      case 'editor':
        return ['read', 'write'];
      default:
        return ['read'];
    }
  }
} 