/**
 * Data Cleanup Scheduler
 * Handles automatic cleanup of old training data
 */

import { TrainingRecordService } from './TrainingRecordService';
import { logger } from '@/utils/logger';

export interface CleanupScheduleConfig {
  enabled: boolean;
  intervalHours: number;
  retentionDays: number;
  batchSize: number;
  maxExecutionTimeMinutes: number;
}

export interface CleanupResult {
  startTime: Date;
  endTime: Date;
  deletedSessions: number;
  deletedDataPoints: number;
  executionTimeMs: number;
  success: boolean;
  error?: string;
}

export class DataCleanupScheduler {
  private trainingRecordService: TrainingRecordService;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;
  private config: CleanupScheduleConfig;
  private lastCleanup: Date | null = null;
  private cleanupHistory: CleanupResult[] = [];

  constructor(config?: Partial<CleanupScheduleConfig>) {
    this.trainingRecordService = new TrainingRecordService();
    this.config = {
      enabled: config?.enabled ?? true,
      intervalHours: config?.intervalHours ?? 24, // Run daily
      retentionDays: config?.retentionDays ?? 30,
      batchSize: config?.batchSize ?? 1000,
      maxExecutionTimeMinutes: config?.maxExecutionTimeMinutes ?? 30,
      ...config,
    };
  }

  /**
   * Start the cleanup scheduler
   */
  public start(): void {
    if (this.intervalId) {
      logger.warn('Data cleanup scheduler is already running');
      return;
    }

    if (!this.config.enabled) {
      logger.info('Data cleanup scheduler is disabled');
      return;
    }

    logger.info('Starting data cleanup scheduler', {
      intervalHours: this.config.intervalHours,
      retentionDays: this.config.retentionDays,
    });

    // Run immediately on start
    this.runCleanup();

    // Schedule recurring cleanup
    const intervalMs = this.config.intervalHours * 60 * 60 * 1000;
    this.intervalId = setInterval(() => {
      this.runCleanup();
    }, intervalMs);

    logger.info('Data cleanup scheduler started successfully');
  }

  /**
   * Stop the cleanup scheduler
   */
  public stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('Data cleanup scheduler stopped');
    }
  }

  /**
   * Run cleanup manually
   */
  public async runCleanup(): Promise<CleanupResult> {
    if (this.isRunning) {
      logger.warn('Data cleanup is already running, skipping this execution');
      throw new Error('Cleanup is already in progress');
    }

    this.isRunning = true;
    const startTime = new Date();

    logger.info('Starting scheduled data cleanup', {
      retentionDays: this.config.retentionDays,
      maxExecutionTime: this.config.maxExecutionTimeMinutes,
    });

    try {
      // Set execution timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Cleanup execution exceeded ${this.config.maxExecutionTimeMinutes} minutes`));
        }, this.config.maxExecutionTimeMinutes * 60 * 1000);
      });

      // Run cleanup with timeout
      const cleanupPromise = this.performCleanup();
      const result = await Promise.race([cleanupPromise, timeoutPromise]);

      const endTime = new Date();
      const executionTimeMs = endTime.getTime() - startTime.getTime();

      const cleanupResult: CleanupResult = {
        startTime,
        endTime,
        deletedSessions: result.deletedSessions,
        deletedDataPoints: result.deletedDataPoints,
        executionTimeMs,
        success: true,
      };

      this.lastCleanup = endTime;
      this.addToHistory(cleanupResult);

      logger.info('Scheduled data cleanup completed successfully', {
        deletedSessions: result.deletedSessions,
        deletedDataPoints: result.deletedDataPoints,
        executionTimeMs,
      });

      return cleanupResult;
    } catch (error) {
      const endTime = new Date();
      const executionTimeMs = endTime.getTime() - startTime.getTime();

      const cleanupResult: CleanupResult = {
        startTime,
        endTime,
        deletedSessions: 0,
        deletedDataPoints: 0,
        executionTimeMs,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };

      this.addToHistory(cleanupResult);

      logger.error('Scheduled data cleanup failed', {
        error: error instanceof Error ? error.message : error,
        executionTimeMs,
      });

      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get cleanup status and statistics
   */
  public getStatus(): {
    isRunning: boolean;
    isEnabled: boolean;
    lastCleanup: Date | null;
    nextCleanup: Date | null;
    config: CleanupScheduleConfig;
    recentHistory: CleanupResult[];
    statistics: {
      totalCleanups: number;
      successfulCleanups: number;
      failedCleanups: number;
      totalDeletedSessions: number;
      totalDeletedDataPoints: number;
      averageExecutionTimeMs: number;
    };
    } {
    const nextCleanup = this.lastCleanup && this.config.enabled
      ? new Date(this.lastCleanup.getTime() + (this.config.intervalHours * 60 * 60 * 1000))
      : null;

    const recentHistory = this.cleanupHistory.slice(-10); // Last 10 cleanups

    const statistics = {
      totalCleanups: this.cleanupHistory.length,
      successfulCleanups: this.cleanupHistory.filter(r => r.success).length,
      failedCleanups: this.cleanupHistory.filter(r => !r.success).length,
      totalDeletedSessions: this.cleanupHistory.reduce((sum, r) => sum + r.deletedSessions, 0),
      totalDeletedDataPoints: this.cleanupHistory.reduce((sum, r) => sum + r.deletedDataPoints, 0),
      averageExecutionTimeMs: this.cleanupHistory.length > 0
        ? this.cleanupHistory.reduce((sum, r) => sum + r.executionTimeMs, 0) / this.cleanupHistory.length
        : 0,
    };

    return {
      isRunning: this.isRunning,
      isEnabled: this.config.enabled,
      lastCleanup: this.lastCleanup,
      nextCleanup,
      config: this.config,
      recentHistory,
      statistics,
    };
  }

  /**
   * Update scheduler configuration
   */
  public updateConfig(newConfig: Partial<CleanupScheduleConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    logger.info('Data cleanup scheduler configuration updated', {
      oldConfig,
      newConfig: this.config,
    });

    // Restart scheduler if interval changed
    if (oldConfig.intervalHours !== this.config.intervalHours && this.intervalId) {
      this.stop();
      this.start();
    }
  }

  /**
   * Perform the actual cleanup operation
   */
  private async performCleanup(): Promise<{ deletedSessions: number; deletedDataPoints: number }> {
    try {
      const result = await this.trainingRecordService.cleanupAllData();
      return {
        deletedSessions: result.deletedSessions,
        deletedDataPoints: result.deletedDataPoints,
      };
    } catch (error) {
      logger.error('Failed to perform data cleanup', { error });
      throw error;
    }
  }

  /**
   * Add cleanup result to history
   */
  private addToHistory(result: CleanupResult): void {
    this.cleanupHistory.push(result);

    // Keep only last 100 results to prevent memory issues
    if (this.cleanupHistory.length > 100) {
      this.cleanupHistory = this.cleanupHistory.slice(-100);
    }
  }

  /**
   * Get cleanup history
   */
  public getHistory(limit: number = 10): CleanupResult[] {
    return this.cleanupHistory.slice(-limit);
  }

  /**
   * Clear cleanup history
   */
  public clearHistory(): void {
    this.cleanupHistory = [];
    logger.info('Data cleanup history cleared');
  }

  /**
   * Check if cleanup is needed based on last execution time
   */
  public isCleanupNeeded(): boolean {
    if (!this.config.enabled) return false;
    if (!this.lastCleanup) return true;

    const timeSinceLastCleanup = Date.now() - this.lastCleanup.getTime();
    const intervalMs = this.config.intervalHours * 60 * 60 * 1000;

    return timeSinceLastCleanup >= intervalMs;
  }

  /**
   * Get time until next cleanup
   */
  public getTimeUntilNextCleanup(): number | null {
    if (!this.config.enabled || !this.lastCleanup) return null;

    const intervalMs = this.config.intervalHours * 60 * 60 * 1000;
    const nextCleanupTime = this.lastCleanup.getTime() + intervalMs;
    const timeUntilNext = nextCleanupTime - Date.now();

    return Math.max(0, timeUntilNext);
  }
}

// Export singleton instance
export const dataCleanupScheduler = new DataCleanupScheduler();
