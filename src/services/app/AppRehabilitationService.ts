import { PrismaClient, TrainingPlanAdmin } from '@prisma/client';
import { notFound } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

const prisma = new PrismaClient();

// --- Data Transformation Layers ---
// These interfaces and functions will format the DB data
// to strictly match the mock data specification.

interface AppTrainingPlan {
  id: string;
  name: string;
  description: string;
  duration: number;
  status: string; // Mock status, as it's user-specific
  progress: number; // Mock progress, as it's user-specific
}

interface AppActionPoint {
  order: number;
  description: string;
}

interface AppTrainingVideo {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl: string;
  duration: number;
  trainingType: string; // We can use the plan's difficulty as a mock type
}

interface AppTrainingPlanDetails extends AppTrainingPlan {
  actionPoints: AppActionPoint[];
  video: AppTrainingVideo | null;
}

function toAppTrainingPlan(plan: TrainingPlanAdmin): AppTrainingPlan {
  return {
    id: plan.id,
    name: plan.name,
    description: plan.description || '',
    duration: plan.durationMinutes || 0,
    // Mock data as per spec, since status & progress are user-specific
    // and not stored in the admin plan template.
    status: '未开始',
    progress: 0.0,
  };
}

function toAppTrainingPlanDetails(
  plan: TrainingPlanAdmin & { actionPoints: any[]; video: any | null },
): AppTrainingPlanDetails {
  return {
    ...toAppTrainingPlan(plan),
    actionPoints: plan.actionPoints.map((ap) => ({
      order: ap.order,
      description: ap.description,
    })),
    video: plan.video
      ? {
          id: plan.video.id,
          title: plan.video.title,
          description: plan.video.description || '',
          thumbnailUrl: plan.video.thumbnailUrl || 'assets/images/rehabilitation/placeholder.png',
          videoUrl: plan.video.filePath, // Maps db filePath to videoUrl
          duration: plan.video.durationSeconds || 0,
          trainingType: plan.difficultyLevel || 'general_training',
        }
      : null,
  };
}


// --- Service Class ---

export class AppRehabilitationService {
  /**
   * Get a list of all active training plans formatted for the app.
   */
  public async getTrainingPlansList(): Promise<AppTrainingPlan[]> {
    const plans = await prisma.trainingPlanAdmin.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' },
    });
    return plans.map(toAppTrainingPlan);
  }

  /**
   * Get the full details of a single training plan by its ID, formatted for the app.
   */
  public async getTrainingPlanDetails(id: string): Promise<AppTrainingPlanDetails> {
    const plan = await prisma.trainingPlanAdmin.findUnique({
      where: { id, isActive: true },
      include: {
        actionPoints: {
          orderBy: { order: 'asc' },
        },
        video: true,
      },
    });

    if (!plan) {
      throw notFound('Active training plan not found');
    }

    return toAppTrainingPlanDetails(plan);
  }
} 