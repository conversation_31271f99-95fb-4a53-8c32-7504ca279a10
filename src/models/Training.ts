/**
 * Training related models and interfaces
 * Based on the comprehensive backend development guide specifications
 */

import { BaseEntity } from './index';

// Training Session Models (from comprehensive guide)
export interface TrainingSession extends BaseEntity {
  userId: string;
  trainingPlanId?: string;
  trainingPlanName: string;
  trainingType: 'rehabilitation' | 'game' | 'assessment';
  startTime: Date;
  endTime?: Date;
  durationSeconds: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'cancelled' | 'paused';
  averageGripStrength: number;
  maxGripStrength: number;
  averageAccuracy: number;
  totalScore: number;
  completedActions: number;
  completionRate: number;
  metadata: Record<string, any>;
}

// Real-time Training Data (from comprehensive guide)
export interface RealTimeTrainingData extends BaseEntity {
  sessionId: string;
  timestamp: Date;
  gripStrength: number;
  actionAccuracy: number;
  targetGripStrength: number;
  targetAccuracy: number;
  score: number;
  metadata: Record<string, any>;
}

// Training Record (from comprehensive guide)
export interface TrainingRecord extends BaseEntity {
  userId: string;
  sessionId: string;
  trainingPlanId?: string;
  trainingPlanName: string;
  trainingDate: Date;
  startTime: Date;
  endTime?: Date;
  durationMinutes: number;
  completionRate: number;
  accuracyRate: number;
  averageGripStrength?: number;
  maxGripStrength?: number;
  totalScore: number;
  completedActions: number;
  status: 'completed' | 'incomplete';
}

// User Goals (from comprehensive guide)
export interface UserGoal extends BaseEntity {
  userId: string;
  goalDate: Date;
  targetDuration: number; // minutes
  targetActions: number;
  targetAccuracy: number;
  currentDuration: number;
  currentActions: number;
  currentAccuracy: number;
  isCompleted: boolean;
}

// Training Plan Models
export interface TrainingPlan extends BaseEntity {
  userId: string;
  name: string;
  description: string;
  type: 'rehabilitation' | 'fitness' | 'maintenance';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in weeks
  status: 'draft' | 'active' | 'completed' | 'paused' | 'cancelled';
  startDate?: Date;
  endDate?: Date;
  targetAreas: string[];
  goals: string[];
  createdBy: string; // therapist or system
  approvedBy?: string; // supervising therapist
  weeklySchedule: WeeklySchedule[];
  exercises: Exercise[];
}

export interface WeeklySchedule {
  week: number;
  sessions: number;
  focus: string;
  exercises: string[];
}

export interface Exercise extends BaseEntity {
  name: string;
  description: string;
  type: 'strength' | 'flexibility' | 'balance' | 'coordination' | 'cardio';
  category: string;
  targetMuscles: string[];
  instructions: string[];
  keyPoints: string[];
  commonMistakes: string[];
  equipment: string[];
  sets?: number;
  reps?: number;
  duration?: number; // in seconds
  restTime?: number; // in seconds
  weight?: number; // in kg
  resistance?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  frequency: string; // e.g., "每日1次"
  mediaUrls: {
    images: string[];
    videos: string[];
    animations: string[];
  };
  videoUrl?: string;
  imageUrl?: string;
}

// Training Statistics and Analytics
export interface TrainingStatistics {
  userId: string;
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  startDate: Date;
  endDate: Date;
  totalSessions: number;
  totalDuration: number; // minutes
  averageScore: number;
  averageAccuracy: number;
  totalActions: number;
  averageGripStrength: number;
  maxGripStrength: number;
  improvementRate: number; // percentage
  consistencyScore: number; // 0-100
  byType: {
    [type: string]: {
      sessions: number;
      duration: number;
      averageScore: number;
      averageAccuracy: number;
    };
  };
  trends: StatisticsTrend[];
}

export interface StatisticsTrend {
  date: Date;
  metric: string;
  value: number;
  change: number; // percentage change from previous period
}

// Training Recommendations
export interface TrainingRecommendation extends BaseEntity {
  userId: string;
  type: 'exercise' | 'plan' | 'goal' | 'schedule';
  title: string;
  description: string;
  reason: string;
  priority: 'low' | 'medium' | 'high';
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedDuration: number; // minutes
  targetAreas: string[];
  status: 'pending' | 'accepted' | 'declined' | 'completed';
  validUntil?: Date;
  metadata: Record<string, any>;
}

// Training Progress Tracking
export interface TrainingProgress {
  userId: string;
  planId?: string;
  overall: {
    completionRate: number; // 0-1
    averageScore: number;
    totalSessions: number;
    totalDuration: number; // in minutes
    currentStreak: number;
    longestStreak: number;
    lastSessionDate?: Date;
  };
  byCategory: {
    [category: string]: {
      sessions: number;
      averageScore: number;
      averageAccuracy: number;
      improvement: number; // percentage
      lastSession?: Date;
    };
  };
  milestones: Milestone[];
  achievements: Achievement[];
  trends: {
    weekly: ProgressDataPoint[];
    monthly: ProgressDataPoint[];
  };
}

export interface Milestone {
  id: string;
  name: string;
  description: string;
  category: string;
  target: number;
  current: number;
  unit: string;
  achievedAt?: Date;
  reward?: string;
  icon?: string;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  category: string;
  type: 'streak' | 'score' | 'duration' | 'accuracy' | 'consistency';
  requirement: number;
  earnedAt: Date;
  icon?: string;
  badge?: string;
}

export interface ProgressDataPoint {
  date: Date;
  value: number;
  category: string;
  metric: string;
  target?: number;
}

// Training Feedback and Notes
export interface TrainingFeedback {
  sessionId: string;
  userId: string;
  therapistId?: string;
  painLevel: number; // 0-10 scale
  fatigue: number; // 0-10 scale
  difficulty: number; // 0-10 scale
  satisfaction: number; // 0-10 scale
  motivation: number; // 0-10 scale
  notes?: string;
  therapistNotes?: string;
  recommendations?: string[];
  followUpRequired: boolean;
  nextSessionAdjustments?: string[];
  createdAt: Date;
}

// Training Schedule and Calendar
export interface TrainingSchedule extends BaseEntity {
  userId: string;
  planId?: string;
  title: string;
  description?: string;
  type: 'session' | 'assessment' | 'consultation' | 'break';
  scheduledAt: Date;
  duration: number; // minutes
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'rescheduled';
  location?: string;
  therapistId?: string;
  exercises?: string[]; // exercise IDs
  reminders: {
    email: boolean;
    sms: boolean;
    push: boolean;
    minutesBefore: number[];
  };
  metadata: Record<string, any>;
}

// Training Equipment and Resources
export interface TrainingEquipment extends BaseEntity {
  name: string;
  type: 'resistance' | 'cardio' | 'balance' | 'flexibility' | 'strength' | 'smart_device';
  description: string;
  manufacturer?: string;
  model?: string;
  specifications: Record<string, any>;
  maintenanceSchedule?: {
    lastMaintenance?: Date;
    nextMaintenance?: Date;
    frequency: string; // e.g., "monthly", "quarterly"
  };
  location?: string;
  status: 'available' | 'in_use' | 'maintenance' | 'out_of_order';
  imageUrl?: string;
  manualUrl?: string;
}

// Export types for API responses
export interface TodayTrainingData {
  trainingDuration: number; // minutes
  completedActions: number;
  accuracy: number; // 0-1
  score: number;
  averageGripStrength: number;
  maxGripStrength: number;
  trainingCount: number;
}

export interface WeeklyTrainingData {
  date: string; // YYYY-MM-DD
  trainingDuration: number;
  completedActions: number;
  accuracy: number;
  score: number;
  goalAchieved: boolean;
}

export interface TrainingRecordsResponse {
  records: TrainingRecord[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

// Training session creation and update DTOs
export interface CreateTrainingSessionDTO {
  userId: string;
  trainingPlanId?: string;
  trainingPlanName: string;
  trainingType: 'rehabilitation' | 'game' | 'assessment';
}

export interface UpdateTrainingSessionDTO {
  endTime: Date;
  duration: number; // seconds
  averageGripStrength: number;
  maxGripStrength: number;
  averageAccuracy: number;
  totalScore: number;
  completedActions: number;
  dataPoints: RealTimeDataPoint[];
}

export interface RealTimeDataPoint {
  timestamp: Date;
  gripStrength: number;
  actionAccuracy: number;
  targetGripStrength?: number;
  targetAccuracy?: number;
  score?: number;
}
