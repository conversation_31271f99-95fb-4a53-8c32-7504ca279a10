/**
 * Data Models for Shoutao Rehabilitation Training App
 *
 * This file defines all the data models and interfaces used throughout the application.
 * Models are organized by domain and follow the database schema specifications.
 */

// Base interfaces
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SoftDeleteEntity extends BaseEntity {
  deletedAt?: Date;
}

// User related models
export interface User extends BaseEntity {
  username: string;
  email?: string;
  phoneNumber?: string;
  passwordHash?: string;
  fullName: string;
  nickname?: string;
  avatar?: string;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other';
  height?: number; // in cm
  weight?: number; // in kg
  medicalHistory?: MedicalHistory;
  emergencyContact?: EmergencyContact;
  preferences?: UserPreferences;
  status: 'active' | 'inactive' | 'suspended';
  lastLoginAt?: Date;
  emailVerifiedAt?: Date;
  phoneVerifiedAt?: Date;
}

export interface MedicalHistory {
  conditions: string[];
  medications: string[];
  allergies: string[];
  surgeries: Array<{
    type: string;
    date: Date;
    notes?: string;
  }>;
  injuries: Array<{
    type: string;
    date: Date;
    severity: 'mild' | 'moderate' | 'severe';
    notes?: string;
  }>;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phoneNumber: string;
  email?: string;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    reminders: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'friends' | 'private';
    shareProgress: boolean;
    shareAchievements: boolean;
  };
}

// Training related models
export interface TrainingPlan extends BaseEntity {
  userId: string;
  name: string;
  description: string;
  type: 'rehabilitation' | 'fitness' | 'maintenance';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in weeks
  status: 'draft' | 'active' | 'completed' | 'paused' | 'cancelled';
  startDate?: Date;
  endDate?: Date;
  targetAreas: string[];
  goals: string[];
  createdBy: string; // therapist or system
  approvedBy?: string; // supervising therapist
  sessions: TrainingSession[];
  progress?: TrainingProgress;
}

export interface TrainingSession extends BaseEntity {
  planId?: string;
  userId: string;
  name: string;
  description?: string;
  type: 'strength' | 'flexibility' | 'balance' | 'coordination' | 'cardio' | 'cognitive';
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedDuration: number; // in minutes
  actualDuration?: number;
  exercises: Exercise[];
  status: 'scheduled' | 'in_progress' | 'completed' | 'skipped' | 'cancelled';
  scheduledAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  score?: number;
  feedback?: SessionFeedback;
  deviceData?: DeviceData[];
}

export interface Exercise extends BaseEntity {
  sessionId?: string;
  name: string;
  description: string;
  type: 'strength' | 'flexibility' | 'balance' | 'coordination' | 'cardio';
  targetMuscles: string[];
  instructions: string[];
  keyPoints: string[];
  commonMistakes: string[];
  equipment: string[];
  sets?: number;
  reps?: number;
  duration?: number; // in seconds
  restTime?: number; // in seconds
  weight?: number; // in kg
  resistance?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  mediaUrls: {
    images: string[];
    videos: string[];
    animations: string[];
  };
  completed: boolean;
  score?: number;
  notes?: string;
}

export interface SessionFeedback {
  painLevel: number; // 0-10 scale
  fatigue: number; // 0-10 scale
  difficulty: number; // 0-10 scale
  satisfaction: number; // 0-10 scale
  notes?: string;
  therapistNotes?: string;
  recommendations?: string[];
}

export interface TrainingProgress {
  userId: string;
  planId?: string;
  overall: {
    completionRate: number; // 0-1
    averageScore: number;
    totalSessions: number;
    totalDuration: number; // in minutes
    currentStreak: number;
    longestStreak: number;
  };
  byCategory: {
    [key: string]: {
      sessions: number;
      averageScore: number;
      improvement: number; // percentage
      lastSession?: Date;
    };
  };
  milestones: Milestone[];
  trends: {
    weekly: ProgressDataPoint[];
    monthly: ProgressDataPoint[];
  };
}

export interface Milestone {
  id: string;
  name: string;
  description: string;
  category: string;
  target: number;
  current: number;
  unit: string;
  achievedAt?: Date;
  reward?: string;
}

export interface ProgressDataPoint {
  date: Date;
  value: number;
  category: string;
  metric: string;
}

// Assessment related models
export interface HealthAssessment extends BaseEntity {
  userId: string;
  type: 'initial' | 'periodic' | 'post_treatment' | 'discharge';
  assessorId?: string; // therapist who conducted the assessment
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  scheduledAt?: Date;
  completedAt?: Date;
  physicalMetrics: PhysicalMetrics;
  functionalAssessment: FunctionalAssessment;
  painAssessment: PainAssessment;
  cognitiveAssessment?: CognitiveAssessment;
  recommendations: string[];
  nextAssessmentDate?: Date;
  notes?: string;
}

export interface PhysicalMetrics {
  height: number; // cm
  weight: number; // kg
  bmi: number;
  bloodPressure?: {
    systolic: number;
    diastolic: number;
  };
  heartRate?: number; // bpm
  bodyFat?: number; // percentage
  muscleMass?: number; // kg
  flexibility: {
    [joint: string]: number; // degrees
  };
  strength: {
    [muscle: string]: number; // kg or score
  };
  balance: {
    staticBalance: number; // seconds
    dynamicBalance: number; // score
  };
}

export interface FunctionalAssessment {
  mobility: {
    walking: number; // score or distance
    stairs: number; // score
    transfers: number; // score
  };
  activities: {
    dressing: number; // score
    bathing: number; // score
    eating: number; // score
    toileting: number; // score
  };
  workCapacity: {
    lifting: number; // kg
    carrying: number; // kg
    standing: number; // minutes
    sitting: number; // minutes
  };
}

export interface PainAssessment {
  currentPain: number; // 0-10 scale
  averagePain: number; // 0-10 scale
  worstPain: number; // 0-10 scale
  painLocations: Array<{
    location: string;
    intensity: number;
    type: 'sharp' | 'dull' | 'burning' | 'throbbing' | 'aching';
  }>;
  painTriggers: string[];
  painRelief: string[];
  medicationUsage: Array<{
    medication: string;
    dosage: string;
    frequency: string;
    effectiveness: number; // 0-10 scale
  }>;
}

export interface CognitiveAssessment {
  memory: number; // score
  attention: number; // score
  processing: number; // score
  problemSolving: number; // score
  language: number; // score
  overall: number; // composite score
}

// Device and IoT related models
export interface Device extends BaseEntity {
  userId?: string;
  name: string;
  type: 'sensor' | 'wearable' | 'exercise_equipment' | 'monitoring_device';
  model: string;
  manufacturer: string;
  serialNumber: string;
  firmwareVersion?: string;
  status: 'active' | 'inactive' | 'maintenance' | 'error';
  lastConnected?: Date;
  batteryLevel?: number; // 0-100
  configuration: DeviceConfiguration;
  calibration?: DeviceCalibration;
}

export interface DeviceConfiguration {
  samplingRate?: number; // Hz
  sensitivity?: number;
  range?: {
    min: number;
    max: number;
  };
  units: string;
  filters?: string[];
  customSettings?: Record<string, any>;
}

export interface DeviceCalibration {
  calibratedAt: Date;
  calibratedBy: string;
  parameters: Record<string, number>;
  accuracy: number; // percentage
  nextCalibrationDate?: Date;
}

export interface DeviceData extends BaseEntity {
  deviceId: string;
  userId: string;
  sessionId?: string;
  dataType: 'motion' | 'force' | 'pressure' | 'angle' | 'heart_rate' | 'temperature';
  timestamp: Date;
  value: number;
  unit: string;
  quality: 'good' | 'fair' | 'poor';
  metadata?: Record<string, any>;
}

// Game and gamification models
export interface Game extends BaseEntity {
  name: string;
  description: string;
  type: 'balance' | 'coordination' | 'strength' | 'cognitive' | 'reaction';
  difficulty: 'easy' | 'medium' | 'hard';
  minDuration: number; // seconds
  maxDuration: number; // seconds
  instructions: string[];
  rules: string[];
  scoring: ScoringRules;
  equipment: string[];
  targetAudience: string[];
  mediaUrls: {
    thumbnail: string;
    screenshots: string[];
    videos: string[];
  };
  isActive: boolean;
}

export interface ScoringRules {
  maxScore: number;
  timeBonus: boolean;
  accuracyWeight: number;
  speedWeight: number;
  difficultyMultiplier: number;
  bonusConditions: Array<{
    condition: string;
    bonus: number;
  }>;
}

export interface GameSession extends BaseEntity {
  gameId: string;
  userId: string;
  difficulty: 'easy' | 'medium' | 'hard';
  duration: number; // seconds
  score: number;
  maxScore: number;
  accuracy?: number; // 0-1
  completionRate: number; // 0-1
  achievements: string[];
  deviceData?: DeviceData[];
  metadata: Record<string, any>;
}

// Export all models
export * from './User';
export * from './Training';
export * from './Assessment';
export * from './Device';
export * from './Game';
