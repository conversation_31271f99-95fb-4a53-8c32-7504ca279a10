import { Worker, Job } from 'bullmq';
import IORedis from 'ioredis';
import ffmpeg from 'fluent-ffmpeg';
import { PrismaClient } from '@prisma/client';
import path from 'path';
import fs from 'fs/promises';
import { logger } from '@/utils/logger';
import { VideoJobData } from '@/queues/videoQueue';

const prisma = new PrismaClient();

const connection = new IORedis(process.env['REDIS_URL'] || 'redis://localhost:6379', {
  maxRetriesPerRequest: null,
});

const videoWorker = new Worker<VideoJobData>(
  'video-processing',
  async (job: Job<VideoJobData>) => {
    const { videoId, filePath: tempFilePath } = job.data;
    logger.info(`Processing video job ${job.id} for videoId: ${videoId}`);

    try {
      // 1. 验证视频文件是否存在
      await fs.access(tempFilePath);

      // 2. 定义最终存储路径
      const uploadsDir = path.join(__dirname, '../../../uploads');
      const videosDir = path.join(uploadsDir, 'videos');
      const thumbnailsDir = path.join(uploadsDir, 'thumbnails');
      const finalFileName = `${videoId}_${path.basename(tempFilePath)}`;
      const finalVideoPath = path.join(videosDir, finalFileName);
      const thumbnailFileName = `${videoId}_thumbnail.jpg`;
      const thumbnailPath = path.join(thumbnailsDir, thumbnailFileName);

      // 确保目录存在
      await fs.mkdir(videosDir, { recursive: true });
      await fs.mkdir(thumbnailsDir, { recursive: true });

      // 3. 获取视频元数据
      const metadata: ffmpeg.FfprobeData = await new Promise((resolve, reject) => {
        ffmpeg.ffprobe(tempFilePath, (err: any, data: ffmpeg.FfprobeData) => {
          if (err) return reject(err);
          resolve(data);
        });
      });
      const duration = metadata.format.duration || 0;

      // 4. 生成缩略图
      await new Promise((resolve, reject) => {
        ffmpeg(tempFilePath)
          .on('end', resolve)
          .on('error', reject)
          .screenshots({
            timestamps: ['10%'],
            filename: thumbnailFileName,
            folder: thumbnailsDir,
            size: '320x240',
          });
      });

      // 5. 移动视频文件
      await fs.rename(tempFilePath, finalVideoPath);

      // 6. 更新数据库记录
      await prisma.trainingVideo.update({
        where: { id: videoId },
        data: {
          filePath: `/uploads/videos/${finalFileName}`,
          thumbnailUrl: `/uploads/thumbnails/${thumbnailFileName}`,
          durationSeconds: Math.round(duration),
          format: metadata.format.format_name,
          fileSize: metadata.format.size,
          uploadStatus: 'completed',
          updatedAt: new Date(),
        },
      });

      logger.info(`Successfully processed video job ${job.id}`);
      return { status: 'success', videoId };

    } catch (error: any) {
      logger.error(`Failed to process video job ${job.id}:`, error);
      // 更新数据库记录为失败状态
      await prisma.trainingVideo.update({
        where: { id: videoId },
        data: { uploadStatus: 'failed' },
      });
      throw error; // 抛出错误以让BullMQ处理重试
    }
  },
  { connection }
);

videoWorker.on('completed', (job: Job, result: any) => {
  logger.info(`Job ${job.id} completed. Result:`, result);
});

videoWorker.on('failed', (job: Job | undefined, err: Error) => {
  if (job) {
    logger.error(`Job ${job.id} failed with error: ${err.message}`, { jobData: job.data });
  } else {
    logger.error(`An unknown job failed with error: ${err.message}`);
  }
});

logger.info('Video worker started and listening for jobs.');
