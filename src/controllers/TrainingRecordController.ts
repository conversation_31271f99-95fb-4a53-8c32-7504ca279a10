/**
 * Training Record Controller
 * Handles HTTP requests for training data storage and querying
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from './BaseController';
import { TrainingRecordService } from '@/services/TrainingRecordService';
import { logger } from '@/utils/logger';

export class TrainingRecordController extends BaseController {
  private trainingRecordService: TrainingRecordService;

  constructor() {
    super();
    this.trainingRecordService = new TrainingRecordService();
  }

  /**
   * GET /api/v1/training/records/user/:userId/daily?date=YYYY-MM-DD
   * Get daily training records for a user
   */
  public getDailyRecords = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const { date } = req.query;

      if (!date) {
        return this.sendError(res, 'VALIDATION_ERROR', 'Date parameter is required', null, 400);
      }

      // Validate date format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(date as string)) {
        return this.sendError(res, 'VALIDATION_ERROR', 'Invalid date format. Use YYYY-MM-DD', null, 400);
      }

      this.logAction('GET_DAILY_TRAINING_RECORDS', userId, { date });

      const records = await this.trainingRecordService.getDailyRecords(userId, date as string);

      this.sendSuccess(res, records, 'Daily training records retrieved successfully');
    }
  );

  /**
   * GET /api/v1/training/records/user/:userId/weekly?week=YYYY-WW
   * Get weekly training records for a user
   */
  public getWeeklyRecords = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const { week } = req.query;

      if (!week) {
        return this.sendError(res, 'VALIDATION_ERROR', 'Week parameter is required', null, 400);
      }

      // Validate week format
      const weekRegex = /^\d{4}-W\d{2}$/;
      if (!weekRegex.test(week as string)) {
        return this.sendError(res, 'VALIDATION_ERROR', 'Invalid week format. Use YYYY-WW', null, 400);
      }

      this.logAction('GET_WEEKLY_TRAINING_RECORDS', userId, { week });

      const records = await this.trainingRecordService.getWeeklyRecords(userId, week as string);

      this.sendSuccess(res, records, 'Weekly training records retrieved successfully');
    }
  );

  /**
   * GET /api/v1/training/records/user/:userId/monthly?month=YYYY-MM
   * Get monthly training records for a user
   */
  public getMonthlyRecords = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const { month } = req.query;

      if (!month) {
        return this.sendError(res, 'VALIDATION_ERROR', 'Month parameter is required', null, 400);
      }

      // Validate month format
      const monthRegex = /^\d{4}-\d{2}$/;
      if (!monthRegex.test(month as string)) {
        return this.sendError(res, 'VALIDATION_ERROR', 'Invalid month format. Use YYYY-MM', null, 400);
      }

      this.logAction('GET_MONTHLY_TRAINING_RECORDS', userId, { month });

      const records = await this.trainingRecordService.getMonthlyRecords(userId, month as string);

      this.sendSuccess(res, records, 'Monthly training records retrieved successfully');
    }
  );

  /**
   * POST /api/v1/training/records/user/:userId/cleanup
   * Clean up old training data for a user
   */
  public cleanupUserData = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;

      this.logAction('CLEANUP_USER_TRAINING_DATA', userId);

      const result = await this.trainingRecordService.cleanupUserData(userId);

      this.sendSuccess(res, result, 'User training data cleanup completed successfully');
    }
  );

  /**
   * POST /api/v1/training/records/cleanup
   * Clean up old training data for all users (admin only)
   */
  public cleanupAllData = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      // Note: In a real application, this should have admin authentication
      this.logAction('CLEANUP_ALL_TRAINING_DATA', undefined);

      const result = await this.trainingRecordService.cleanupAllData();

      this.sendSuccess(res, result, 'Global training data cleanup completed successfully');
    }
  );

  /**
   * GET /api/v1/training/records/user/:userId/export?format=json&period=month&date=YYYY-MM
   * Export training data for a user
   */
  public exportUserData = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const { format = 'json', period = 'month', date } = req.query;

      if (!date) {
        return this.sendError(res, 'VALIDATION_ERROR', 'Date parameter is required', null, 400);
      }

      this.logAction('EXPORT_USER_TRAINING_DATA', userId, { format, period, date });

      let exportData: any;
      let filename: string;

      try {
        switch (period) {
        case 'day':
          if (!/^\d{4}-\d{2}-\d{2}$/.test(date as string)) {
            return this.sendError(res, 'VALIDATION_ERROR', 'Invalid date format for daily export. Use YYYY-MM-DD', null, 400);
          }
          exportData = await this.trainingRecordService.getDailyRecords(userId, date as string);
          filename = `training-data-daily-${date}.${format}`;
          break;

        case 'week':
          if (!/^\d{4}-W\d{2}$/.test(date as string)) {
            return this.sendError(res, 'VALIDATION_ERROR', 'Invalid week format for weekly export. Use YYYY-WW', null, 400);
          }
          exportData = await this.trainingRecordService.getWeeklyRecords(userId, date as string);
          filename = `training-data-weekly-${date}.${format}`;
          break;

        case 'month':
          if (!/^\d{4}-\d{2}$/.test(date as string)) {
            return this.sendError(res, 'VALIDATION_ERROR', 'Invalid month format for monthly export. Use YYYY-MM', null, 400);
          }
          exportData = await this.trainingRecordService.getMonthlyRecords(userId, date as string);
          filename = `training-data-monthly-${date}.${format}`;
          break;

        default:
          return this.sendError(res, 'VALIDATION_ERROR', 'Invalid period. Use day, week, or month', null, 400);
        }

        // Set appropriate headers for file download
        res.setHeader('Content-Type', format === 'json' ? 'application/json' : 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

        if (format === 'json') {
          res.json({
            success: true,
            data: exportData,
            exportInfo: {
              userId,
              period,
              date,
              exportedAt: new Date().toISOString(),
              format,
            },
          });
        } else if (format === 'csv') {
          // Convert to CSV format (simplified)
          const csvData = this.convertToCSV(exportData, period as string);
          res.send(csvData);
        } else {
          return this.sendError(res, 'VALIDATION_ERROR', 'Invalid format. Use json or csv', null, 400);
        }
      } catch (error) {
        logger.error('Failed to export user training data', { error, userId, format, period, date });
        return this.sendError(res, 'EXPORT_ERROR', 'Failed to export training data', error, 500);
      }
    }
  );

  /**
   * GET /api/v1/training/records/user/:userId/summary
   * Get training data summary for a user
   */
  public getUserSummary = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const { days = 30 } = req.query;

      this.logAction('GET_USER_TRAINING_SUMMARY', userId, { days });

      try {
        // Get current month data
        const currentDate = new Date();
        const currentMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

        const monthlyData = await this.trainingRecordService.getMonthlyRecords(userId, currentMonth);

        // Get recent daily data
        const recentDays = [];
        for (let i = 0; i < parseInt(days as string); i++) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          const dateStr = date.toISOString().split('T')[0];

          try {
            const dailyData = await this.trainingRecordService.getDailyRecords(userId, dateStr);
            if (dailyData.summary.totalSessions > 0) {
              recentDays.push({
                date: dateStr,
                sessions: dailyData.summary.totalSessions,
                duration: dailyData.summary.totalDuration,
                score: dailyData.summary.averageScore,
              });
            }
          } catch (error) {
            // Skip days with no data
          }
        }

        const summary = {
          userId,
          period: `Last ${days} days`,
          currentMonth: {
            month: currentMonth,
            totalSessions: monthlyData.monthlyTotals.totalSessions,
            totalDuration: monthlyData.monthlyTotals.totalDuration,
            averageScore: monthlyData.monthlyTotals.averageScore,
            completionRate: monthlyData.monthlyTotals.completionRate,
            goalAchievementRate: monthlyData.monthlyTotals.goalAchievementRate,
          },
          recentActivity: recentDays.slice(0, 7), // Last 7 days with activity
          trends: monthlyData.improvements,
          recommendations: monthlyData.recommendations,
          dataRetention: {
            retentionPeriod: 30,
            oldestData: recentDays.length > 0 ? recentDays[recentDays.length - 1].date : null,
          },
        };

        this.sendSuccess(res, summary, 'User training summary retrieved successfully');
      } catch (error) {
        logger.error('Failed to get user training summary', { error, userId });
        return this.sendError(res, 'SUMMARY_ERROR', 'Failed to retrieve training summary', error, 500);
      }
    }
  );

  /**
   * Convert data to CSV format
   */
  private convertToCSV(data: any, period: string): string {
    let csvContent = '';

    switch (period) {
    case 'day':
      csvContent = 'Session ID,Start Time,Score,Accuracy,Duration (min)\n';
      data.chartData.performanceBySession.forEach((session: any) => {
        csvContent += `${session.sessionId},${session.startTime},${session.score},${session.accuracy},${session.duration}\n`;
      });
      break;

    case 'week':
      csvContent = 'Date,Score,Accuracy,Duration (min)\n';
      data.chartData.dailyProgress.forEach((day: any) => {
        csvContent += `${day.date},${day.score},${day.accuracy},${day.duration}\n`;
      });
      break;

    case 'month':
      csvContent = 'Date,Sessions,Duration (min),Score\n';
      data.dailyActivity.forEach((day: any) => {
        csvContent += `${day.date},${day.sessions},${day.duration},${day.score}\n`;
      });
      break;
    }

    return csvContent;
  }
}

// Export singleton instance
export const trainingRecordController = new TrainingRecordController();
