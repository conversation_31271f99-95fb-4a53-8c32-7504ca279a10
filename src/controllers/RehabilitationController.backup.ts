import { Request, Response, NextFunction } from 'express';
import { BaseController } from './BaseController';
import { logger } from '@/utils/logger';
import { TrainingRecordService } from '@/services/TrainingRecordService';
import { TrainingSessionRepository } from '@/services/database/TrainingSessionRepository';
import { TrainingPlanRepository } from '@/services/database/TrainingPlanRepository';
import { DatabaseService } from '@/services/database/DatabaseService';
import { Prisma } from '@prisma/client';

/**
 * Rehabilitation Training Controller
 * Handles rehabilitation training records, plans, action points, and targets
 * All data comes from real database operations - NO MOCK DATA
 */
export class RehabilitationController extends BaseController {
  private trainingRecordService: TrainingRecordService;
  private trainingSessionRepository: TrainingSessionRepository;
  private trainingPlanRepository: TrainingPlanRepository;
  private databaseService: DatabaseService;

  constructor() {
    super();
    this.trainingRecordService = new TrainingRecordService();
    this.trainingSessionRepository = new TrainingSessionRepository();
    this.trainingPlanRepository = new TrainingPlanRepository();
    this.databaseService = new DatabaseService();
  }

  /**
   * Helper method to map session type to training type
   */
  private mapSessionTypeToTrainingType(sessionType: string): string {
    const typeMapping: { [key: string]: string } = {
      'strength_training': 'strength',
      'flexibility_training': 'flexibility',
      'balance_training': 'balance',
      'coordination_training': 'coordination',
      'rehabilitation': 'rehabilitation',
    };
    return typeMapping[sessionType] || sessionType;
  }

  /**
   * Helper method to extract target muscles from plan data
   */
  private extractTargetMuscles(planData: any): string[] {
    if (!planData) return [];

    // Extract from plan's target areas or session data
    if (planData.targetAreas && Array.isArray(planData.targetAreas)) {
      return planData.targetAreas;
    }

    // Default based on recovery stage
    const defaultMuscles: { [key: string]: string[] } = {
      'early': ['手指', '手腕'],
      'intermediate': ['手指', '手腕', '前臂'],
      'advanced': ['手指', '手腕', '前臂', '上臂'],
    };

    return defaultMuscles[planData.recoveryStage] || ['手部'];
  }

  /**
   * Helper method to extract exercises from data points
   */
  private extractExercisesFromDataPoints(dataPoints: any[]): any[] {
    if (!dataPoints || dataPoints.length === 0) return [];

    // Group data points by action type to create exercises
    const exerciseGroups: { [key: string]: any[] } = {};

    dataPoints.forEach(point => {
      const actionType = point.actionType || 'general_exercise';
      if (!exerciseGroups[actionType]) {
        exerciseGroups[actionType] = [];
      }
      exerciseGroups[actionType].push(point);
    });

    return Object.entries(exerciseGroups).map(([actionType, points]) => ({
      name: this.getExerciseNameFromActionType(actionType),
      sets: Math.ceil(points.length / 10), // Estimate sets based on data points
      reps: Math.min(points.length, 15),   // Estimate reps
      completed: points.length > 0,
      score: points.length > 0 ? Math.round(points.reduce((sum, p) => sum + (Number(p.qualityScore) || 0), 0) / points.length) : 0,
    }));
  }

  /**
   * Helper method to get exercise name from action type
   */
  private getExerciseNameFromActionType(actionType: string): string {
    const exerciseNames: { [key: string]: string } = {
      'grip': '握力训练',
      'pinch': '捏取训练',
      'extension': '伸展训练',
      'flexion': '弯曲训练',
      'rotation': '旋转训练',
      'general_exercise': '综合训练',
    };
    return exerciseNames[actionType] || '康复训练';
  }

  /**
   * Helper method to determine difficulty from plan
   */
  private mapDifficultyFromPlan(plan: any): string {
    if (!plan) return 'medium';

    if (plan.recoveryStage === 'early') return 'easy';
    if (plan.recoveryStage === 'advanced') return 'hard';
    return 'medium';
  }

  /**
   * Clear all user training data (reset to new user state)
   */
  public clearUserTrainingData = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);

      this.logAction('CLEAR_TRAINING_DATA', userId);

      try {
        // Start a database transaction to ensure data consistency
        const result = await this.databaseService.transaction(async (prisma) => {
          // Delete training data points first (due to foreign key constraints)
          const deletedDataPoints = await prisma.trainingDataPoint.deleteMany({
            where: {
              session: {
                userId: userId,
              },
            },
          });

          // Delete game records
          const deletedGameRecords = await prisma.gameRecord.deleteMany({
            where: {
              userId: userId,
            },
          });

          // Delete training sessions
          const deletedSessions = await prisma.trainingSession.deleteMany({
            where: {
              userId: userId,
            },
          });

          // Delete rehabilitation plans
          const deletedPlans = await prisma.rehabilitationPlan.deleteMany({
            where: {
              userId: userId,
            },
          });

          // Delete health assessments
          const deletedAssessments = await prisma.healthAssessment.deleteMany({
            where: {
              userId: userId,
            },
          });

          return {
            deletedDataPoints: deletedDataPoints.count,
            deletedGameRecords: deletedGameRecords.count,
            deletedSessions: deletedSessions.count,
            deletedPlans: deletedPlans.count,
            deletedAssessments: deletedAssessments.count,
          };
        });

        logger.info('用户训练数据清空成功', {
          userId,
          deletedCounts: result,
        });

        this.sendSuccessResponse(res, {
          message: '用户训练数据已清空，账户已重置为新用户状态',
          clearedData: {
            trainingDataPoints: result.deletedDataPoints,
            trainingSessions: result.deletedSessions,
            rehabilitationPlans: result.deletedPlans,
            gameRecords: result.deletedGameRecords,
            healthAssessments: result.deletedAssessments,
          },
          resetTimestamp: new Date().toISOString(),
        });

      } catch (error) {
        logger.error('清空用户训练数据失败', {
          userId,
          error: error instanceof Error ? error.message : error,
        });
        throw error;
      }
    }
  );

  /**
   * Get user's rehabilitation training records
   */
  public getTrainingRecords = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);
      const { limit, offset } = this.getPaginationParams(req);
      const { startDate, endDate, type } = req.query;

      this.logAction('GET_TRAINING_RECORDS', userId, {
        type,
        limit,
        offset,
        dateRange: { startDate, endDate },
      });

      try {
        // Get training sessions from database
        const sessions = await this.trainingSessionRepository.findMany({
          where: {
            userId: userId,
            ...(startDate && { startTime: { gte: new Date(startDate as string) } }),
            ...(endDate && { startTime: { lte: new Date(endDate as string) } }),
            ...(type && type !== 'all' && { sessionType: type as string }),
          },
          include: {
            rehabilitationPlan: true,
            dataPoints: true,
          },
          orderBy: {
            startTime: 'desc',
          },
          skip: offset,
          take: limit,
        });

        // Transform database records to API format
        const allRecords = sessions.map(session => ({
          id: session.id,
          userId: session.userId,
          type: this.mapSessionTypeToTrainingType(session.sessionType),
          name: session.sessionName || session.rehabilitationPlan?.planName || '康复训练',
          description: session.rehabilitationPlan?.description || '康复训练记录',
          duration: session.actualDurationSeconds || session.plannedDurationSeconds || 0,
          completedActions: session.completedActions,
          totalActions: session.targetActions || session.completedActions || 1,
          accuracy: session.achievedAccuracy ? Number(session.achievedAccuracy) : 0,
          difficulty: this.mapDifficultyFromPlan(session.rehabilitationPlan),
          score: session.totalScore,
          targetMuscles: this.extractTargetMuscles(session.rehabilitationPlan),
          completedAt: session.endTime?.toISOString() || session.startTime.toISOString(),
          exercises: this.extractExercisesFromDataPoints(session.dataPoints || []),
          feedback: {
            therapistNotes: session.notes || '',
            painLevel: 0, // Could be extracted from session data if available
            fatigue: 0,   // Could be extracted from session data if available
            satisfaction: 0, // Could be extracted from session data if available
          },
        }));

        // Get total count for pagination
        const totalCount = await this.trainingSessionRepository.count({
          where: {
            userId: userId,
            ...(startDate && { startTime: { gte: new Date(startDate as string) } }),
            ...(endDate && { startTime: { lte: new Date(endDate as string) } }),
            ...(type && type !== 'all' && { sessionType: type as string }),
          },
        });

        // Calculate statistics from real data
        const statistics = allRecords.length > 0 ? {
          total: totalCount,
          byType: {
            strength: allRecords.filter(r => r.type === 'strength').length,
            flexibility: allRecords.filter(r => r.type === 'flexibility').length,
            balance: allRecords.filter(r => r.type === 'balance').length,
            coordination: allRecords.filter(r => r.type === 'coordination').length,
          },
          averageScore: Math.round(allRecords.reduce((sum, r) => sum + r.score, 0) / allRecords.length),
          averageAccuracy: Math.round(allRecords.reduce((sum, r) => sum + r.accuracy, 0) / allRecords.length * 100) / 100,
          totalDuration: allRecords.reduce((sum, r) => sum + r.duration, 0),
          completionRate: Math.round(allRecords.reduce((sum, r) => sum + (r.completedActions / r.totalActions), 0) / allRecords.length * 100) / 100,
        } : {
          total: 0,
          byType: { strength: 0, flexibility: 0, balance: 0, coordination: 0 },
          averageScore: 0,
          averageAccuracy: 0,
          totalDuration: 0,
          completionRate: 0,
        };

        logger.info('康复训练记录获取成功', {
          userId,
          recordsCount: allRecords.length,
          totalCount,
          filters: { type, startDate, endDate },
        });

        // Return records with pagination info
        this.sendSuccessResponse(res, {
          records: allRecords,
          statistics,
          pagination: {
            limit,
            offset,
            total: totalCount,
            hasMore: offset + limit < totalCount,
          },
        });

      } catch (error) {
        logger.error('获取康复训练记录失败', {
          userId,
          error: error instanceof Error ? error.message : error,
        });
        throw error;
      }
    }
  );

  /**
   * Get user's rehabilitation training plans
   */


  /**
   * Get user's rehabilitation training plans
   */
  public getTrainingPlans = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);
      const { status, difficulty } = req.query;

      this.logAction('GET_TRAINING_PLANS', userId, { status, difficulty });

      try {
        // Get rehabilitation plans from database
        const plans = await this.trainingPlanRepository.findMany({
          where: {
            userId: userId,
            ...(status && status !== 'all' && { status: status as string }),
            ...(difficulty && difficulty !== 'all' && { recoveryStage: difficulty as string }),
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

        // Transform database records to API format
        const transformedPlans = plans.map(plan => ({
          id: plan.id,
          userId: plan.userId,
          name: plan.planName,
          description: plan.description || '',
          status: plan.status,
          difficulty: plan.recoveryStage || 'medium',
          duration: plan.durationWeeks || 0,
          currentWeek: this.calculateCurrentWeek(plan.startDate, plan.durationWeeks),
          progress: this.calculateProgress(plan.startDate, plan.durationWeeks),
          startDate: plan.startDate?.toISOString(),
          endDate: plan.endDate?.toISOString(),
          therapistId: plan.therapistId || '',
          therapistName: plan.therapistName || '',
          targetAreas: this.extractTargetAreas(plan),
          goals: this.extractGoals(plan),
          weeklySchedule: this.extractWeeklySchedule(plan),
          nextSession: await this.getNextSession(userId, plan.id),
        }));

        // Calculate summary statistics
        const summary = {
          total: transformedPlans.length,
          active: transformedPlans.filter(p => p.status === 'active').length,
          completed: transformedPlans.filter(p => p.status === 'completed').length,
          paused: transformedPlans.filter(p => p.status === 'paused').length,
        };

        logger.info('康复训练计划获取成功', {
          userId,
          plansCount: transformedPlans.length,
          filters: { status, difficulty },
        });

        this.sendSuccessResponse(res, {
          plans: transformedPlans,
          summary,
        });

      } catch (error) {
        logger.error('获取康复训练计划失败', {
          userId,
          error: error instanceof Error ? error.message : error,
        });
        throw error;
      }
    }
  );

  /**
   * Helper methods for training plans
   */
  private calculateCurrentWeek(startDate: Date | null, durationWeeks: number | null): number {
    if (!startDate || !durationWeeks) return 1;

    const now = new Date();
    const diffTime = now.getTime() - startDate.getTime();
    const diffWeeks = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));

    return Math.min(Math.max(diffWeeks, 1), durationWeeks);
  }

  private calculateProgress(startDate: Date | null, durationWeeks: number | null): number {
    if (!startDate || !durationWeeks) return 0;

    const currentWeek = this.calculateCurrentWeek(startDate, durationWeeks);
    return Math.min(currentWeek / durationWeeks, 1);
  }

  private extractTargetAreas(plan: any): string[] {
    if (plan.targetAreas && Array.isArray(plan.targetAreas)) {
      return plan.targetAreas;
    }

    const defaultAreas: { [key: string]: string[] } = {
      'early': ['手指', '手腕'],
      'intermediate': ['手指', '手腕', '前臂'],
      'advanced': ['手指', '手腕', '前臂', '上臂'],
    };

    return defaultAreas[plan.recoveryStage] || ['手部'];
  }

  private extractGoals(plan: any): string[] {
    if (plan.goals && Array.isArray(plan.goals)) {
      return plan.goals;
    }

    const defaultGoals: { [key: string]: string[] } = {
      'early': ['减少疼痛和肿胀', '保护受伤组织', '维持基本活动'],
      'intermediate': ['恢复关节活动范围', '增强肌肉力量', '改善协调性'],
      'advanced': ['恢复功能性活动', '提高运动表现', '预防再次受伤'],
    };

    return defaultGoals[plan.recoveryStage] || ['康复训练目标'];
  }

  private extractWeeklySchedule(plan: any): any[] {
    if (plan.weeklySchedule && Array.isArray(plan.weeklySchedule)) {
      return plan.weeklySchedule;
    }
    return [];
  }

  private async getNextSession(userId: string, planId: string): Promise<any | null> {
    try {
      const nextSession = await this.trainingSessionRepository.findFirst({
        where: {
          userId: userId,
          rehabilitationPlanId: planId,
          startTime: { gte: new Date() },
        },
        orderBy: { startTime: 'asc' },
      });

      if (nextSession) {
        return {
          date: nextSession.startTime.toISOString(),
          type: this.mapSessionTypeToTrainingType(nextSession.sessionType),
          duration: nextSession.plannedDurationSeconds ? Math.round(nextSession.plannedDurationSeconds / 60) : 30,
          exercises: [],
        };
      }
      return null;
    } catch (error) {
      logger.warn('获取下次训练会话失败', { userId, planId, error });
      return null;
    }
  }

  /**
   * Get rehabilitation action points
   */
  public getActionPoints = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);

      this.logAction('GET_ACTION_POINTS', userId);

      try {
        // In a real implementation, action points could come from:
        // 1. A configuration table in the database
        // 2. A static configuration file
        // 3. Generated based on user's rehabilitation plan

        // For now, return empty array as no action points are configured
        const actionPoints: any[] = [];

        logger.info('康复训练动作要点获取成功', {
          userId,
          totalActions: actionPoints.length,
        });

        this.sendSuccessResponse(res, {
          actionPoints,
          categories: {
            strength: actionPoints.filter(a => a.category === 'strength').length,
            flexibility: actionPoints.filter(a => a.category === 'flexibility').length,
            balance: actionPoints.filter(a => a.category === 'balance').length,
            coordination: actionPoints.filter(a => a.category === 'coordination').length,
          },
        });

      } catch (error) {
        logger.error('获取康复训练动作要点失败', {
          userId,
          error: error instanceof Error ? error.message : error,
        });
        throw error;
      }
    }
  );

  /**
   * Get user's rehabilitation training targets
   */
  public getTrainingTargets = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);
      const { period, status } = req.query;

      this.logAction('GET_TRAINING_TARGETS', userId, { period, status });

      try {
        // In a real implementation, training targets would come from:
        // 1. User's rehabilitation plan goals
        // 2. Therapist-set targets
        // 3. System-generated targets based on progress

        // For now, return empty array as no targets are configured
        const targets: any[] = [];

        // Calculate summary statistics
        const summary = {
          total: targets.length,
          byPeriod: {
            daily: targets.filter(t => t.type === 'daily').length,
            weekly: targets.filter(t => t.type === 'weekly').length,
            monthly: targets.filter(t => t.type === 'monthly').length,
          },
          byStatus: {
            active: targets.filter(t => t.status === 'active').length,
            completed: targets.filter(t => t.status === 'completed').length,
            overdue: targets.filter(t => t.status === 'overdue').length,
          },
          averageProgress: targets.length > 0 ?
            Math.round(targets.reduce((sum, t) => sum + t.progress, 0) / targets.length * 100) / 100 : 0,
          totalRewardPoints: targets.reduce((sum, t) => {
            const points = parseInt(t.reward?.match(/\d+/)?.[0] || '0');
            return sum + (t.progress >= 1 ? points : 0);
          }, 0),
        };

        logger.info('康复训练目标获取成功', {
          userId,
          targetsCount: targets.length,
          filters: { period, status },
        });

        this.sendSuccessResponse(res, {
          targets,
          summary,
        });

      } catch (error) {
        logger.error('获取康复训练目标失败', {
          userId,
          error: error instanceof Error ? error.message : error,
        });
        throw error;
      }
    }
  );

  /**
   * Get daily training records grouped by date
   */
  public getDailyTrainingRecords = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);
      const { startDate, endDate } = req.query;

      this.logAction('GET_DAILY_TRAINING_RECORDS', userId, {
        dateRange: { startDate, endDate },
      });

      try {
        // Get training sessions from database
        const sessions = await this.trainingSessionRepository.findMany({
          where: {
            userId: userId,
            ...(startDate && { startTime: { gte: new Date(startDate as string) } }),
            ...(endDate && { startTime: { lte: new Date(endDate as string) } }),
          },
          include: {
            rehabilitationPlan: true,
            dataPoints: true,
          },
          orderBy: {
            startTime: 'desc',
          },
        });

        // Group sessions by date
        const groupedByDate: { [key: string]: any[] } = {};

        sessions.forEach(session => {
          const dateKey = session.startTime.toISOString().split('T')[0]; // YYYY-MM-DD format

          if (!groupedByDate[dateKey]) {
            groupedByDate[dateKey] = [];
          }

          groupedByDate[dateKey].push({
            id: session.id,
            trainingPlanName: session.sessionName || session.rehabilitationPlan?.planName || '康复训练',
            sessionDuration: session.actualDurationSeconds || session.plannedDurationSeconds || 0,
            completedActions: session.completedActions,
            totalActions: session.targetActions || session.completedActions || 1,
            accuracy: session.achievedAccuracy ? Number(session.achievedAccuracy) : 0,
            score: session.totalScore,
            type: this.mapSessionTypeToTrainingType(session.sessionType),
            difficulty: this.mapDifficultyFromPlan(session.rehabilitationPlan),
            targetMuscles: this.extractTargetMuscles(session.rehabilitationPlan),
            exercises: this.extractExercisesFromDataPoints(session.dataPoints || []),
            feedback: {
              therapistNotes: session.notes || '',
              painLevel: 0,
              fatigue: 0,
              satisfaction: 0,
            },
            completedAt: session.endTime?.toISOString() || session.startTime.toISOString(),
          });
        });

        // Transform grouped data to API format
        const dailyRecords = Object.entries(groupedByDate).map(([date, records]) => {
          const summary = {
            totalSessions: records.length,
            totalDuration: records.reduce((sum, r) => sum + r.sessionDuration, 0),
            averageAccuracy: records.length > 0 ?
              Math.round(records.reduce((sum, r) => sum + r.accuracy, 0) / records.length * 100) / 100 : 0,
            totalScore: records.reduce((sum, r) => sum + r.score, 0),
          };

          return {
            date,
            records,
            summary,
          };
        }).sort((a, b) => b.date.localeCompare(a.date)); // Sort by date descending

        logger.info('每日康复训练记录获取成功', {
          userId,
          daysCount: dailyRecords.length,
          totalSessions: sessions.length,
          dateRange: { startDate, endDate },
        });

        this.sendSuccessResponse(res, dailyRecords);

      } catch (error) {
        logger.error('获取每日康复训练记录失败', {
          userId,
          error: error instanceof Error ? error.message : error,
        });
        throw error;
      }
    }
  );
}
