/**
 * Health Assessment Controller
 * Handles HTTP requests for health assessment operations
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from './BaseController';
import { HealthAssessmentService, AssessmentCompletionData, AssessmentReportData } from '@/services/HealthAssessmentService';
import { CreateHealthAssessmentData } from '@/services/database/HealthAssessmentRepository';
import { container, SERVICE_NAMES } from '@/core/Container';
import { logger } from '@/utils/logger';

export class HealthAssessmentController extends BaseController {
  private healthAssessmentService: HealthAssessmentService;

  constructor() {
    super();
    // Use lazy initialization to avoid circular dependency issues
    this.healthAssessmentService = new HealthAssessmentService();
  }

  /**
   * POST /api/v1/health/assessments
   * Create a new health assessment
   */
  public createAssessment = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId, templateId, assessmentType, assessmentName, description, conductedBy } = req.body;

      this.logAction('CREATE_ASSESSMENT', userId);

      const assessmentData: CreateHealthAssessmentData = {
        userId,
        templateId,
        assessmentType,
        assessmentName,
        description,
        conductedBy,
      };

      const assessment = await this.healthAssessmentService.createAssessment(assessmentData);

      this.sendSuccess(res, assessment, 'Health assessment created successfully', 201);
    }
  );

  /**
   * GET /api/v1/health/assessments/:id
   * Get assessment by ID
   */
  public getAssessmentById = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;

      this.logAction('GET_ASSESSMENT', undefined, { assessmentId: id });

      const assessment = await this.healthAssessmentService.getAssessmentById(id);

      if (!assessment) {
        return this.sendError(res, 'ASSESSMENT_NOT_FOUND', 'Assessment not found', null, 404);
      }

      this.sendSuccess(res, assessment, 'Assessment retrieved successfully');
    }
  );

  /**
   * GET /api/v1/health/assessments/user/:userId
   * Get user assessments with pagination and filters
   */
  public getUserAssessments = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const {
        assessmentType,
        status,
        templateId,
        dateFrom,
        dateTo,
        page = 1,
        limit = 10,
      } = req.query;

      this.logAction('GET_USER_ASSESSMENTS', userId);

      const filters = {
        ...(assessmentType && { assessmentType: assessmentType as string }),
        ...(status && { status: status as string }),
        ...(templateId && { templateId: templateId as string }),
        ...(dateFrom && { dateFrom: new Date(dateFrom as string) }),
        ...(dateTo && { dateTo: new Date(dateTo as string) }),
      };

      const result = await this.healthAssessmentService.getUserAssessments(
        userId,
        filters,
        parseInt(page as string),
        parseInt(limit as string)
      );

      this.sendSuccess(res, result, 'User assessments retrieved successfully');
    }
  );

  /**
   * PUT /api/v1/health/assessments/:id
   * Update assessment
   */
  public updateAssessment = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;
      const updateData = req.body;

      this.logAction('UPDATE_ASSESSMENT', undefined, { assessmentId: id });

      const assessment = await this.healthAssessmentService.updateAssessment(id, updateData);

      this.sendSuccess(res, assessment, 'Assessment updated successfully');
    }
  );

  /**
   * POST /api/v1/health/assessments/:id/start
   * Start an assessment
   */
  public startAssessment = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;

      this.logAction('START_ASSESSMENT', undefined, { assessmentId: id });

      const assessment = await this.healthAssessmentService.startAssessment(id);

      this.sendSuccess(res, assessment, 'Assessment started successfully');
    }
  );

  /**
   * POST /api/v1/health/assessments/:id/complete
   * Complete an assessment with results
   */
  public completeAssessment = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;
      const completionData: AssessmentCompletionData = req.body;

      this.logAction('COMPLETE_ASSESSMENT', undefined, {
        assessmentId: id,
        resultsCount: completionData.results.length,
      });

      const assessment = await this.healthAssessmentService.completeAssessment(id, completionData);

      this.sendSuccess(res, assessment, 'Assessment completed successfully');
    }
  );

  /**
   * GET /api/v1/health/assessments/:id/results
   * Get assessment results
   */
  public getAssessmentResults = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;

      this.logAction('GET_ASSESSMENT_RESULTS', undefined, { assessmentId: id });

      const results = await this.healthAssessmentService.getAssessmentResults(id);

      this.sendSuccess(res, results, 'Assessment results retrieved successfully');
    }
  );

  /**
   * GET /api/v1/health/assessments/:id/report
   * Generate and get assessment report
   */
  public generateAssessmentReport = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;
      const { reportType = 'comprehensive', title, summary, detailedAnalysis, isPublic = false } = req.body;

      this.logAction('GENERATE_ASSESSMENT_REPORT', undefined, {
        assessmentId: id,
        reportType,
      });

      // Get assessment to validate it exists and is completed
      const assessment = await this.healthAssessmentService.getAssessmentById(id);
      if (!assessment) {
        return this.sendError(res, 'ASSESSMENT_NOT_FOUND', 'Assessment not found', null, 404);
      }

      if (assessment.status !== 'completed') {
        return this.sendError(res, 'ASSESSMENT_NOT_COMPLETED', 'Cannot generate report for incomplete assessment', null, 400);
      }

      // Generate default report content if not provided
      const reportData: AssessmentReportData = {
        reportType,
        title: title || `${assessment.assessmentName} - Assessment Report`,
        summary: summary || this.generateDefaultSummary(assessment),
        detailedAnalysis: detailedAnalysis || this.generateDefaultAnalysis(assessment),
        recommendations: assessment.recommendations || {},
        isPublic: Boolean(isPublic),
      };

      const report = await this.healthAssessmentService.generateAssessmentReport(
        id,
        reportData,
        req.user?.id // Assuming user ID is available in request
      );

      this.sendSuccess(res, report, 'Assessment report generated successfully');
    }
  );

  /**
   * GET /api/v1/health/assessments/:id/reports
   * Get all reports for an assessment
   */
  public getAssessmentReports = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;

      this.logAction('GET_ASSESSMENT_REPORTS', undefined, { assessmentId: id });

      const reports = await this.healthAssessmentService.getAssessmentReports(id);

      this.sendSuccess(res, reports, 'Assessment reports retrieved successfully');
    }
  );

  /**
   * GET /api/v1/health/assessments/templates
   * Get assessment templates
   */
  public getAssessmentTemplates = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { templateType } = req.query;

      this.logAction('GET_ASSESSMENT_TEMPLATES', undefined, { templateType });

      const templates = await this.healthAssessmentService.getAssessmentTemplates(
        templateType as string
      );

      this.sendSuccess(res, templates, 'Assessment templates retrieved successfully');
    }
  );

  /**
   * GET /api/v1/health/assessments/user/:userId/stats
   * Get user assessment statistics
   */
  public getUserAssessmentStats = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const { assessmentType, dateFrom, dateTo } = req.query;

      this.logAction('GET_USER_ASSESSMENT_STATS', userId);

      const stats = await this.healthAssessmentService.getUserAssessmentStats(
        userId,
        assessmentType as string,
        dateFrom ? new Date(dateFrom as string) : undefined,
        dateTo ? new Date(dateTo as string) : undefined
      );

      this.sendSuccess(res, stats, 'User assessment statistics retrieved successfully');
    }
  );

  /**
   * DELETE /api/v1/health/assessments/:id
   * Delete (soft delete) an assessment
   */
  public deleteAssessment = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;

      this.logAction('DELETE_ASSESSMENT', undefined, { assessmentId: id });

      const assessment = await this.healthAssessmentService.deleteAssessment(id);

      this.sendSuccess(res, assessment, 'Assessment deleted successfully');
    }
  );

  /**
   * Generate default summary for assessment report
   */
  private generateDefaultSummary(assessment: any): string {
    const score = assessment.overallScore?.toNumber() || 0;
    const completionRate = assessment.completionRate?.toNumber() || 0;
    const duration = Math.floor((assessment.durationSeconds || 0) / 60);

    return `Assessment completed with an overall score of ${score.toFixed(1)}% and completion rate of ${(completionRate * 100).toFixed(1)}%. The assessment took ${duration} minutes to complete. ${score >= 80 ? 'Excellent performance with strong rehabilitation progress.' : score >= 60 ? 'Good performance with areas for improvement identified.' : 'Performance indicates need for focused rehabilitation efforts.'}`;
  }

  /**
   * Generate default analysis for assessment report
   */
  private generateDefaultAnalysis(assessment: any): string {
    const score = assessment.overallScore?.toNumber() || 0;

    let analysis = `This ${assessment.assessmentType} assessment evaluated the patient's functional capabilities and rehabilitation progress. `;

    if (score >= 80) {
      analysis += 'The results demonstrate significant improvement and strong functional recovery. The patient shows excellent motor control, coordination, and strength in the assessed areas.';
    } else if (score >= 60) {
      analysis += 'The results show moderate progress with some areas requiring continued focus. The patient demonstrates good basic function with room for improvement in precision and consistency.';
    } else {
      analysis += 'The results indicate early-stage recovery with significant opportunity for improvement. The patient would benefit from intensive rehabilitation focusing on fundamental movement patterns and strength building.';
    }

    return analysis;
  }
}

// Export singleton instance
export const healthAssessmentController = new HealthAssessmentController();
