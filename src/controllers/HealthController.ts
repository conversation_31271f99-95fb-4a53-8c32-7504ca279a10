/**
 * Health Controller
 * Handles system health checks and monitoring endpoints
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from './BaseController';
import { DatabaseService } from '@/services/database/DatabaseService';
import { CacheService } from '@/services/CacheService';
import { container, SERVICE_NAMES } from '@/core/Container';
import { logger } from '@/utils/logger';

export class HealthController extends BaseController {
  private databaseService: DatabaseService;
  private cacheService: CacheService;

  constructor() {
    super();
    this.databaseService = container.get<DatabaseService>(SERVICE_NAMES.DATABASE_SERVICE)
      || new DatabaseService();
    this.cacheService = container.get<CacheService>(SERVICE_NAMES.CACHE_SERVICE)
      || new CacheService();
  }

  /**
   * GET /health
   * Basic health check endpoint
   */
  public basicHealthCheck = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      this.logAction('HEALTH_CHECK', undefined);

      const healthData = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024),
        },
        cpu: {
          loadAverage: typeof process.loadavg === 'function' ? process.loadavg() : [0, 0, 0],
          platform: process.platform,
          arch: process.arch,
        },
      };

      this.sendSuccess(res, healthData, 'System is healthy');
    }
  );

  /**
   * GET /health/detailed
   * Detailed health check including database and cache
   */
  public detailedHealthCheck = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      this.logAction('DETAILED_HEALTH_CHECK', undefined);

      const startTime = Date.now();

      // Check database health
      const dbHealth = await this.checkDatabaseHealth();

      // Check cache health
      const cacheHealth = await this.checkCacheHealth();

      // Check system resources
      const systemHealth = await this.checkSystemHealth();

      const totalTime = Date.now() - startTime;
      const overallStatus = dbHealth.status === 'healthy' &&
                           cacheHealth.status === 'healthy' &&
                           systemHealth.status === 'healthy'
        ? 'healthy' : 'unhealthy';

      const healthData = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        responseTime: totalTime,
        services: {
          database: dbHealth,
          cache: cacheHealth,
          system: systemHealth,
        },
        summary: {
          totalChecks: 3,
          healthyChecks: [dbHealth, cacheHealth, systemHealth].filter(h => h.status === 'healthy').length,
          unhealthyChecks: [dbHealth, cacheHealth, systemHealth].filter(h => h.status === 'unhealthy').length,
        },
      };

      if (overallStatus === 'healthy') {
        this.sendSuccess(res, healthData, 'All systems are healthy');
      } else {
        this.sendError(res, 'SYSTEM_UNHEALTHY', 'One or more systems are unhealthy', healthData, 503);
      }
    }
  );

  /**
   * GET /health/database
   * Database-specific health check
   */
  public databaseHealthCheck = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      this.logAction('DATABASE_HEALTH_CHECK', undefined);

      const dbHealth = await this.checkDatabaseHealth();

      if (dbHealth.status === 'healthy') {
        this.sendSuccess(res, dbHealth, 'Database is healthy');
      } else {
        this.sendError(res, 'DATABASE_UNHEALTHY', 'Database is unhealthy', dbHealth, 503);
      }
    }
  );

  /**
   * GET /health/cache
   * Cache-specific health check
   */
  public cacheHealthCheck = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      this.logAction('CACHE_HEALTH_CHECK', undefined);

      const cacheHealth = await this.checkCacheHealth();

      if (cacheHealth.status === 'healthy') {
        this.sendSuccess(res, cacheHealth, 'Cache is healthy');
      } else {
        this.sendError(res, 'CACHE_UNHEALTHY', 'Cache is unhealthy', cacheHealth, 503);
      }
    }
  );

  /**
   * GET /health/stats
   * System statistics and metrics
   */
  public getSystemStats = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      this.logAction('GET_SYSTEM_STATS', undefined);

      try {
        // Get database statistics
        const dbStats = await this.databaseService.getStats();

        // Get cache statistics
        const cacheStats = this.cacheService.getStats();

        // Get system statistics
        const systemStats = {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: {
            loadAverage: typeof process.loadavg === 'function' ? process.loadavg() : [0, 0, 0],
            usage: process.cpuUsage(),
          },
          platform: {
            type: process.platform,
            arch: process.arch,
            version: process.version,
            nodeVersion: process.versions.node,
          },
        };

        const statsData = {
          timestamp: new Date().toISOString(),
          database: dbStats,
          cache: cacheStats,
          system: systemStats,
        };

        this.sendSuccess(res, statsData, 'System statistics retrieved successfully');
      } catch (error) {
        logger.error('Failed to get system stats', { error });
        this.sendError(res, 'STATS_ERROR', 'Failed to retrieve system statistics', null, 500);
      }
    }
  );

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<any> {
    try {
      const dbHealth = await this.databaseService.healthCheck();
      return {
        status: dbHealth.status,
        latency: dbHealth.latency,
        connection: 'active',
        provider: 'PostgreSQL',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Database health check failed', { error });
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        connection: 'failed',
        provider: 'PostgreSQL',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Check cache health
   */
  private async checkCacheHealth(): Promise<any> {
    try {
      const testKey = 'health_check_test';
      const testValue = Date.now().toString();

      // Test cache write
      await this.cacheService.set(testKey, testValue, 10);

      // Test cache read
      const retrievedValue = await this.cacheService.get(testKey);

      // Test cache delete
      await this.cacheService.delete(testKey);

      const isHealthy = retrievedValue === testValue;
      const stats = this.cacheService.getStats();

      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        operations: {
          write: 'success',
          read: retrievedValue === testValue ? 'success' : 'failed',
          delete: 'success',
        },
        statistics: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Cache health check failed', { error });
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Check system health
   */
  private async checkSystemHealth(): Promise<any> {
    try {
      const memoryUsage = process.memoryUsage();
      const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
      const loadAverage = typeof process.loadavg === 'function' ? process.loadavg()[0] : 0; // 1-minute load average

      // Define thresholds
      const memoryThreshold = 90; // 90%
      const loadThreshold = 2.0; // Load average threshold

      const isMemoryHealthy = memoryUsagePercent < memoryThreshold;
      const isLoadHealthy = loadAverage < loadThreshold;
      const isHealthy = isMemoryHealthy && isLoadHealthy;

      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        memory: {
          usage: Math.round(memoryUsagePercent),
          threshold: memoryThreshold,
          healthy: isMemoryHealthy,
        },
        load: {
          average: Math.round(loadAverage * 100) / 100,
          threshold: loadThreshold,
          healthy: isLoadHealthy,
        },
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('System health check failed', { error });
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
    }
  }
}

// Export singleton instance
export const healthController = new HealthController();
