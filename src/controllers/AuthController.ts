import { Request, Response, NextFunction } from 'express';
import { BaseController } from './BaseController';
import { userService } from '@/services/userService';
import { smsService } from '@/services/smsService';
import { logger } from '@/utils/logger';

/**
 * Authentication Controller
 * Handles user authentication, registration, and token management
 */
export class AuthController extends BaseController {
  /**
   * User login with username/email and password
   */
  public loginWithPassword = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { identifier, password } = req.body;

      this.logAction('LOGIN_PASSWORD_ATTEMPT', undefined, { identifier });

      const result = await userService.loginWithPassword({
        identifier,
        password,
      });

      this.logAction('LOGIN_PASSWORD_SUCCESS', result.user.id, {
        username: result.user.username,
        lastLoginAt: result.user.lastLoginAt,
      });

      this.sendSuccess(res, {
        user: result.user,
        accessToken: result.tokens.accessToken,
        refreshToken: result.tokens.refreshToken,
        expiresIn: result.tokens.expiresIn,
      }, 'Login successful');
    }
  );

  /**
   * User login with phone number and SMS code
   */
  public loginWithPhone = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { phoneNumber, smsCode } = req.body;

      this.logAction('LOGIN_PHONE_ATTEMPT', undefined, { phoneNumber });

      const result = await userService.loginWithPhone({
        phoneNumber,
        smsCode,
        ipAddress: req.ip,
      });

      this.logAction('LOGIN_PHONE_SUCCESS', result.user.id, {
        phoneNumber: result.user.phoneNumber,
        lastLoginAt: result.user.lastLoginAt,
      });

      this.sendSuccess(res, {
        user: result.user,
        accessToken: result.tokens.accessToken,
        refreshToken: result.tokens.refreshToken,
        expiresIn: result.tokens.expiresIn,
      }, 'Phone login successful');
    }
  );

  /**
   * User registration with password
   */
  public registerWithPassword = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { username, email, phoneNumber, password, fullName, age, gender } = req.body;

      this.logAction('REGISTER_PASSWORD_ATTEMPT', undefined, {
        username,
        email,
        phoneNumber,
      });

      const result = await userService.registerWithPassword({
        username,
        email,
        phoneNumber,
        password,
        fullName,
        age,
        gender,
      });

      this.logAction('REGISTER_PASSWORD_SUCCESS', result.user.id, {
        username: result.user.username,
        email: result.user.email,
      });

      this.sendSuccess(res, {
        user: result.user,
        accessToken: result.tokens.accessToken,
        refreshToken: result.tokens.refreshToken,
        expiresIn: result.tokens.expiresIn,
      }, 'Registration successful', 201);
    }
  );

  /**
   * User registration with phone number
   */
  public registerWithPhone = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { phoneNumber, smsCode, fullName, password, age, gender } = req.body;

      this.logAction('REGISTER_PHONE_ATTEMPT', undefined, { phoneNumber });

      const result = await userService.registerWithPhone({
        phoneNumber,
        smsCode,
        fullName,
        password,
        age,
        gender,
        ipAddress: req.ip,
      });

      this.logAction('REGISTER_PHONE_SUCCESS', result.user.id, {
        phoneNumber: result.user.phoneNumber,
      });

      this.sendSuccess(res, {
        user: result.user,
        accessToken: result.tokens.accessToken,
        refreshToken: result.tokens.refreshToken,
        expiresIn: result.tokens.expiresIn,
      }, 'Phone registration successful', 201);
    }
  );

  /**
   * Send SMS verification code
   */
  public sendSmsCode = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { phoneNumber, purpose } = req.body;

      this.logAction('SMS_CODE_REQUEST', undefined, { phoneNumber, purpose });

      const result = await smsService.sendVerificationCode({
        phoneNumber,
        purpose: purpose || 'register',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      this.logAction('SMS_CODE_SENT', undefined, {
        phoneNumber,
        purpose,
        expiresIn: result.expiresIn,
      });

      this.sendSuccess(res, {
        sent: result.sent,
        expiresIn: result.expiresIn,
        retryAfter: result.retryAfter,
      }, 'SMS code sent successfully');
    }
  );

  /**
   * Refresh access token using refresh token
   */
  public refreshToken = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);

      this.logAction('TOKEN_REFRESH_ATTEMPT', userId);

      const result = await userService.refreshTokens(userId);

      this.logAction('TOKEN_REFRESH_SUCCESS', userId);

      this.sendSuccess(res, {
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        expiresIn: result.expiresIn,
      }, 'Token refreshed successfully');
    }
  );

  /**
   * User logout (invalidate tokens)
   */
  public logout = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);

      this.logAction('LOGOUT_ATTEMPT', userId);

      await userService.logout(userId);

      this.logAction('LOGOUT_SUCCESS', userId);

      this.sendSuccess(res, null, 'Logout successful');
    }
  );

  /**
   * Verify SMS code
   */
  public verifySmsCode = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { phoneNumber, smsCode, codeId } = req.body;

      this.logAction('SMS_VERIFY_ATTEMPT', undefined, { phoneNumber, codeId });

      const isValid = await smsService.verifyCode(phoneNumber, smsCode, codeId);

      if (isValid) {
        this.logAction('SMS_VERIFY_SUCCESS', undefined, { phoneNumber, codeId });
        this.sendSuccess(res, { verified: true }, 'SMS code verified successfully');
      } else {
        this.logAction('SMS_VERIFY_FAILED', undefined, { phoneNumber, codeId });
        this.sendError(res, 'Invalid or expired SMS code', 400, 'INVALID_SMS_CODE');
      }
    }
  );

  /**
   * Get current user profile
   */
  public getProfile = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);

      this.logAction('GET_PROFILE', userId);

      const user = await userService.getUserById(userId);

      this.sendSuccess(res, user, 'Profile retrieved successfully');
    }
  );

  /**
   * Update user password
   */
  public updatePassword = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const userId = this.getUserId(req);
      const { currentPassword, newPassword } = req.body;

      this.logAction('UPDATE_PASSWORD_ATTEMPT', userId);

      await userService.updatePassword(userId, currentPassword, newPassword);

      this.logAction('UPDATE_PASSWORD_SUCCESS', userId);

      this.sendSuccess(res, null, 'Password updated successfully');
    }
  );

  /**
   * Reset password using SMS code
   */
  public resetPassword = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { phoneNumber, smsCode, newPassword } = req.body;

      this.logAction('RESET_PASSWORD_ATTEMPT', undefined, { phoneNumber });

      await userService.resetPassword({
        phoneNumber,
        smsCode,
        newPassword,
        ipAddress: req.ip,
      });

      this.logAction('RESET_PASSWORD_SUCCESS', undefined, { phoneNumber });

      this.sendSuccess(res, null, 'Password reset successfully');
    }
  );
}

// Export singleton instance
export const authController = new AuthController();
