import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

/**
 * Base controller class that provides common functionality for all controllers
 * Implements error handling, response formatting, and logging
 */
export abstract class BaseController {
  /**
   * Handles async operations and catches errors
   */
  protected asyncHandler = (fn: Function) => {
    return (req: Request, res: Response, next: NextFunction) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  };

  /**
   * Sends a successful response with standardized format
   */
  protected sendSuccess(
    res: Response,
    data: any = null,
    message: string = 'Operation successful',
    statusCode: number = 200
  ): void {
    const response = {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
    };

    logger.info('API Success Response', {
      statusCode,
      message,
      dataType: data ? typeof data : 'null',
    });

    res.status(statusCode).json(response);
  }

  /**
   * Sends a paginated response
   */
  protected sendPaginatedSuccess(
    res: Response,
    data: any[],
    pagination: {
      limit: number;
      offset: number;
      total: number;
      hasMore: boolean;
    },
    message: string = 'Data retrieved successfully',
    statusCode: number = 200
  ): void {
    const response = {
      success: true,
      data,
      pagination,
      message,
      timestamp: new Date().toISOString(),
    };

    logger.info('API Paginated Success Response', {
      statusCode,
      message,
      itemCount: data.length,
      pagination,
    });

    res.status(statusCode).json(response);
  }

  /**
   * Sends an error response
   */
  protected sendError(
    res: Response,
    message: string,
    statusCode: number = 500,
    code?: string,
    details?: any
  ): void {
    const response = {
      success: false,
      error: {
        code: code || 'INTERNAL_ERROR',
        message,
        ...(details && { details }),
      },
      timestamp: new Date().toISOString(),
    };

    logger.error('API Error Response', {
      statusCode,
      code,
      message,
      details,
    });

    res.status(statusCode).json(response);
  }

  /**
   * Extracts pagination parameters from request query
   */
  protected getPaginationParams(req: Request): {
    limit: number;
    offset: number;
  } {
    const limit = Math.min(
      parseInt(req.query.limit as string) || 20,
      100 // Maximum limit
    );
    const offset = parseInt(req.query.offset as string) || 0;

    return { limit, offset };
  }

  /**
   * Extracts user ID from authenticated request
   */
  protected getUserId(req: Request): string {
    const user = (req as any).user;
    if (!user || !user.sub) {
      throw new Error('User not authenticated');
    }
    return user.sub;
  }

  /**
   * Validates required fields in request body
   */
  protected validateRequiredFields(
    body: any,
    requiredFields: string[]
  ): void {
    const missingFields = requiredFields.filter(field => !body[field]);

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Logs controller action
   */
  protected logAction(
    action: string,
    userId?: string,
    metadata?: any
  ): void {
    logger.info(`Controller Action: ${action}`, {
      action,
      userId,
      metadata,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 发送成功响应
   * @param res Express响应对象
   * @param data 响应数据
   * @param message 成功消息
   */
  protected sendSuccessResponse(res: Response, data: any, message?: string): Response {
    return res.status(200).json({
      success: true,
      data,
      message: message || '操作成功',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 发送错误响应
   * @param res Express响应对象
   * @param error 错误消息
   * @param statusCode HTTP状态码
   */
  protected sendErrorResponse(res: Response, error: string, statusCode: number = 500): Response {
    return res.status(statusCode).json({
      success: false,
      error: {
        message: error,
        code: statusCode,
      },
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 发送分页响应
   * @param res Express响应对象
   * @param data 数据数组
   * @param pagination 分页信息
   * @param message 成功消息
   */
  protected sendPaginatedResponse(
    res: Response,
    data: any[],
    pagination: any,
    message?: string
  ): Response {
    return res.status(200).json({
      success: true,
      data,
      pagination,
      message: message || '获取成功',
      timestamp: new Date().toISOString(),
    });
  }
}

/**
 * Interface for controller dependencies
 */
export interface ControllerDependencies {
  [key: string]: any;
}

/**
 * Abstract factory for creating controllers with dependencies
 */
export abstract class ControllerFactory {
  protected dependencies: ControllerDependencies;

  constructor(dependencies: ControllerDependencies) {
    this.dependencies = dependencies;
  }

  abstract createController(): BaseController;
}
