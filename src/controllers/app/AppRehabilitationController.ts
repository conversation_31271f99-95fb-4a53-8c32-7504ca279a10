import { Request, Response, NextFunction } from 'express';
import { BaseController } from '@/controllers/BaseController';
import { AppRehabilitationService } from '@/services/app/AppRehabilitationService';

export class AppRehabilitationController extends BaseController {
  private rehabilitationService: AppRehabilitationService;

  constructor() {
    super();
    this.rehabilitationService = new AppRehabilitationService();
  }

  public getPlansList = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const plans = await this.rehabilitationService.getTrainingPlansList();
      this.sendSuccessResponse(res, plans, 'Training plans retrieved successfully.');
    } catch (error) {
      next(error);
    }
  };

  public getPlanDetails = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const planDetails = await this.rehabilitationService.getTrainingPlanDetails(id!);
      this.sendSuccessResponse(res, planDetails, 'Training plan details retrieved successfully.');
    } catch (error) {
      next(error);
    }
  };
} 