import { Request, Response, NextFunction } from 'express';
import { BaseController } from '@/controllers/BaseController';
import { AdminTrainingPlanService } from '@/services/admin/AdminTrainingPlanService';
import { logger } from '@/utils/logger';

export class AdminTrainingPlanController extends BaseController {
  private planService: AdminTrainingPlanService;

  constructor() {
    super();
    this.planService = new AdminTrainingPlanService();
  }

  public create = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const planData = { ...req.body, adminId: req.admin!.id };
      const newPlan = await this.planService.createPlan(planData);
      res.status(201);
      this.sendSuccessResponse(res, newPlan, 'Training plan created successfully.');
    } catch (error) {
      next(error);
    }
  };

  public getAll = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const result = await this.planService.findAllPlans(req.query);
      this.sendPaginatedResponse(res, result.data, result.pagination, 'Training plans retrieved successfully.');
    } catch (error) {
      next(error);
    }
  };

  public getById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const plan = await this.planService.findPlanById(req.params['id']!);
      this.sendSuccessResponse(res, plan, 'Training plan retrieved successfully.');
    } catch (error) {
      next(error);
    }
  };

  public update = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const planData = { ...req.body, adminId: req.admin!.id };
      const updatedPlan = await this.planService.updatePlan(req.params['id']!, planData);
      this.sendSuccessResponse(res, updatedPlan, 'Training plan updated successfully.');
    } catch (error) {
      next(error);
    }
  };

  public delete = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      await this.planService.deletePlan(req.params['id']!);
      this.sendSuccessResponse(res, null, 'Training plan deleted successfully.');
    } catch (error) {
      next(error);
    }
  };
} 