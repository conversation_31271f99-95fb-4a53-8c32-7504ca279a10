import { Request, Response, NextFunction } from 'express';
import { BaseController } from '@/controllers/BaseController';
import { AdminVideoService } from '@/services/admin/AdminVideoService';
import { logger } from '@/utils/logger';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// --- Multer Configuration ---
const tempDir = path.join(__dirname, '../../../../uploads/temp');

// Ensure temp directory exists
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1E9)}`;
    cb(null, `${file.fieldname}-${uniqueSuffix}${path.extname(file.originalname)}`);
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100 MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|mp4|avi|mov|wmv/;
    const mimetype = allowedTypes.test(file.mimetype);
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error(`File upload only supports the following filetypes: ${allowedTypes}`));
  },
});

export const videoUploadMiddleware = upload.single('video');
// --- End Multer Configuration ---


export class AdminVideoController extends BaseController {
  private adminVideoService: AdminVideoService;

  constructor() {
    super();
    this.adminVideoService = new AdminVideoService();
  }

  /**
   * @route POST /api/v1/admin/videos/upload
   * @description Handles video file upload and queuing for processing
   */
  public uploadVideo = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.file) {
        this.sendErrorResponse(res, 'No video file uploaded.', 400);
        return;
      }
      
      const { title, description, category, relatedId } = req.body;
      const uploaderId = req.admin?.id; // From authenticateAdmin middleware

      if (!uploaderId) {
        this.sendErrorResponse(res, 'Uploader not authenticated.', 401);
        return;
      }

      const videoData = {
        title,
        description,
        fileName: req.file.originalname,
        filePath: req.file.path,
        fileSize: req.file.size,
        category,
        relatedId,
        uploaderId,
      };

      const video = await this.adminVideoService.createAndQueueVideo(videoData);

      res.status(202).json({
        success: true,
        data: {
          videoId: video.id,
          status: 'queued',
          message: 'Video upload accepted and is being processed.',
        },
        message: 'Video processing started.',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      // If something fails, we might need to clean up the uploaded file
      if (req.file) {
        fs.unlink(req.file.path, (err) => {
          if (err) logger.error(`Failed to delete temp file: ${req.file?.path}`, err);
        });
      }
      next(error);
    }
  };
} 