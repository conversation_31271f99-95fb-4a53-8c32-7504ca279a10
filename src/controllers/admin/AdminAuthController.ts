import { Request, Response, NextFunction } from 'express';
import { AdminAuthService } from '@/services/admin/AdminAuthService';
import { BaseController } from '@/controllers/BaseController';
import { logger } from '@/utils/logger';

export class AdminAuthController extends BaseController {
  private adminAuthService: AdminAuthService;

  constructor() {
    super();
    this.adminAuthService = new AdminAuthService();
  }

  /**
   * @route POST /api/v1/admin/auth/login
   * @description Handles admin login
   */
  public login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { username, password } = req.body;
      const ipAddress = req.ip;
      const userAgent = req.get('User-Agent') || '';

      const result = await this.adminAuthService.login({ username, password, ipAddress, userAgent });

      logger.info(`Admin '${username}' logged in successfully from IP: ${ipAddress}`);
      this.sendSuccessResponse(res, result, 'Admin login successful');
    } catch (error) {
      next(error);
    }
  };

  /**
   * @route POST /api/v1/admin/auth/refresh
   * @description Refreshes an admin's access token
   */
  public refreshToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { refreshToken } = req.body;
      const ipAddress = req.ip;

      const newTokens = await this.adminAuthService.refreshToken(refreshToken, ipAddress);

      this.sendSuccessResponse(res, newTokens, 'Token refreshed successfully');
    } catch (error) {
      next(error);
    }
  };

  /**
   * @route POST /api/v1/admin/auth/logout
   * @description Logs out an admin
   */
  public logout = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { refreshToken } = req.body; // Optional: for invalidating a specific token
      const adminId = req.admin?.id;

      if (!adminId) {
        // This should theoretically not be reached if authenticateAdmin middleware is used
        this.sendErrorResponse(res, 'Admin not authenticated', 401);
        return;
      }

      await this.adminAuthService.logout(adminId, refreshToken);

      this.sendSuccessResponse(res, null, 'Logout successful');
    } catch (error) {
      next(error);
    }
  };
} 