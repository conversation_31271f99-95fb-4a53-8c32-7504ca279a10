/**
 * Training Plan Controller
 * Handles HTTP requests for training plan management and recommendations
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from './BaseController';
import { TrainingPlanService, TrainingPlanRecommendation, ExecutionTrackingData } from '@/services/TrainingPlanService';
import { CreateTrainingPlanData, UpdateTrainingPlanData, RecommendationCriteria } from '@/services/database/TrainingPlanRepository';
import { logger } from '@/utils/logger';

export class TrainingPlanController extends BaseController {
  private trainingPlanService: TrainingPlanService;

  constructor() {
    super();
    // Use lazy initialization to avoid circular dependency issues
    this.trainingPlanService = new TrainingPlanService();
  }

  /**
   * POST /api/v1/training/plans
   * Create a new training plan
   */
  public createTrainingPlan = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const planData: CreateTrainingPlanData = req.body;

      this.logAction('CREATE_TRAINING_PLAN', planData.userId);

      const plan = await this.trainingPlanService.createTrainingPlan(planData);

      this.sendSuccess(res, plan, 'Training plan created successfully', 201);
    }
  );

  /**
   * GET /api/v1/training/plans/:id
   * Get training plan by ID
   */
  public getTrainingPlanById = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;

      this.logAction('GET_TRAINING_PLAN', undefined, { planId: id });

      const plan = await this.trainingPlanService.getTrainingPlanById(id);

      if (!plan) {
        return this.sendError(res, 'TRAINING_PLAN_NOT_FOUND', 'Training plan not found', null, 404);
      }

      this.sendSuccess(res, plan, 'Training plan retrieved successfully');
    }
  );

  /**
   * GET /api/v1/training/plans/user/:userId
   * Get user training plans with pagination and filters
   */
  public getUserTrainingPlans = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const {
        status,
        targetCondition,
        difficultyLevel,
        createdBy,
        approvedBy,
        dateFrom,
        dateTo,
        page = 1,
        limit = 10,
      } = req.query;

      this.logAction('GET_USER_TRAINING_PLANS', userId);

      const filters = {
        ...(status && { status: status as string }),
        ...(targetCondition && { targetCondition: targetCondition as string }),
        ...(difficultyLevel && { difficultyLevel: parseInt(difficultyLevel as string) }),
        ...(createdBy && { createdBy: createdBy as string }),
        ...(approvedBy && { approvedBy: approvedBy as string }),
        ...(dateFrom && { dateFrom: new Date(dateFrom as string) }),
        ...(dateTo && { dateTo: new Date(dateTo as string) }),
      };

      const result = await this.trainingPlanService.getUserTrainingPlans(
        userId,
        filters,
        parseInt(page as string),
        parseInt(limit as string)
      );

      this.sendSuccess(res, result, 'User training plans retrieved successfully');
    }
  );

  /**
   * PUT /api/v1/training/plans/:id
   * Update training plan
   */
  public updateTrainingPlan = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;
      const updateData: UpdateTrainingPlanData = req.body;

      this.logAction('UPDATE_TRAINING_PLAN', undefined, { planId: id });

      const plan = await this.trainingPlanService.updateTrainingPlan(id, updateData);

      this.sendSuccess(res, plan, 'Training plan updated successfully');
    }
  );

  /**
   * POST /api/v1/training/plans/:id/approve
   * Approve training plan
   */
  public approveTrainingPlan = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;
      const { approvedBy } = req.body;

      this.logAction('APPROVE_TRAINING_PLAN', undefined, { planId: id, approvedBy });

      const plan = await this.trainingPlanService.approveTrainingPlan(id, approvedBy);

      this.sendSuccess(res, plan, 'Training plan approved successfully');
    }
  );

  /**
   * POST /api/v1/training/plans/:id/activate
   * Activate training plan
   */
  public activateTrainingPlan = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;

      this.logAction('ACTIVATE_TRAINING_PLAN', undefined, { planId: id });

      const plan = await this.trainingPlanService.activateTrainingPlan(id);

      this.sendSuccess(res, plan, 'Training plan activated successfully');
    }
  );

  /**
   * POST /api/v1/training/plans/:id/complete
   * Complete training plan
   */
  public completeTrainingPlan = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;
      const completionData = req.body;

      this.logAction('COMPLETE_TRAINING_PLAN', undefined, { planId: id });

      const plan = await this.trainingPlanService.completeTrainingPlan(id, completionData);

      this.sendSuccess(res, plan, 'Training plan completed successfully');
    }
  );

  /**
   * GET /api/v1/training/plans/user/:userId/recommendations
   * Get personalized training plan recommendations
   */
  public getPersonalizedRecommendations = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const {
        currentCondition,
        difficultyPreference,
        timeAvailability,
        previousPlans,
        assessmentResults,
        userPreferences,
      } = req.query;

      this.logAction('GET_PERSONALIZED_RECOMMENDATIONS', userId);

      const criteria: RecommendationCriteria = {
        userId,
        currentCondition: currentCondition as string,
        difficultyPreference: parseInt(difficultyPreference as string) || 1,
        timeAvailability: parseInt(timeAvailability as string) || 180, // Default 3 hours per week
        previousPlans: previousPlans ? (previousPlans as string).split(',') : [],
        assessmentResults: assessmentResults ? JSON.parse(assessmentResults as string) : undefined,
        userPreferences: userPreferences ? JSON.parse(userPreferences as string) : undefined,
      };

      const recommendations = await this.trainingPlanService.getPersonalizedRecommendations(criteria);

      this.sendSuccess(res, recommendations, 'Personalized recommendations retrieved successfully');
    }
  );

  /**
   * GET /api/v1/training/plans/user/:userId/stats
   * Get user training plan statistics
   */
  public getUserTrainingPlanStats = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { userId } = req.params;
      const { dateFrom, dateTo } = req.query;

      this.logAction('GET_USER_TRAINING_PLAN_STATS', userId);

      const stats = await this.trainingPlanService.getUserTrainingPlanStats(
        userId,
        dateFrom ? new Date(dateFrom as string) : undefined,
        dateTo ? new Date(dateTo as string) : undefined
      );

      this.sendSuccess(res, stats, 'User training plan statistics retrieved successfully');
    }
  );

  /**
   * POST /api/v1/training/plans/:id/track
   * Track training plan execution
   */
  public trackExecution = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;
      const trackingData: ExecutionTrackingData = {
        planId: id,
        ...req.body,
      };

      this.logAction('TRACK_TRAINING_EXECUTION', undefined, {
        planId: id,
        sessionId: trackingData.sessionId,
        status: trackingData.completionStatus,
      });

      await this.trainingPlanService.trackExecution(trackingData);

      this.sendSuccess(res, { tracked: true }, 'Training execution tracked successfully');
    }
  );

  /**
   * GET /api/v1/training/plans/:id/progress
   * Analyze training plan progress
   */
  public analyzeProgress = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;

      this.logAction('ANALYZE_TRAINING_PROGRESS', undefined, { planId: id });

      const analysis = await this.trainingPlanService.analyzeProgress(id);

      this.sendSuccess(res, analysis, 'Training plan progress analyzed successfully');
    }
  );

  /**
   * DELETE /api/v1/training/plans/:id
   * Delete training plan (soft delete)
   */
  public deleteTrainingPlan = this.asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { id } = req.params;

      this.logAction('DELETE_TRAINING_PLAN', undefined, { planId: id });

      const plan = await this.trainingPlanService.deleteTrainingPlan(id);

      this.sendSuccess(res, plan, 'Training plan deleted successfully');
    }
  );
}

// Export singleton instance
export const trainingPlanController = new TrainingPlanController();
