import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { badRequest } from '@/middleware/errorHandler';

// Common validation schemas
export const commonSchemas = {
  uuid: Joi.string().uuid().required(),
  email: Joi.string().email().required(),
  password: Joi.string()
    .min(6)
    .max(128)
    .pattern(/^(?=.*[A-Za-z])(?=.*\d)/)
    .message('Password must be at least 6 characters and contain both letters and numbers')
    .required(),
  username: Joi.string().alphanum().min(3).max(30).required(),
  phoneNumber: Joi.string()
    .pattern(/^\+?[1-9]\d{1,14}$/)
    .required(),
  pagination: {
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
  },
};

/**
 * Normalize phone number format for consistent storage and lookup
 * Removes country code prefixes and formatting characters
 */
export function normalizePhoneNumber(phoneNumber: string): string {
  // Remove all non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');

  // Handle Chinese phone numbers (+86)
  if (digitsOnly.startsWith('86') && digitsOnly.length === 13) {
    return digitsOnly.substring(2); // Remove '86' prefix
  }

  // Handle US phone numbers (+1)
  if (digitsOnly.startsWith('1') && digitsOnly.length === 11) {
    return digitsOnly.substring(1); // Remove '1' prefix
  }

  // For other cases, return the digits as-is
  return digitsOnly;
}

// Auth validation schemas
export const authSchemas = {
  login: Joi.object({
    identifier: Joi.string().required(), // Can be username, email, or phone
    password: Joi.string().required(), // Don't validate password strength on login
  }),

  loginWithPhone: Joi.object({
    phoneNumber: commonSchemas.phoneNumber,
    smsCode: Joi.string().length(6).pattern(/^\d+$/).required(),
  }),

  register: Joi.object({
    username: commonSchemas.username,
    email: commonSchemas.email.optional(),
    phoneNumber: commonSchemas.phoneNumber.optional(),
    password: commonSchemas.password,
    fullName: Joi.string().min(2).max(100).required(),
    age: Joi.number().integer().min(1).max(150).optional(),
    gender: Joi.string().valid('male', 'female', 'other').optional(),
  }).or('email', 'phoneNumber'), // At least one contact method required

  refreshToken: Joi.object({
    refreshToken: Joi.string().required(),
  }),

  sendSms: Joi.object({
    phoneNumber: commonSchemas.phoneNumber,
    purpose: Joi.string().valid('login', 'register', 'reset_password').required(),
  }),

  registerWithPhone: Joi.object({
    phoneNumber: commonSchemas.phoneNumber,
    smsCode: Joi.string().length(6).pattern(/^\d+$/).required(),
    fullName: Joi.string().min(2).max(100).required(),
    password: commonSchemas.password,
    age: Joi.number().integer().min(1).max(150).optional(),
    gender: Joi.string().valid('male', 'female', 'other').optional(),
  }),

  loginWithPassword: Joi.object({
    identifier: Joi.string().required(), // username or phoneNumber
    password: Joi.string().required(),
  }),

  resetPasswordRequest: Joi.object({
    phoneNumber: commonSchemas.phoneNumber,
    smsCode: Joi.string().length(6).pattern(/^\d+$/).required(),
    newPassword: commonSchemas.password,
  }),

  resetPassword: Joi.object({
    token: Joi.string().required(),
    newPassword: commonSchemas.password,
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: commonSchemas.password,
  }),
};

// Training session validation schemas
export const trainingSchemas = {
  createSession: Joi.object({
    userId: Joi.string().uuid().required(),
    trainingPlanId: Joi.string().uuid().optional(),
    trainingPlanName: Joi.string().min(1).max(255).required(),
    trainingType: Joi.string().valid('rehabilitation', 'game', 'assessment').required(),
  }),

  uploadSessionData: Joi.object({
    endTime: Joi.date().iso().required(),
    duration: Joi.number().integer().min(0).required(),
    averageGripStrength: Joi.number().min(0).max(10).required(),
    maxGripStrength: Joi.number().min(0).max(10).required(),
    averageAccuracy: Joi.number().min(0).max(1).required(),
    totalScore: Joi.number().integer().min(0).required(),
    completedActions: Joi.number().integer().min(0).required(),
    dataPoints: Joi.array().items(
      Joi.object({
        timestamp: Joi.date().iso().required(),
        gripStrength: Joi.number().min(0).max(10).required(),
        actionAccuracy: Joi.number().min(0).max(1).required(),
        targetGripStrength: Joi.number().min(0).max(10).optional(),
        targetAccuracy: Joi.number().min(0).max(1).optional(),
        score: Joi.number().integer().min(0).optional(),
      })
    ).optional(),
  }),

  updateGoal: Joi.object({
    targetDuration: Joi.number().integer().min(1).max(480).required(),
    targetActions: Joi.number().integer().min(1).max(100).required(),
    targetAccuracy: Joi.number().min(0).max(1).required(),
  }),

  recordsQuery: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    date: Joi.date().iso().optional(),
  }),

  realtimeDataQuery: Joi.object({
    limit: Joi.number().integer().min(1).max(1000).default(100),
    offset: Joi.number().integer().min(0).default(0),
  }),
};

// User validation schemas
export const userSchemas = {
  updateProfile: Joi.object({
    fullName: Joi.string().min(2).max(100).optional(),
    age: Joi.number().integer().min(1).max(150).optional(),
    gender: Joi.string().valid('male', 'female', 'other').optional(),
    recoveryPhase: Joi.string().max(50).optional(),
    healthMetrics: Joi.object({
      height: Joi.number().positive().max(300).optional(), // cm
      weight: Joi.number().positive().max(1000).optional(), // kg
    }).optional(),
  }),

  uploadAvatar: Joi.object({
    file: Joi.any().required(),
  }),
};

// Device validation schemas
export const deviceSchemas = {
  pairDevice: Joi.object({
    deviceMac: Joi.string()
      .pattern(/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/)
      .required(),
    deviceName: Joi.string().min(1).max(100).optional(),
    pairingCode: Joi.string().length(6).pattern(/^\d+$/).required(),
  }),

  updateDeviceStatus: Joi.object({
    deviceId: commonSchemas.uuid,
    status: Joi.string().valid('online', 'offline', 'error').required(),
    batteryLevel: Joi.number().integer().min(0).max(100).optional(),
    lastConnectedAt: Joi.date().iso().optional(),
  }),

  calibrateDevice: Joi.object({
    deviceId: commonSchemas.uuid,
    sensorType: Joi.string().valid('FSR', 'EMG', 'IMU', 'FLEX').required(),
    calibrationData: Joi.object({
      minValue: Joi.number().required(),
      maxValue: Joi.number().required(),
      forceRange: Joi.object({
        min: Joi.number().positive().required(),
        max: Joi.number().positive().required(),
      }).optional(),
    }).required(),
  }),
};

// Training validation schemas
export const trainingSchemas = {
  startSession: Joi.object({
    sessionType: Joi.string().valid('rehabilitation', 'game', 'assessment', 'free_training').required(),
    plannedDuration: Joi.number().integer().positive().max(7200).required(), // max 2 hours
    targetActions: Joi.number().integer().positive().optional(),
    deviceId: commonSchemas.uuid.optional(),
    rehabilitationPlanId: commonSchemas.uuid.optional(),
  }),

  endSession: Joi.object({
    sessionId: commonSchemas.uuid,
    endReason: Joi.string().valid('completed', 'timeout', 'user_stopped', 'device_error').required(),
    actualDuration: Joi.number().integer().positive().optional(),
    completedActions: Joi.number().integer().min(0).optional(),
    notes: Joi.string().max(500).optional(),
  }),

  uploadTrainingData: Joi.object({
    sessionId: commonSchemas.uuid,
    dataPoints: Joi.array()
      .items(
        Joi.object({
          timestamp: Joi.date().iso().required(),
          sequenceNumber: Joi.number().integer().min(1).required(),
          gripStrength: Joi.number().positive().optional(),
          accuracy: Joi.number().min(0).max(1).optional(),
          actionType: Joi.string().max(50).optional(),
          gestureType: Joi.string().max(50).optional(),
          confidence: Joi.number().min(0).max(1).optional(),
          sensorData: Joi.object().optional(),
        }),
      )
      .min(1)
      .max(1000)
      .required(), // Limit batch size
  }),
};

// Game validation schemas
export const gameSchemas = {
  saveGameRecord: Joi.object({
    gameType: Joi.string().valid('orchard_picking', 'grip_training').required(),
    level: Joi.number().integer().min(1).max(100).required(),
    score: Joi.number().integer().min(0).required(),
    fruitsCollected: Joi.number().integer().min(0).optional(),
    targetFruits: Joi.number().integer().min(0).optional(),
    averageGripStrength: Joi.number().positive().optional(),
    maxGripStrength: Joi.number().positive().optional(),
    minGripStrength: Joi.number().positive().optional(),
    accuracy: Joi.number().min(0).max(1).optional(),
    playTimeSeconds: Joi.number().integer().positive().optional(),
    comboCount: Joi.number().integer().min(0).optional(),
    perfectPicks: Joi.number().integer().min(0).optional(),
    missedFruits: Joi.number().integer().min(0).optional(),
    penaltyScore: Joi.number().integer().min(0).optional(),
    endReason: Joi.string().valid('completed', 'timeout', 'abandoned').required(),
    actions: Joi.array()
      .items(
        Joi.object({
          timestamp: Joi.date().iso().required(),
          actionType: Joi.string().required(),
          gripStrength: Joi.number().positive().optional(),
          accuracy: Joi.number().min(0).max(1).optional(),
          score: Joi.number().integer().min(0).optional(),
          fruitType: Joi.string().optional(),
          positionX: Joi.number().optional(),
          positionY: Joi.number().optional(),
        }),
      )
      .optional(),
    metadata: Joi.object().optional(),
  }),

  getGameRecords: Joi.object({
    gameType: Joi.string().valid('orchard_picking', 'grip_training').optional(),
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit,
  }),
};

// Community validation schemas
export const communitySchemas = {
  createPost: Joi.object({
    title: Joi.string().min(1).max(200).optional(),
    content: Joi.string().min(1).max(10000).required(),
    postType: Joi.string().valid('forum', 'friend_circle', 'expert_column').default('forum'),
    category: Joi.string().max(50).optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(10).optional(),
    images: Joi.array().items(Joi.string().uri()).max(5).optional(),
  }),

  updatePost: Joi.object({
    postId: commonSchemas.uuid,
    title: Joi.string().min(1).max(200).optional(),
    content: Joi.string().min(1).max(10000).optional(),
    category: Joi.string().max(50).optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(10).optional(),
  }),

  addComment: Joi.object({
    postId: commonSchemas.uuid,
    content: Joi.string().min(1).max(1000).required(),
    parentCommentId: commonSchemas.uuid.optional(),
  }),

  getPosts: Joi.object({
    postType: Joi.string().valid('forum', 'friend_circle', 'expert_column').optional(),
    category: Joi.string().max(50).optional(),
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit,
  }),
};

/**
 * Validation middleware factory
 */
export function validate(schema: Joi.ObjectSchema, source: 'body' | 'query' | 'params' = 'body') {
  return (req: Request, _res: Response, next: NextFunction): void => {
    const data = req[source];
    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
      convert: true,
    });

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      throw badRequest('Validation failed', { validation: details });
    }

    // Replace the original data with validated and sanitized data
    req[source] = value;
    next();
  };
}

/**
 * Validate UUID parameter
 */
export function validateUuidParam(paramName: string) {
  return validate(
    Joi.object({
      [paramName]: commonSchemas.uuid,
    }),
    'params',
  );
}

/**
 * Validate pagination query
 */
export const validatePagination = validate(Joi.object(commonSchemas.pagination), 'query');
