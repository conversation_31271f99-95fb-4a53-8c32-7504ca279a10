import jwt from 'jsonwebtoken';
import { logger } from '@/utils/logger';

export interface JwtPayload {
  sub: string; // user id or admin id
  username: string;
  email?: string | undefined;
  role?: string; // admin role (for admin tokens)
  permissions?: string[]; // admin permissions (for admin tokens)
  type?: 'user' | 'admin'; // token type
  iat?: number;
  exp?: number;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

class JwtService {
  private readonly accessTokenSecret: string;
  private readonly refreshTokenSecret: string;
  private readonly accessTokenExpiry: string;
  private readonly refreshTokenExpiry: string;

  constructor() {
    this.accessTokenSecret = process.env['JWT_SECRET'] || 'fallback-secret';
    this.refreshTokenSecret = `${this.accessTokenSecret}-refresh`;
    this.accessTokenExpiry = process.env['JWT_EXPIRES_IN'] || '15m';
    this.refreshTokenExpiry = process.env['REFRESH_TOKEN_EXPIRES_IN'] || '7d';

    if (this.accessTokenSecret === 'fallback-secret') {
      logger.warn('Using fallback JWT secret. This is not secure for production!');
    }
  }

  /**
   * Generate access token
   */
  generateAccessToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): string {
    try {
      return jwt.sign(payload as Record<string, any>, this.accessTokenSecret, {
        expiresIn: this.accessTokenExpiry,
        issuer: 'shoutao-backend',
        audience: 'shoutao-app',
      } as any);
    } catch (error) {
      logger.error('Failed to generate access token:', error);
      throw new Error('Token generation failed');
    }
  }

  /**
   * Generate refresh token
   */
  generateRefreshToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): string {
    try {
      return jwt.sign(payload as Record<string, any>, this.refreshTokenSecret, {
        expiresIn: this.refreshTokenExpiry,
        issuer: 'shoutao-backend',
        audience: 'shoutao-app',
      } as any);
    } catch (error) {
      logger.error('Failed to generate refresh token:', error);
      throw new Error('Token generation failed');
    }
  }

  /**
   * Generate token pair (access + refresh)
   */
  generateTokenPair(payload: Omit<JwtPayload, 'iat' | 'exp'>): TokenPair {
    const accessToken = this.generateAccessToken(payload);
    const refreshToken = this.generateRefreshToken(payload);

    // Calculate expiry in seconds
    const expiresIn = this.parseExpiryToSeconds(this.accessTokenExpiry);

    return {
      accessToken,
      refreshToken,
      expiresIn,
    };
  }

  /**
   * Verify access token
   */
  verifyAccessToken(token: string): JwtPayload {
    try {
      const decoded = jwt.verify(token, this.accessTokenSecret, {
        issuer: 'shoutao-backend',
        audience: 'shoutao-app',
      }) as JwtPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Access token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid access token');
      } else {
        logger.error('Access token verification failed:', error);
        throw new Error('Token verification failed');
      }
    }
  }

  /**
   * Verify refresh token
   */
  verifyRefreshToken(token: string): JwtPayload {
    try {
      const decoded = jwt.verify(token, this.refreshTokenSecret, {
        issuer: 'shoutao-backend',
        audience: 'shoutao-app',
      }) as JwtPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid refresh token');
      } else {
        logger.error('Refresh token verification failed:', error);
        throw new Error('Token verification failed');
      }
    }
  }

  /**
   * Extract token from Authorization header
   */
  extractTokenFromHeader(authHeader?: string): string | null {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1] || null;
  }

  /**
   * Parse expiry string to seconds
   */
  private parseExpiryToSeconds(expiry: string): number {
    const match = expiry.match(/^(\d+)([smhd])$/);
    if (!match || !match[1] || !match[2]) {
      return 900; // Default 15 minutes
    }

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
    case 's':
      return value;
    case 'm':
      return value * 60;
    case 'h':
      return value * 60 * 60;
    case 'd':
      return value * 60 * 60 * 24;
    default:
      return 900;
    }
  }

  /**
   * Get token expiry date
   */
  getTokenExpiry(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as JwtPayload;
      if (decoded && decoded.exp) {
        return new Date(decoded.exp * 1000);
      }
      return null;
    } catch (error) {
      logger.error('Failed to decode token for expiry:', error);
      return null;
    }
  }
}

export const jwtService = new JwtService();
export default jwtService;
