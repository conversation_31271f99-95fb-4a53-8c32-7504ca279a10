/**
 * Health Assessment Validation Schemas
 * Joi validation schemas for health assessment endpoints
 */

import <PERSON><PERSON> from 'joi';

/**
 * Create Health Assessment Schema
 */
export const createHealthAssessmentSchema = Joi.object({
  userId: Joi.string().uuid().required().messages({
    'string.uuid': 'User ID must be a valid UUID',
    'any.required': 'User ID is required',
  }),
  templateId: Joi.string().uuid().optional().messages({
    'string.uuid': 'Template ID must be a valid UUID',
  }),
  assessmentType: Joi.string().valid(
    'motor_function',
    'cognitive_assessment',
    'strength_evaluation',
    'coordination_test',
    'range_of_motion',
    'functional_capacity',
    'comprehensive'
  ).required().messages({
    'any.only': 'Assessment type must be one of: motor_function, cognitive_assessment, strength_evaluation, coordination_test, range_of_motion, functional_capacity, comprehensive',
    'any.required': 'Assessment type is required',
  }),
  assessmentName: Joi.string().min(3).max(100).required().messages({
    'string.min': 'Assessment name must be at least 3 characters long',
    'string.max': 'Assessment name must not exceed 100 characters',
    'any.required': 'Assessment name is required',
  }),
  description: Jo<PERSON>.string().max(500).optional().messages({
    'string.max': 'Description must not exceed 500 characters',
  }),
  conductedBy: Joi.string().uuid().optional().messages({
    'string.uuid': 'Conducted by must be a valid UUID',
  }),
});

/**
 * Update Health Assessment Schema
 */
export const updateHealthAssessmentSchema = Joi.object({
  status: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled').optional().messages({
    'any.only': 'Status must be one of: pending, in_progress, completed, cancelled',
  }),
  assessmentName: Joi.string().min(3).max(100).optional().messages({
    'string.min': 'Assessment name must be at least 3 characters long',
    'string.max': 'Assessment name must not exceed 100 characters',
  }),
  description: Joi.string().max(500).optional().messages({
    'string.max': 'Description must not exceed 500 characters',
  }),
  notes: Joi.string().max(1000).optional().messages({
    'string.max': 'Notes must not exceed 1000 characters',
  }),
  reviewedBy: Joi.string().uuid().optional().messages({
    'string.uuid': 'Reviewed by must be a valid UUID',
  }),
}).min(1).messages({
  'object.min': 'At least one field must be provided for update',
});

/**
 * Assessment Task Data Schema
 */
export const assessmentTaskDataSchema = Joi.object({
  taskName: Joi.string().min(3).max(100).required().messages({
    'string.min': 'Task name must be at least 3 characters long',
    'string.max': 'Task name must not exceed 100 characters',
    'any.required': 'Task name is required',
  }),
  taskType: Joi.string().valid(
    'grip_strength',
    'finger_dexterity',
    'range_of_motion',
    'coordination',
    'reaction_time',
    'precision',
    'endurance',
    'cognitive'
  ).required().messages({
    'any.only': 'Task type must be one of: grip_strength, finger_dexterity, range_of_motion, coordination, reaction_time, precision, endurance, cognitive',
    'any.required': 'Task type is required',
  }),
  taskOrder: Joi.number().integer().min(1).required().messages({
    'number.integer': 'Task order must be an integer',
    'number.min': 'Task order must be at least 1',
    'any.required': 'Task order is required',
  }),
  startTime: Joi.date().iso().required().messages({
    'date.format': 'Start time must be a valid ISO date',
    'any.required': 'Start time is required',
  }),
  endTime: Joi.date().iso().min(Joi.ref('startTime')).required().messages({
    'date.format': 'End time must be a valid ISO date',
    'date.min': 'End time must be after start time',
    'any.required': 'End time is required',
  }),
  accuracy: Joi.number().min(0).max(1).optional().messages({
    'number.min': 'Accuracy must be between 0 and 1',
    'number.max': 'Accuracy must be between 0 and 1',
  }),
  performanceData: Joi.object().required().messages({
    'any.required': 'Performance data is required',
  }),
  gripStrengthData: Joi.object({
    readings: Joi.array().items(Joi.number().min(0).max(100)).optional(),
    target: Joi.number().min(0).max(100).optional(),
    average: Joi.number().min(0).max(100).optional(),
    maximum: Joi.number().min(0).max(100).optional(),
    consistency: Joi.number().min(0).max(1).optional(),
  }).optional(),
  movementData: Joi.object({
    smoothness: Joi.number().min(0).max(1).optional(),
    precision: Joi.number().min(0).max(1).optional(),
    rangeOfMotion: Joi.number().min(0).max(1).optional(),
    tremorLevel: Joi.number().min(0).max(1).optional(),
    speed: Joi.number().min(0).optional(),
  }).optional(),
  notes: Joi.string().max(500).optional().messages({
    'string.max': 'Notes must not exceed 500 characters',
  }),
});

/**
 * Complete Assessment Schema
 */
export const completeAssessmentSchema = Joi.object({
  results: Joi.array().items(assessmentTaskDataSchema).min(1).required().messages({
    'array.min': 'At least one task result is required',
    'any.required': 'Results are required',
  }),
  overallNotes: Joi.string().max(1000).optional().messages({
    'string.max': 'Overall notes must not exceed 1000 characters',
  }),
  recommendations: Joi.object().optional(),
});

/**
 * Generate Assessment Report Schema
 */
export const generateAssessmentReportSchema = Joi.object({
  reportType: Joi.string().valid(
    'comprehensive',
    'summary',
    'progress',
    'clinical',
    'patient_friendly'
  ).default('comprehensive').messages({
    'any.only': 'Report type must be one of: comprehensive, summary, progress, clinical, patient_friendly',
  }),
  title: Joi.string().min(5).max(200).optional().messages({
    'string.min': 'Title must be at least 5 characters long',
    'string.max': 'Title must not exceed 200 characters',
  }),
  summary: Joi.string().min(10).max(1000).optional().messages({
    'string.min': 'Summary must be at least 10 characters long',
    'string.max': 'Summary must not exceed 1000 characters',
  }),
  detailedAnalysis: Joi.string().min(20).max(5000).optional().messages({
    'string.min': 'Detailed analysis must be at least 20 characters long',
    'string.max': 'Detailed analysis must not exceed 5000 characters',
  }),
  isPublic: Joi.boolean().default(false),
});

/**
 * Query Parameters Schema for User Assessments
 */
export const getUserAssessmentsQuerySchema = Joi.object({
  assessmentType: Joi.string().valid(
    'motor_function',
    'cognitive_assessment',
    'strength_evaluation',
    'coordination_test',
    'range_of_motion',
    'functional_capacity',
    'comprehensive'
  ).optional(),
  status: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled').optional(),
  templateId: Joi.string().uuid().optional(),
  dateFrom: Joi.date().iso().optional(),
  dateTo: Joi.date().iso().min(Joi.ref('dateFrom')).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
});

/**
 * Query Parameters Schema for Assessment Statistics
 */
export const getAssessmentStatsQuerySchema = Joi.object({
  assessmentType: Joi.string().valid(
    'motor_function',
    'cognitive_assessment',
    'strength_evaluation',
    'coordination_test',
    'range_of_motion',
    'functional_capacity',
    'comprehensive'
  ).optional(),
  dateFrom: Joi.date().iso().optional(),
  dateTo: Joi.date().iso().min(Joi.ref('dateFrom')).optional(),
});

/**
 * Query Parameters Schema for Assessment Templates
 */
export const getAssessmentTemplatesQuerySchema = Joi.object({
  templateType: Joi.string().valid(
    'motor_function',
    'cognitive_assessment',
    'strength_evaluation',
    'coordination_test',
    'range_of_motion',
    'functional_capacity',
    'comprehensive'
  ).optional(),
});

/**
 * UUID Parameter Schema
 */
export const uuidParamSchema = Joi.object({
  id: Joi.string().uuid().required().messages({
    'string.uuid': 'ID must be a valid UUID',
    'any.required': 'ID is required',
  }),
});

/**
 * User ID Parameter Schema
 */
export const userIdParamSchema = Joi.object({
  userId: Joi.string().uuid().required().messages({
    'string.uuid': 'User ID must be a valid UUID',
    'any.required': 'User ID is required',
  }),
});
