/**
 * Training Record Validation Schemas
 * Joi validation schemas for training record endpoints
 */

import Jo<PERSON> from 'joi';

/**
 * User ID Parameter Schema
 */
export const userIdParamSchema = Joi.object({
  userId: Joi.string().uuid().required().messages({
    'string.uuid': 'User ID must be a valid UUID',
    'any.required': 'User ID is required',
  }),
});

/**
 * Daily Records Query Schema
 */
export const dailyRecordsQuerySchema = Joi.object({
  date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required().messages({
    'string.pattern.base': 'Date must be in YYYY-MM-DD format',
    'any.required': 'Date parameter is required',
  }),
});

/**
 * Weekly Records Query Schema
 */
export const weeklyRecordsQuerySchema = Joi.object({
  week: Joi.string().pattern(/^\d{4}-W\d{2}$/).required().messages({
    'string.pattern.base': 'Week must be in YYYY-WW format (e.g., 2024-W15)',
    'any.required': 'Week parameter is required',
  }),
});

/**
 * Monthly Records Query Schema
 */
export const monthlyRecordsQuerySchema = Joi.object({
  month: Joi.string().pattern(/^\d{4}-\d{2}$/).required().messages({
    'string.pattern.base': 'Month must be in YYYY-MM format',
    'any.required': 'Month parameter is required',
  }),
});

/**
 * Export Data Query Schema
 */
export const exportDataQuerySchema = Joi.object({
  format: Joi.string().valid('json', 'csv').default('json').messages({
    'any.only': 'Format must be either json or csv',
  }),
  period: Joi.string().valid('day', 'week', 'month').default('month').messages({
    'any.only': 'Period must be day, week, or month',
  }),
  date: Joi.string().required().custom((value, helpers) => {
    const period = helpers.state.ancestors[0].period;
    
    switch (period) {
      case 'day':
        if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
          return helpers.error('date.invalidDayFormat');
        }
        break;
      case 'week':
        if (!/^\d{4}-W\d{2}$/.test(value)) {
          return helpers.error('date.invalidWeekFormat');
        }
        break;
      case 'month':
        if (!/^\d{4}-\d{2}$/.test(value)) {
          return helpers.error('date.invalidMonthFormat');
        }
        break;
    }
    
    return value;
  }).messages({
    'date.invalidDayFormat': 'Date must be in YYYY-MM-DD format for daily export',
    'date.invalidWeekFormat': 'Date must be in YYYY-WW format for weekly export',
    'date.invalidMonthFormat': 'Date must be in YYYY-MM format for monthly export',
    'any.required': 'Date parameter is required',
  }),
});

/**
 * User Summary Query Schema
 */
export const userSummaryQuerySchema = Joi.object({
  days: Joi.number().integer().min(1).max(90).default(30).messages({
    'number.integer': 'Days must be an integer',
    'number.min': 'Days must be at least 1',
    'number.max': 'Days must not exceed 90',
  }),
});

/**
 * Date Range Query Schema (for future use)
 */
export const dateRangeQuerySchema = Joi.object({
  startDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required().messages({
    'string.pattern.base': 'Start date must be in YYYY-MM-DD format',
    'any.required': 'Start date is required',
  }),
  endDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required().custom((value, helpers) => {
    const startDate = helpers.state.ancestors[0].startDate;
    if (startDate && new Date(value) < new Date(startDate)) {
      return helpers.error('date.endBeforeStart');
    }
    return value;
  }).messages({
    'string.pattern.base': 'End date must be in YYYY-MM-DD format',
    'date.endBeforeStart': 'End date must be after start date',
    'any.required': 'End date is required',
  }),
  limit: Joi.number().integer().min(1).max(1000).default(100).messages({
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit must not exceed 1000',
  }),
  offset: Joi.number().integer().min(0).default(0).messages({
    'number.integer': 'Offset must be an integer',
    'number.min': 'Offset must be at least 0',
  }),
});

/**
 * Cleanup Options Schema
 */
export const cleanupOptionsSchema = Joi.object({
  dryRun: Joi.boolean().default(false).messages({
    'boolean.base': 'Dry run must be a boolean value',
  }),
  retentionDays: Joi.number().integer().min(1).max(365).default(30).messages({
    'number.integer': 'Retention days must be an integer',
    'number.min': 'Retention days must be at least 1',
    'number.max': 'Retention days must not exceed 365',
  }),
  batchSize: Joi.number().integer().min(1).max(10000).default(1000).messages({
    'number.integer': 'Batch size must be an integer',
    'number.min': 'Batch size must be at least 1',
    'number.max': 'Batch size must not exceed 10000',
  }),
});

/**
 * Data Filter Schema (for advanced filtering)
 */
export const dataFilterSchema = Joi.object({
  sessionStatus: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled').optional().messages({
    'any.only': 'Session status must be one of: pending, in_progress, completed, cancelled',
  }),
  minScore: Joi.number().min(0).max(100).optional().messages({
    'number.min': 'Minimum score must be at least 0',
    'number.max': 'Minimum score must not exceed 100',
  }),
  maxScore: Joi.number().min(0).max(100).optional().messages({
    'number.min': 'Maximum score must be at least 0',
    'number.max': 'Maximum score must not exceed 100',
  }),
  minDuration: Joi.number().integer().min(0).optional().messages({
    'number.integer': 'Minimum duration must be an integer',
    'number.min': 'Minimum duration must be at least 0',
  }),
  maxDuration: Joi.number().integer().min(0).optional().messages({
    'number.integer': 'Maximum duration must be an integer',
    'number.min': 'Maximum duration must be at least 0',
  }),
  minAccuracy: Joi.number().min(0).max(1).optional().messages({
    'number.min': 'Minimum accuracy must be between 0 and 1',
    'number.max': 'Minimum accuracy must be between 0 and 1',
  }),
  maxAccuracy: Joi.number().min(0).max(1).optional().messages({
    'number.min': 'Maximum accuracy must be between 0 and 1',
    'number.max': 'Maximum accuracy must be between 0 and 1',
  }),
  planId: Joi.string().uuid().optional().messages({
    'string.uuid': 'Plan ID must be a valid UUID',
  }),
  exerciseType: Joi.string().valid(
    'grip_strength',
    'finger_dexterity',
    'range_of_motion',
    'coordination',
    'endurance',
    'precision',
    'cognitive'
  ).optional().messages({
    'any.only': 'Exercise type must be one of: grip_strength, finger_dexterity, range_of_motion, coordination, endurance, precision, cognitive',
  }),
}).custom((value, helpers) => {
  // Validate that min values are not greater than max values
  if (value.minScore !== undefined && value.maxScore !== undefined && value.minScore > value.maxScore) {
    return helpers.error('filter.minGreaterThanMax', { field: 'score' });
  }
  if (value.minDuration !== undefined && value.maxDuration !== undefined && value.minDuration > value.maxDuration) {
    return helpers.error('filter.minGreaterThanMax', { field: 'duration' });
  }
  if (value.minAccuracy !== undefined && value.maxAccuracy !== undefined && value.minAccuracy > value.maxAccuracy) {
    return helpers.error('filter.minGreaterThanMax', { field: 'accuracy' });
  }
  return value;
}).messages({
  'filter.minGreaterThanMax': 'Minimum {{#field}} cannot be greater than maximum {{#field}}',
});

/**
 * Chart Data Options Schema
 */
export const chartDataOptionsSchema = Joi.object({
  includeChartData: Joi.boolean().default(true).messages({
    'boolean.base': 'Include chart data must be a boolean value',
  }),
  chartResolution: Joi.string().valid('hour', 'day', 'week', 'month').default('day').messages({
    'any.only': 'Chart resolution must be one of: hour, day, week, month',
  }),
  aggregateBy: Joi.string().valid('sum', 'average', 'count', 'max', 'min').default('average').messages({
    'any.only': 'Aggregate by must be one of: sum, average, count, max, min',
  }),
  includeComparisons: Joi.boolean().default(false).messages({
    'boolean.base': 'Include comparisons must be a boolean value',
  }),
});

/**
 * Pagination Schema
 */
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.integer': 'Page must be an integer',
    'number.min': 'Page must be at least 1',
  }),
  limit: Joi.number().integer().min(1).max(100).default(10).messages({
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit must not exceed 100',
  }),
  sortBy: Joi.string().valid('date', 'score', 'duration', 'accuracy', 'sessions').default('date').messages({
    'any.only': 'Sort by must be one of: date, score, duration, accuracy, sessions',
  }),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
    'any.only': 'Sort order must be either asc or desc',
  }),
});

/**
 * Combined Query Schema for Advanced Endpoints
 */
export const advancedQuerySchema = Joi.object({
  ...dateRangeQuerySchema.describe().keys,
  ...dataFilterSchema.describe().keys,
  ...chartDataOptionsSchema.describe().keys,
  ...paginationSchema.describe().keys,
});

/**
 * Bulk Operation Schema
 */
export const bulkOperationSchema = Joi.object({
  operation: Joi.string().valid('export', 'cleanup', 'analyze').required().messages({
    'any.only': 'Operation must be one of: export, cleanup, analyze',
    'any.required': 'Operation is required',
  }),
  userIds: Joi.array().items(Joi.string().uuid()).min(1).max(100).optional().messages({
    'array.min': 'At least one user ID is required',
    'array.max': 'Cannot process more than 100 users at once',
    'string.uuid': 'Each user ID must be a valid UUID',
  }),
  options: Joi.object().optional(),
  async: Joi.boolean().default(false).messages({
    'boolean.base': 'Async must be a boolean value',
  }),
});
