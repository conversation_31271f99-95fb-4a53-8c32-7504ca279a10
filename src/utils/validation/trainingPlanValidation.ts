/**
 * Training Plan Validation Schemas
 * Joi validation schemas for training plan endpoints
 */

import <PERSON><PERSON> from 'joi';

/**
 * Exercise Schema for plan configuration
 */
export const exerciseSchema = Joi.object({
  name: Joi.string().min(3).max(100).required().messages({
    'string.min': 'Exercise name must be at least 3 characters long',
    'string.max': 'Exercise name must not exceed 100 characters',
    'any.required': 'Exercise name is required',
  }),
  type: Joi.string().valid(
    'grip_strength',
    'finger_dexterity',
    'range_of_motion',
    'coordination',
    'endurance',
    'precision',
    'cognitive'
  ).required().messages({
    'any.only': 'Exercise type must be one of: grip_strength, finger_dexterity, range_of_motion, coordination, endurance, precision, cognitive',
    'any.required': 'Exercise type is required',
  }),
  description: Joi.string().max(500).optional().messages({
    'string.max': 'Exercise description must not exceed 500 characters',
  }),
  duration: Joi.number().integer().min(30).max(3600).optional().messages({
    'number.integer': 'Duration must be an integer',
    'number.min': 'Duration must be at least 30 seconds',
    'number.max': 'Duration must not exceed 3600 seconds (1 hour)',
  }),
  repetitions: Joi.number().integer().min(1).max(100).optional().messages({
    'number.integer': 'Repetitions must be an integer',
    'number.min': 'Repetitions must be at least 1',
    'number.max': 'Repetitions must not exceed 100',
  }),
  intensity: Joi.number().min(0.1).max(1.0).optional().messages({
    'number.min': 'Intensity must be between 0.1 and 1.0',
    'number.max': 'Intensity must be between 0.1 and 1.0',
  }),
  restPeriod: Joi.number().integer().min(0).max(300).optional().messages({
    'number.integer': 'Rest period must be an integer',
    'number.min': 'Rest period must be at least 0 seconds',
    'number.max': 'Rest period must not exceed 300 seconds',
  }),
  targetMetrics: Joi.object({
    accuracy: Joi.number().min(0).max(1).optional(),
    speed: Joi.number().min(0).optional(),
    strength: Joi.number().min(0).max(100).optional(),
    endurance: Joi.number().min(0).optional(),
  }).optional(),
}).or('duration', 'repetitions').messages({
  'object.missing': 'Each exercise must specify either duration or repetitions',
});

/**
 * Plan Configuration Schema
 */
export const planConfigSchema = Joi.object({
  exercises: Joi.array().items(exerciseSchema).min(1).required().messages({
    'array.min': 'Plan must include at least one exercise',
    'any.required': 'Exercises are required',
  }),
  warmUp: Joi.object({
    duration: Joi.number().integer().min(60).max(600).default(300),
    exercises: Joi.array().items(exerciseSchema).optional(),
  }).optional(),
  coolDown: Joi.object({
    duration: Joi.number().integer().min(60).max(600).default(300),
    exercises: Joi.array().items(exerciseSchema).optional(),
  }).optional(),
  progressionRules: Joi.object({
    accuracyThreshold: Joi.number().min(0).max(1).default(0.8),
    scoreThreshold: Joi.number().min(0).max(100).default(70),
    consistencyDays: Joi.number().integer().min(1).max(14).default(3),
  }).optional(),
  adaptationRules: Joi.object({
    difficultyIncrease: Joi.number().min(0.1).max(0.5).default(0.2),
    maxDifficultyLevel: Joi.number().integer().min(1).max(10).default(5),
    adaptationFrequency: Joi.number().integer().min(1).max(30).default(7), // days
  }).optional(),
});

/**
 * Create Training Plan Schema
 */
export const createTrainingPlanSchema = Joi.object({
  userId: Joi.string().uuid().required().messages({
    'string.uuid': 'User ID must be a valid UUID',
    'any.required': 'User ID is required',
  }),
  planName: Joi.string().min(3).max(100).required().messages({
    'string.min': 'Plan name must be at least 3 characters long',
    'string.max': 'Plan name must not exceed 100 characters',
    'any.required': 'Plan name is required',
  }),
  description: Joi.string().max(1000).optional().messages({
    'string.max': 'Description must not exceed 1000 characters',
  }),
  targetCondition: Joi.string().min(3).max(100).required().messages({
    'string.min': 'Target condition must be at least 3 characters long',
    'string.max': 'Target condition must not exceed 100 characters',
    'any.required': 'Target condition is required',
  }),
  difficultyLevel: Joi.number().integer().min(1).max(10).default(1).messages({
    'number.integer': 'Difficulty level must be an integer',
    'number.min': 'Difficulty level must be between 1 and 10',
    'number.max': 'Difficulty level must be between 1 and 10',
  }),
  estimatedDurationWeeks: Joi.number().integer().min(1).max(52).default(4).messages({
    'number.integer': 'Estimated duration must be an integer',
    'number.min': 'Estimated duration must be between 1 and 52 weeks',
    'number.max': 'Estimated duration must be between 1 and 52 weeks',
  }),
  sessionsPerWeek: Joi.number().integer().min(1).max(14).default(3).messages({
    'number.integer': 'Sessions per week must be an integer',
    'number.min': 'Sessions per week must be between 1 and 14',
    'number.max': 'Sessions per week must be between 1 and 14',
  }),
  sessionDurationMinutes: Joi.number().integer().min(10).max(180).default(30).messages({
    'number.integer': 'Session duration must be an integer',
    'number.min': 'Session duration must be between 10 and 180 minutes',
    'number.max': 'Session duration must be between 10 and 180 minutes',
  }),
  planConfig: planConfigSchema.required().messages({
    'any.required': 'Plan configuration is required',
  }),
  goals: Joi.object({
    primary: Joi.string().max(200).required(),
    secondary: Joi.array().items(Joi.string().max(200)).optional(),
    targetMetrics: Joi.object({
      accuracy: Joi.number().min(0).max(1).optional(),
      strength: Joi.number().min(0).max(100).optional(),
      endurance: Joi.number().min(0).optional(),
      consistency: Joi.number().min(0).max(1).optional(),
    }).optional(),
  }).required().messages({
    'any.required': 'Goals are required',
  }),
  createdBy: Joi.string().uuid().optional().messages({
    'string.uuid': 'Created by must be a valid UUID',
  }),
  approvedBy: Joi.string().uuid().optional().messages({
    'string.uuid': 'Approved by must be a valid UUID',
  }),
});

/**
 * Update Training Plan Schema
 */
export const updateTrainingPlanSchema = Joi.object({
  planName: Joi.string().min(3).max(100).optional().messages({
    'string.min': 'Plan name must be at least 3 characters long',
    'string.max': 'Plan name must not exceed 100 characters',
  }),
  description: Joi.string().max(1000).optional().messages({
    'string.max': 'Description must not exceed 1000 characters',
  }),
  difficultyLevel: Joi.number().integer().min(1).max(10).optional().messages({
    'number.integer': 'Difficulty level must be an integer',
    'number.min': 'Difficulty level must be between 1 and 10',
    'number.max': 'Difficulty level must be between 1 and 10',
  }),
  estimatedDurationWeeks: Joi.number().integer().min(1).max(52).optional().messages({
    'number.integer': 'Estimated duration must be an integer',
    'number.min': 'Estimated duration must be between 1 and 52 weeks',
    'number.max': 'Estimated duration must be between 1 and 52 weeks',
  }),
  sessionsPerWeek: Joi.number().integer().min(1).max(14).optional().messages({
    'number.integer': 'Sessions per week must be an integer',
    'number.min': 'Sessions per week must be between 1 and 14',
    'number.max': 'Sessions per week must be between 1 and 14',
  }),
  sessionDurationMinutes: Joi.number().integer().min(10).max(180).optional().messages({
    'number.integer': 'Session duration must be an integer',
    'number.min': 'Session duration must be between 10 and 180 minutes',
    'number.max': 'Session duration must be between 10 and 180 minutes',
  }),
  planConfig: planConfigSchema.optional(),
  goals: Joi.object({
    primary: Joi.string().max(200).required(),
    secondary: Joi.array().items(Joi.string().max(200)).optional(),
    targetMetrics: Joi.object({
      accuracy: Joi.number().min(0).max(1).optional(),
      strength: Joi.number().min(0).max(100).optional(),
      endurance: Joi.number().min(0).optional(),
      consistency: Joi.number().min(0).max(1).optional(),
    }).optional(),
  }).optional(),
  status: Joi.string().valid('draft', 'pending_approval', 'approved', 'active', 'completed', 'cancelled').optional().messages({
    'any.only': 'Status must be one of: draft, pending_approval, approved, active, completed, cancelled',
  }),
  notes: Joi.string().max(1000).optional().messages({
    'string.max': 'Notes must not exceed 1000 characters',
  }),
}).min(1).messages({
  'object.min': 'At least one field must be provided for update',
});

/**
 * Approve Training Plan Schema
 */
export const approveTrainingPlanSchema = Joi.object({
  approvedBy: Joi.string().uuid().required().messages({
    'string.uuid': 'Approved by must be a valid UUID',
    'any.required': 'Approved by is required',
  }),
  notes: Joi.string().max(500).optional().messages({
    'string.max': 'Notes must not exceed 500 characters',
  }),
});

/**
 * Complete Training Plan Schema
 */
export const completeTrainingPlanSchema = Joi.object({
  completionNotes: Joi.string().max(1000).optional().messages({
    'string.max': 'Completion notes must not exceed 1000 characters',
  }),
  finalMetrics: Joi.object({
    overallScore: Joi.number().min(0).max(100).optional(),
    averageAccuracy: Joi.number().min(0).max(1).optional(),
    totalSessions: Joi.number().integer().min(0).optional(),
    completedSessions: Joi.number().integer().min(0).optional(),
  }).optional(),
});

/**
 * Track Execution Schema
 */
export const trackExecutionSchema = Joi.object({
  sessionId: Joi.string().uuid().required().messages({
    'string.uuid': 'Session ID must be a valid UUID',
    'any.required': 'Session ID is required',
  }),
  completionStatus: Joi.string().valid('completed', 'partial', 'skipped').required().messages({
    'any.only': 'Completion status must be one of: completed, partial, skipped',
    'any.required': 'Completion status is required',
  }),
  performanceMetrics: Joi.object({
    accuracy: Joi.number().min(0).max(1).required(),
    duration: Joi.number().integer().min(0).required(),
    score: Joi.number().min(0).max(100).required(),
  }).required().messages({
    'any.required': 'Performance metrics are required',
  }),
  notes: Joi.string().max(500).optional().messages({
    'string.max': 'Notes must not exceed 500 characters',
  }),
});

/**
 * Query Parameters Schema for User Training Plans
 */
export const getUserTrainingPlansQuerySchema = Joi.object({
  status: Joi.string().valid('draft', 'pending_approval', 'approved', 'active', 'completed', 'cancelled').optional(),
  targetCondition: Joi.string().max(100).optional(),
  difficultyLevel: Joi.number().integer().min(1).max(10).optional(),
  createdBy: Joi.string().uuid().optional(),
  approvedBy: Joi.string().uuid().optional(),
  dateFrom: Joi.date().iso().optional(),
  dateTo: Joi.date().iso().min(Joi.ref('dateFrom')).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
});

/**
 * Recommendation Criteria Schema
 */
export const getRecommendationsQuerySchema = Joi.object({
  currentCondition: Joi.string().min(3).max(100).required().messages({
    'string.min': 'Current condition must be at least 3 characters long',
    'string.max': 'Current condition must not exceed 100 characters',
    'any.required': 'Current condition is required',
  }),
  difficultyPreference: Joi.number().integer().min(1).max(10).default(1).messages({
    'number.integer': 'Difficulty preference must be an integer',
    'number.min': 'Difficulty preference must be between 1 and 10',
    'number.max': 'Difficulty preference must be between 1 and 10',
  }),
  timeAvailability: Joi.number().integer().min(30).max(2520).default(180).messages({
    'number.integer': 'Time availability must be an integer (minutes per week)',
    'number.min': 'Time availability must be at least 30 minutes per week',
    'number.max': 'Time availability must not exceed 2520 minutes per week (42 hours)',
  }),
  previousPlans: Joi.string().optional(), // Comma-separated UUIDs
  assessmentResults: Joi.string().optional(), // JSON string
  userPreferences: Joi.string().optional(), // JSON string
});

/**
 * UUID Parameter Schema
 */
export const uuidParamSchema = Joi.object({
  id: Joi.string().uuid().required().messages({
    'string.uuid': 'ID must be a valid UUID',
    'any.required': 'ID is required',
  }),
});

/**
 * User ID Parameter Schema
 */
export const userIdParamSchema = Joi.object({
  userId: Joi.string().uuid().required().messages({
    'string.uuid': 'User ID must be a valid UUID',
    'any.required': 'User ID is required',
  }),
});
