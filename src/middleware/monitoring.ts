import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

// Performance monitoring middleware
export const performanceMonitoring = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const startHrTime = process.hrtime();

  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const endTime = Date.now();
    const diff = process.hrtime(startHrTime);
    const responseTime = diff[0] * 1000 + diff[1] * 1e-6; // Convert to milliseconds

    // Log performance metrics
    logger.info('API Performance', {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: Math.round(responseTime * 100) / 100, // Round to 2 decimal places
      contentLength: res.get('content-length') || 0,
      userAgent: req.get('user-agent'),
      ip: req.ip,
      userId: req.user?.sub || 'anonymous',
      timestamp: new Date().toISOString(),
    });

    // Log slow requests (> 1 second)
    if (responseTime > 1000) {
      logger.warn('Slow API Request', {
        method: req.method,
        url: req.originalUrl,
        responseTime,
        userId: req.user?.sub || 'anonymous',
      });
    }

    // Log errors
    if (res.statusCode >= 400) {
      logger.error('API Error Response', {
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        responseTime,
        userId: req.user?.sub || 'anonymous',
      });
    }

    return originalEnd.call(this, chunk, encoding);
  };

  next();
};

// Request logging middleware
export const requestLogging = (req: Request, res: Response, next: NextFunction) => {
  logger.info('API Request', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('user-agent'),
    contentType: req.get('content-type'),
    contentLength: req.get('content-length'),
    userId: req.user?.sub || 'anonymous',
    timestamp: new Date().toISOString(),
  });

  next();
};

// Error tracking middleware
export const errorTracking = (err: any, req: Request, res: Response, next: NextFunction) => {
  logger.error('Unhandled API Error', {
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
    },
    user: {
      id: req.user?.sub || 'anonymous',
      ip: req.ip,
      userAgent: req.get('user-agent'),
    },
    timestamp: new Date().toISOString(),
  });

  next(err);
};

// Health metrics collection
interface HealthMetrics {
  requests: {
    total: number;
    success: number;
    error: number;
    lastMinute: number[];
  };
  responseTime: {
    average: number;
    p95: number;
    p99: number;
    samples: number[];
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  uptime: number;
}

class MetricsCollector {
  private metrics: HealthMetrics = {
    requests: {
      total: 0,
      success: 0,
      error: 0,
      lastMinute: [],
    },
    responseTime: {
      average: 0,
      p95: 0,
      p99: 0,
      samples: [],
    },
    memory: {
      used: 0,
      total: 0,
      percentage: 0,
    },
    uptime: 0,
  };

  private startTime = Date.now();

  recordRequest(statusCode: number, responseTime: number) {
    this.metrics.requests.total++;

    if (statusCode >= 200 && statusCode < 400) {
      this.metrics.requests.success++;
    } else {
      this.metrics.requests.error++;
    }

    // Track requests in the last minute
    const now = Date.now();
    this.metrics.requests.lastMinute.push(now);
    this.metrics.requests.lastMinute = this.metrics.requests.lastMinute.filter(
      time => now - time < 60000 // Keep only last minute
    );

    // Track response times
    this.metrics.responseTime.samples.push(responseTime);
    if (this.metrics.responseTime.samples.length > 1000) {
      this.metrics.responseTime.samples = this.metrics.responseTime.samples.slice(-1000);
    }

    this.updateResponseTimeMetrics();
    this.updateMemoryMetrics();
    this.updateUptime();
  }

  private updateResponseTimeMetrics() {
    const samples = this.metrics.responseTime.samples;
    if (samples.length === 0) return;

    const sorted = [...samples].sort((a, b) => a - b);
    this.metrics.responseTime.average = samples.reduce((a, b) => a + b, 0) / samples.length;
    this.metrics.responseTime.p95 = sorted[Math.floor(sorted.length * 0.95)];
    this.metrics.responseTime.p99 = sorted[Math.floor(sorted.length * 0.99)];
  }

  private updateMemoryMetrics() {
    const memUsage = process.memoryUsage();
    this.metrics.memory.used = Math.round(memUsage.heapUsed / 1024 / 1024); // MB
    this.metrics.memory.total = Math.round(memUsage.heapTotal / 1024 / 1024); // MB
    this.metrics.memory.percentage = (this.metrics.memory.used / this.metrics.memory.total) * 100;
  }

  private updateUptime() {
    this.metrics.uptime = Math.round((Date.now() - this.startTime) / 1000); // seconds
  }

  getMetrics(): HealthMetrics {
    this.updateMemoryMetrics();
    this.updateUptime();
    return { ...this.metrics };
  }

  reset() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        error: 0,
        lastMinute: [],
      },
      responseTime: {
        average: 0,
        p95: 0,
        p99: 0,
        samples: [],
      },
      memory: {
        used: 0,
        total: 0,
        percentage: 0,
      },
      uptime: 0,
    };
    this.startTime = Date.now();
  }
}

export const metricsCollector = new MetricsCollector();

// Metrics collection middleware
export const metricsCollection = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const responseTime = Date.now() - startTime;
    metricsCollector.recordRequest(res.statusCode, responseTime);
    return originalEnd.call(this, chunk, encoding);
  };

  next();
};

// Rate limiting tracking
interface RateLimitInfo {
  ip: string;
  requests: number[];
  blocked: boolean;
}

class RateLimiter {
  private clients = new Map<string, RateLimitInfo>();
  private readonly maxRequests = 100; // requests per minute
  private readonly windowMs = 60000; // 1 minute

  checkRateLimit(ip: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    let client = this.clients.get(ip);

    if (!client) {
      client = { ip, requests: [], blocked: false };
      this.clients.set(ip, client);
    }

    // Remove old requests outside the window
    client.requests = client.requests.filter(time => now - time < this.windowMs);

    const remaining = this.maxRequests - client.requests.length;
    const resetTime = now + this.windowMs;

    if (client.requests.length >= this.maxRequests) {
      client.blocked = true;
      return { allowed: false, remaining: 0, resetTime };
    }

    client.requests.push(now);
    client.blocked = false;
    return { allowed: true, remaining: remaining - 1, resetTime };
  }

  getClientInfo(ip: string): RateLimitInfo | undefined {
    return this.clients.get(ip);
  }

  cleanup() {
    const now = Date.now();
    for (const [ip, client] of this.clients.entries()) {
      client.requests = client.requests.filter(time => now - time < this.windowMs);
      if (client.requests.length === 0) {
        this.clients.delete(ip);
      }
    }
  }
}

export const rateLimiter = new RateLimiter();

// Rate limiting middleware
export const rateLimiting = (req: Request, res: Response, next: NextFunction) => {
  const ip = req.ip;
  const { allowed, remaining, resetTime } = rateLimiter.checkRateLimit(ip);

  res.set({
    'X-RateLimit-Limit': '100',
    'X-RateLimit-Remaining': remaining.toString(),
    'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString(),
  });

  if (!allowed) {
    logger.warn('Rate limit exceeded', { ip, url: req.originalUrl });
    return res.status(429).json({
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: '请求频率过高，请稍后重试',
      },
      timestamp: new Date().toISOString(),
    });
  }

  next();
};

// Cleanup interval for rate limiter
setInterval(() => {
  rateLimiter.cleanup();
}, 60000); // Clean up every minute
