import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { jwtService, JwtPayload } from '@/utils/jwt';
import { unauthorized, forbidden } from '@/middleware/errorHandler';
import { logger, logSecurity } from '@/utils/logger';

const prisma = new PrismaClient();

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        sub: string; // Add sub field for JWT compatibility
        username: string;
        email?: string | undefined;
        isActive: boolean;
        roles?: string[];
      };
    }
  }
}

/**
 * Authentication middleware
 * Verifies JWT token and attaches user to request
 */
export const authenticate = async (req: Request, _res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = jwtService.extractTokenFromHeader(authHeader);

    if (!token) {
      logSecurity('Missing authentication token', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
      });
      throw unauthorized('Authentication token required');
    }

    // Verify token
    let payload: JwtPayload;
    try {
      payload = jwtService.verifyAccessToken(token);
    } catch (error) {
      logSecurity('Invalid authentication token', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        error: (error as Error).message,
      });
      throw unauthorized('Invalid or expired token');
    }

    // Check if user exists and is active
    const user = await prisma.user.findUnique({
      where: { id: payload.sub },
      select: {
        id: true,
        username: true,
        email: true,
        isActive: true,
      },
    });

    if (!user) {
      logSecurity('Token for non-existent user', {
        userId: payload.sub,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      throw unauthorized('User not found');
    }

    if (!user.isActive) {
      logSecurity('Token for inactive user', {
        userId: user.id,
        username: user.username,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      throw unauthorized('Account is inactive');
    }

    // Attach user to request
    req.user = {
      id: user.id,
      sub: user.id, // Add sub field for compatibility with BaseController.getUserId()
      username: user.username,
      email: user.email ?? undefined,
      isActive: user.isActive,
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional authentication middleware
 * Attaches user to request if token is provided, but doesn't require it
 */
export const optionalAuth = async (req: Request, _res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = jwtService.extractTokenFromHeader(authHeader);

    if (!token) {
      return next(); // No token provided, continue without user
    }

    // Try to verify token
    try {
      const payload = jwtService.verifyAccessToken(token);

      const user = await prisma.user.findUnique({
        where: { id: payload.sub },
        select: {
          id: true,
          username: true,
          email: true,
          isActive: true,
        },
      });

      if (user && user.isActive) {
        req.user = {
          id: user.id,
          sub: user.id, // Add sub field for compatibility with BaseController.getUserId()
          username: user.username,
          email: user.email ?? undefined,
          isActive: user.isActive,
        };
      }
    } catch (error) {
      // Invalid token, but we don't throw error in optional auth
      logger.debug('Optional auth failed:', error);
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Role-based authorization middleware factory
 */
export const authorize = (requiredRoles: string[]) => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw unauthorized('Authentication required');
    }

    const userRoles = req.user.roles || [];
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      logSecurity('Insufficient permissions', {
        userId: req.user.id,
        username: req.user.username,
        requiredRoles,
        userRoles,
        path: req.path,
        ip: req.ip,
      });
      throw forbidden('Insufficient permissions');
    }

    next();
  };
};

/**
 * Owner authorization middleware
 * Checks if the authenticated user is the owner of the resource
 */
export const authorizeOwner = (userIdParam: string = 'userId') => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw unauthorized('Authentication required');
    }

    const resourceUserId = req.params[userIdParam] || req.body[userIdParam];

    if (!resourceUserId) {
      throw forbidden('Resource owner not specified');
    }

    if (req.user.id !== resourceUserId) {
      logSecurity('Unauthorized resource access attempt', {
        userId: req.user.id,
        username: req.user.username,
        resourceUserId,
        path: req.path,
        ip: req.ip,
      });
      throw forbidden('Access denied: not resource owner');
    }

    next();
  };
};

/**
 * Rate limiting by user
 */
export const userRateLimit = (maxRequests: number, windowMs: number) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(); // Skip rate limiting for unauthenticated requests
    }

    const userId = req.user.id;
    const now = Date.now();
    const userLimit = userRequests.get(userId);

    if (!userLimit || now > userLimit.resetTime) {
      // Reset or initialize user limit
      userRequests.set(userId, {
        count: 1,
        resetTime: now + windowMs,
      });
      return next();
    }

    if (userLimit.count >= maxRequests) {
      logSecurity('User rate limit exceeded', {
        userId,
        username: req.user.username,
        count: userLimit.count,
        maxRequests,
        ip: req.ip,
      });

      res.status(429).json({
        success: false,
        error: {
          code: 'USER_RATE_LIMIT_EXCEEDED',
          message: 'Too many requests from this user',
          retryAfter: Math.ceil((userLimit.resetTime - now) / 1000),
        },
        timestamp: new Date().toISOString(),
      });
      return;
    }

    userLimit.count++;
    next();
  };
};

/**
 * Admin only middleware
 */
export const adminOnly = authorize(['admin']);

/**
 * Doctor only middleware
 */
export const doctorOnly = authorize(['doctor', 'admin']);

/**
 * Moderator or admin middleware
 */
export const moderatorOnly = authorize(['moderator', 'admin']);
