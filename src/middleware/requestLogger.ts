import { Request, Response, NextFunction } from 'express';
import { logRequest } from '@/utils/logger';

export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();

  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function (chunk?: any, encoding?: any): any {
    const responseTime = Date.now() - startTime;
    logRequest(req, res, responseTime);
    originalEnd.call(this, chunk, encoding);
  };

  next();
};
