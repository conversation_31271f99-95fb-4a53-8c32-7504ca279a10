import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { logger } from '@/utils/logger';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

export class CustomError extends Error implements ApiError {
  public statusCode: number;
  public code: string;
  public details?: any;

  constructor(message: string, statusCode: number = 500, code?: string, details?: any) {
    super(message);
    this.name = 'CustomError';
    this.statusCode = statusCode;
    this.code = code || 'INTERNAL_ERROR';
    this.details = details;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, CustomError);
  }
}

export const errorHandler = (error: ApiError, req: Request, res: Response, _next: NextFunction): void => {
  let statusCode = error.statusCode || 500;
  let message = error.message || 'Internal server error';
  let code = error.code || 'INTERNAL_ERROR';
  let details = error.details;

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
    case 'P2002':
      statusCode = 409;
      code = 'DUPLICATE_ENTRY';
      message = 'A record with this information already exists';
      details = {
        field: error.meta?.['target'],
        constraint: error.meta?.['constraint'],
      };
      break;
    case 'P2025':
      statusCode = 404;
      code = 'RECORD_NOT_FOUND';
      message = 'The requested record was not found';
      break;
    case 'P2003':
      statusCode = 400;
      code = 'FOREIGN_KEY_CONSTRAINT';
      message = 'Foreign key constraint failed';
      break;
    case 'P2014':
      statusCode = 400;
      code = 'INVALID_RELATION';
      message = 'The change you are trying to make would violate the required relation';
      break;
    default:
      statusCode = 500;
      code = 'DATABASE_ERROR';
      message = 'Database operation failed';
    }
  }

  // Handle Prisma validation errors
  if (error instanceof Prisma.PrismaClientValidationError) {
    statusCode = 400;
    code = 'VALIDATION_ERROR';
    message = 'Invalid data provided';
    details = { validation: error.message };
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    code = 'INVALID_TOKEN';
    message = 'Invalid authentication token';
  }

  if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    code = 'TOKEN_EXPIRED';
    message = 'Authentication token has expired';
  }

  // Handle Joi validation errors
  if (error.name === 'ValidationError') {
    statusCode = 400;
    code = 'VALIDATION_ERROR';
    message = 'Request validation failed';
    details = {
      validation: error.details || error.message,
    };
  }

  // Handle multer errors (file upload)
  if (error.name === 'MulterError') {
    statusCode = 400;
    code = 'FILE_UPLOAD_ERROR';

    switch ((error as any).code) {
    case 'LIMIT_FILE_SIZE':
      message = 'File size too large';
      break;
    case 'LIMIT_FILE_COUNT':
      message = 'Too many files';
      break;
    case 'LIMIT_UNEXPECTED_FILE':
      message = 'Unexpected file field';
      break;
    default:
      message = 'File upload failed';
    }
  }

  // Log error
  const errorLog = {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code,
      statusCode,
      details,
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: (req as any).user?.id,
    },
  };

  if (statusCode >= 500) {
    logger.error('Server Error', errorLog);
  } else if (statusCode >= 400) {
    logger.warn('Client Error', errorLog);
  }

  // Send error response
  const errorResponse = {
    success: false,
    error: {
      code,
      message,
      ...(details && { details }),
      ...(process.env['NODE_ENV'] === 'development' && {
        stack: error.stack,
        originalError: error.name,
      }),
    },
    timestamp: new Date().toISOString(),
  };

  res.status(statusCode).json(errorResponse);
};

// Helper functions for creating common errors
export const createError = (message: string, statusCode: number = 500, code?: string, details?: any): CustomError => {
  return new CustomError(message, statusCode, code, details);
};

export const badRequest = (message: string, details?: any): CustomError => {
  return createError(message, 400, 'BAD_REQUEST', details);
};

export const unauthorized = (message: string = 'Unauthorized'): CustomError => {
  return createError(message, 401, 'UNAUTHORIZED');
};

export const forbidden = (message: string = 'Forbidden'): CustomError => {
  return createError(message, 403, 'FORBIDDEN');
};

export const notFound = (message: string = 'Resource not found'): CustomError => {
  return createError(message, 404, 'NOT_FOUND');
};

export const conflict = (message: string, details?: any): CustomError => {
  return createError(message, 409, 'CONFLICT', details);
};

export const unprocessableEntity = (message: string, details?: any): CustomError => {
  return createError(message, 422, 'UNPROCESSABLE_ENTITY', details);
};

export const tooManyRequests = (message: string = 'Too many requests'): CustomError => {
  return createError(message, 429, 'TOO_MANY_REQUESTS');
};

export const internalServerError = (message: string = 'Internal server error'): CustomError => {
  return createError(message, 500, 'INTERNAL_ERROR');
};
