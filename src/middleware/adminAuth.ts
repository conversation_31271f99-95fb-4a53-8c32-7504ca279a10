import { Request, Response, NextFunction } from 'express';
import { jwtService, JwtPayload } from '@/utils/jwt';
import { logger } from '@/utils/logger';
import { unauthorized, forbidden } from '@/middleware/errorHandler';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Extend Express Request interface to include admin
declare global {
  namespace Express {
    interface Request {
      admin?: {
        id: string;
        sub: string;
        username: string;
        email?: string;
        role: string;
        permissions: string[];
        isActive: boolean;
      };
    }
  }
}

/**
 * Log security events for admin operations
 */
const logAdminSecurity = (message: string, details: Record<string, any>) => {
  logger.warn(`[ADMIN_SECURITY] ${message}`, {
    ...details,
    timestamp: new Date().toISOString(),
  });
};

/**
 * Admin authentication middleware
 * Verifies JWT token and attaches admin to request
 */
export const authenticateAdmin = async (req: Request, _res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = jwtService.extractTokenFromHeader(authHeader);

    if (!token) {
      logAdminSecurity('Missing admin authentication token', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
      });
      throw unauthorized('Admin authentication token required');
    }

    // Verify token
    let payload: JwtPayload;
    try {
      payload = jwtService.verifyAccessToken(token);
    } catch (error) {
      logAdminSecurity('Invalid admin authentication token', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        error: (error as Error).message,
      });
      throw unauthorized('Invalid or expired admin token');
    }

    // Check if this is an admin token
    if (payload.type !== 'admin') {
      logAdminSecurity('Non-admin token used for admin endpoint', {
        tokenType: payload.type,
        userId: payload.sub,
        ip: req.ip,
        path: req.path,
      });
      throw unauthorized('Admin access required');
    }

    // Check if admin exists and is active
    const admin = await prisma.adminUser.findUnique({
      where: { id: payload.sub },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        permissions: true,
        isActive: true,
      },
    });

    if (!admin) {
      logAdminSecurity('Token for non-existent admin', {
        adminId: payload.sub,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      throw unauthorized('Admin not found');
    }

    if (!admin.isActive) {
      logAdminSecurity('Token for inactive admin', {
        adminId: admin.id,
        username: admin.username,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      throw unauthorized('Admin account is inactive');
    }

    // Attach admin to request
    req.admin = {
      id: admin.id,
      sub: admin.id,
      username: admin.username,
      email: admin.email ?? undefined,
      role: admin.role,
      permissions: admin.permissions as string[] || [],
      isActive: admin.isActive,
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional admin authentication middleware
 * Similar to authenticateAdmin but doesn't throw error if no token
 */
export const optionalAdminAuth = async (req: Request, _res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = jwtService.extractTokenFromHeader(authHeader);

    if (!token) {
      next();
      return;
    }

    // Try to verify token
    try {
      const payload = jwtService.verifyAccessToken(token);

      // Only process if it's an admin token
      if (payload.type === 'admin') {
        const admin = await prisma.adminUser.findUnique({
          where: { id: payload.sub },
          select: {
            id: true,
            username: true,
            email: true,
            role: true,
            permissions: true,
            isActive: true,
          },
        });

        if (admin && admin.isActive) {
          req.admin = {
            id: admin.id,
            sub: admin.id,
            username: admin.username,
            email: admin.email ?? undefined,
            role: admin.role,
            permissions: admin.permissions as string[] || [],
            isActive: admin.isActive,
          };
        }
      }
    } catch (error) {
      // Invalid token, but we don't throw error in optional auth
      logger.debug('Optional admin auth failed:', error);
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Admin role-based authorization middleware factory
 */
export const authorizeAdmin = (requiredRoles: string[]) => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    if (!req.admin) {
      throw unauthorized('Admin authentication required');
    }

    const hasRequiredRole = requiredRoles.includes(req.admin.role);

    if (!hasRequiredRole) {
      logAdminSecurity('Insufficient admin role', {
        adminId: req.admin.id,
        username: req.admin.username,
        currentRole: req.admin.role,
        requiredRoles,
        path: req.path,
        ip: req.ip,
      });
      throw forbidden('Insufficient admin role');
    }

    next();
  };
};

/**
 * Admin permission-based authorization middleware factory
 */
export const requireAdminPermission = (requiredPermissions: string[]) => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    if (!req.admin) {
      throw unauthorized('Admin authentication required');
    }

    const hasRequiredPermission = requiredPermissions.some(permission =>
      req.admin!.permissions.includes(permission)
    );

    if (!hasRequiredPermission) {
      logAdminSecurity('Insufficient admin permissions', {
        adminId: req.admin.id,
        username: req.admin.username,
        currentPermissions: req.admin.permissions,
        requiredPermissions,
        path: req.path,
        ip: req.ip,
      });
      throw forbidden('Insufficient admin permissions');
    }

    next();
  };
};

/**
 * Super admin only middleware
 */
export const superAdminOnly = authorizeAdmin(['super_admin']);

/**
 * Admin or super admin middleware
 */
export const adminOrSuperAdmin = authorizeAdmin(['admin', 'super_admin']);

/**
 * Editor, admin, or super admin middleware
 */
export const editorOrAbove = authorizeAdmin(['editor', 'admin', 'super_admin']);

/**
 * Write permission middleware
 */
export const requireWritePermission = requireAdminPermission(['write']);

/**
 * Delete permission middleware
 */
export const requireDeletePermission = requireAdminPermission(['delete']);

/**
 * Manage users permission middleware
 */
export const requireManageUsersPermission = requireAdminPermission(['manage_users']);

/**
 * Admin rate limiting middleware
 * More lenient than user rate limiting but still provides protection
 */
const adminRateLimits = new Map<string, { count: number; resetTime: number }>();

export const adminRateLimit = (maxRequests: number = 1000, windowMs: number = 60000) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.admin) {
      next();
      return;
    }

    const adminId = req.admin.id;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up old entries
    for (const [key, limit] of adminRateLimits.entries()) {
      if (limit.resetTime < windowStart) {
        adminRateLimits.delete(key);
      }
    }

    // Get or create admin limit
    let adminLimit = adminRateLimits.get(adminId);
    if (!adminLimit || adminLimit.resetTime < windowStart) {
      adminLimit = { count: 0, resetTime: now + windowMs };
      adminRateLimits.set(adminId, adminLimit);
    }

    if (adminLimit.count >= maxRequests) {
      logAdminSecurity('Admin rate limit exceeded', {
        adminId,
        username: req.admin.username,
        count: adminLimit.count,
        maxRequests,
        ip: req.ip,
      });

      res.status(429).json({
        success: false,
        error: {
          code: 'ADMIN_RATE_LIMIT_EXCEEDED',
          message: 'Too many requests from this admin',
          retryAfter: Math.ceil((adminLimit.resetTime - now) / 1000),
        },
        timestamp: new Date().toISOString(),
      });
      return;
    }

    adminLimit.count++;
    next();
  };
};
