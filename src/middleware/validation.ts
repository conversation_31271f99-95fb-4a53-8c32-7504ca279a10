/**
 * Validation Middleware
 * Provides request validation using Joi schemas
 */

import { Request, Response, NextFunction } from 'express';
import Jo<PERSON> from 'joi';
import { logger } from '@/utils/logger';

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  data?: any;
}

/**
 * Validate request data against a Joi schema
 */
export function validateRequest(
  schema: Joi.ObjectSchema,
  source: 'body' | 'query' | 'params' = 'body'
) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const dataToValidate = req[source];

      const { error, value } = schema.validate(dataToValidate, {
        abortEarly: false,
        allowUnknown: false,
        stripUnknown: true,
      });

      if (error) {
        const validationErrors: ValidationError[] = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value,
        }));

        logger.warn('Request validation failed', {
          source,
          errors: validationErrors,
          originalData: dataToValidate,
        });

        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Request validation failed',
          details: validationErrors,
          timestamp: new Date().toISOString(),
        });
      }

      // Replace the original data with validated and sanitized data
      req[source] = value;
      next();
    } catch (err) {
      logger.error('Validation middleware error', { error: err, source });
      return res.status(500).json({
        success: false,
        error: 'VALIDATION_MIDDLEWARE_ERROR',
        message: 'Internal validation error',
        timestamp: new Date().toISOString(),
      });
    }
  };
}

/**
 * Validate multiple sources in a single middleware
 */
export function validateMultiple(validations: Array<{
  schema: Joi.ObjectSchema;
  source: 'body' | 'query' | 'params';
}>) {
  return (req: Request, res: Response, next: NextFunction) => {
    const allErrors: ValidationError[] = [];
    const validatedData: Record<string, any> = {};

    for (const validation of validations) {
      const { schema, source } = validation;
      const dataToValidate = req[source];

      const { error, value } = schema.validate(dataToValidate, {
        abortEarly: false,
        allowUnknown: false,
        stripUnknown: true,
      });

      if (error) {
        const validationErrors: ValidationError[] = error.details.map(detail => ({
          field: `${source}.${detail.path.join('.')}`,
          message: detail.message,
          value: detail.context?.value,
        }));
        allErrors.push(...validationErrors);
      } else {
        validatedData[source] = value;
      }
    }

    if (allErrors.length > 0) {
      logger.warn('Multiple validation failed', {
        errors: allErrors,
        validations: validations.map(v => v.source),
      });

      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Request validation failed',
        details: allErrors,
        timestamp: new Date().toISOString(),
      });
    }

    // Replace original data with validated data
    for (const [source, data] of Object.entries(validatedData)) {
      req[source as keyof Request] = data;
    }

    next();
  };
}

/**
 * Validate and sanitize data without middleware (for use in services)
 */
export function validateData<T>(
  data: any,
  schema: Joi.ObjectSchema
): ValidationResult {
  try {
    const { error, value } = schema.validate(data, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors: ValidationError[] = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      return {
        isValid: false,
        errors: validationErrors,
      };
    }

    return {
      isValid: true,
      errors: [],
      data: value,
    };
  } catch (err) {
    logger.error('Data validation error', { error: err, data });
    return {
      isValid: false,
      errors: [{
        field: 'unknown',
        message: 'Validation processing error',
      }],
    };
  }
}

/**
 * Create a validation middleware for array data
 */
export function validateArray(
  itemSchema: Joi.ObjectSchema,
  source: 'body' | 'query' | 'params' = 'body',
  options: {
    minItems?: number;
    maxItems?: number;
    unique?: boolean;
  } = {}
) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const dataToValidate = req[source];

      if (!Array.isArray(dataToValidate)) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: `${source} must be an array`,
          timestamp: new Date().toISOString(),
        });
      }

      // Check array length constraints
      if (options.minItems && dataToValidate.length < options.minItems) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: `${source} must contain at least ${options.minItems} items`,
          timestamp: new Date().toISOString(),
        });
      }

      if (options.maxItems && dataToValidate.length > options.maxItems) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: `${source} must contain at most ${options.maxItems} items`,
          timestamp: new Date().toISOString(),
        });
      }

      // Validate each item in the array
      const validatedItems = [];
      const allErrors: ValidationError[] = [];

      for (let i = 0; i < dataToValidate.length; i++) {
        const item = dataToValidate[i];
        const { error, value } = itemSchema.validate(item, {
          abortEarly: false,
          allowUnknown: false,
          stripUnknown: true,
        });

        if (error) {
          const itemErrors: ValidationError[] = error.details.map(detail => ({
            field: `${source}[${i}].${detail.path.join('.')}`,
            message: detail.message,
            value: detail.context?.value,
          }));
          allErrors.push(...itemErrors);
        } else {
          validatedItems.push(value);
        }
      }

      if (allErrors.length > 0) {
        logger.warn('Array validation failed', {
          source,
          errors: allErrors,
          itemCount: dataToValidate.length,
        });

        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Array validation failed',
          details: allErrors,
          timestamp: new Date().toISOString(),
        });
      }

      // Check uniqueness if required
      if (options.unique) {
        const uniqueItems = Array.from(new Set(validatedItems.map(item => JSON.stringify(item))));
        if (uniqueItems.length !== validatedItems.length) {
          return res.status(400).json({
            success: false,
            error: 'VALIDATION_ERROR',
            message: `${source} must contain unique items`,
            timestamp: new Date().toISOString(),
          });
        }
      }

      // Replace with validated data
      req[source] = validatedItems;
      next();
    } catch (err) {
      logger.error('Array validation middleware error', { error: err, source });
      return res.status(500).json({
        success: false,
        error: 'VALIDATION_MIDDLEWARE_ERROR',
        message: 'Internal validation error',
        timestamp: new Date().toISOString(),
      });
    }
  };
}

/**
 * Common validation schemas
 */
export const commonSchemas = {
  uuid: Joi.string().uuid().required(),
  optionalUuid: Joi.string().uuid().optional(),
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
  }),
  dateRange: Joi.object({
    dateFrom: Joi.date().iso().optional(),
    dateTo: Joi.date().iso().min(Joi.ref('dateFrom')).optional(),
  }),
};
