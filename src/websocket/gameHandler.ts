import { Server as SocketIOServer } from 'socket.io';
import { logger } from '@/utils/logger';

export const setupGameWebSocket = (io: SocketIOServer): void => {
  const gameNamespace = io.of('/game-events');

  gameNamespace.on('connection', socket => {
    logger.info(`Game WebSocket connected: ${socket.id}`);

    socket.on('fruit-picked', data => {
      logger.debug('Received fruit picked event:', data);
      // TODO: Implement game event processing
      socket.emit('picking-result', {
        success: true,
        score: 100,
        message: 'Game event processing will be implemented in the next phase',
      });
    });

    socket.on('game-start', data => {
      logger.debug('Game started:', data);
      // TODO: Implement game session management
    });

    socket.on('disconnect', () => {
      logger.info(`Game WebSocket disconnected: ${socket.id}`);
    });
  });

  logger.info('Game WebSocket namespace initialized');
};
