import { Router } from 'express';
import { AdminAuthController } from '@/controllers/admin/AdminAuthController';
import { validateRequest } from '@/middleware/validation';
import { authenticateAdmin } from '@/middleware/adminAuth';
import Joi from 'joi';

const router = Router();
const adminAuthController = new AdminAuthController();

const loginSchema = Joi.object({
  username: Joi.string().required().messages({
    'any.required': 'Username is required',
    'string.empty': 'Username cannot be empty',
  }),
  password: Joi.string().required().messages({
    'any.required': 'Password is required',
    'string.empty': 'Password cannot be empty',
  }),
});

const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required().messages({
    'any.required': 'Refresh token is required',
  }),
});

router.post(
  '/login',
  validateRequest(loginSchema),
  adminAuthController.login
);

router.post(
  '/refresh',
  validateRequest(refreshTokenSchema),
  adminAuthController.refreshToken
);

router.post(
  '/logout',
  authenticateAdmin, // Requires admin to be logged in to log out
  adminAuthController.logout
);

export default router; 