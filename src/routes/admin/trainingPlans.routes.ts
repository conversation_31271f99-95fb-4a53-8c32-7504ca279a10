import { Router } from 'express';
import { AdminTrainingPlanController } from '@/controllers/admin/AdminTrainingPlanController';
import { authenticateAdmin, requireAdminPermission } from '@/middleware/adminAuth';
import { validateRequest } from '@/middleware/validation';
import Joi from 'joi';

const router = Router();
const controller = new AdminTrainingPlanController();

// Nested schema for an action point
const actionPointSchema = Joi.object({
  order: Joi.number().integer().min(1).required(),
  description: Joi.string().required(),
});

// Validation Schemas
const createPlanSchema = Joi.object({
  name: Joi.string().required(),
  description: Joi.string().optional().allow(null, ''),
  difficultyLevel: Joi.string().valid('beginner', 'intermediate', 'advanced').optional(),
  durationMinutes: Joi.number().integer().min(1).optional(),
  videoId: Joi.string().uuid().optional().allow(null),
  actionPoints: Joi.array().items(actionPointSchema).optional(),
});

const updatePlanSchema = Joi.object({
  name: Jo<PERSON>.string().optional(),
  description: Joi.string().optional().allow(null, ''),
  difficultyLevel: Joi.string().valid('beginner', 'intermediate', 'advanced').optional(),
  durationMinutes: Joi.number().integer().min(1).optional(),
  isActive: Joi.boolean().optional(),
  videoId: Joi.string().uuid().optional().allow(null),
  actionPoints: Joi.array().items(actionPointSchema).optional(),
});

const commonIdParamSchema = Joi.object({
  id: Joi.string().uuid().required(),
});

// Apply auth middleware to all routes
router.use(authenticateAdmin);

// Define routes
router.post(
  '/',
  requireAdminPermission(['write']),
  validateRequest(createPlanSchema),
  controller.create
);

router.get(
  '/',
  requireAdminPermission(['read']),
  controller.getAll
);

router.get(
  '/:id',
  requireAdminPermission(['read']),
  validateRequest(commonIdParamSchema, 'params'),
  controller.getById
);

router.put(
  '/:id',
  requireAdminPermission(['write']),
  validateRequest(commonIdParamSchema, 'params'),
  validateRequest(updatePlanSchema),
  controller.update
);

router.delete(
  '/:id',
  requireAdminPermission(['delete']),
  validateRequest(commonIdParamSchema, 'params'),
  controller.delete
);

export default router; 