import { Router } from 'express';
import { AdminVideoController, videoUploadMiddleware } from '@/controllers/admin/AdminVideoController';
import { authenticateAdmin, requireAdminPermission } from '@/middleware/adminAuth';
import { validateRequest } from '@/middleware/validation';
import Jo<PERSON> from 'joi';

const router = Router();
const videoController = new AdminVideoController();

// Validation schemas
const videoIdParamSchema = Joi.object({
  id: Joi.string().uuid().required(),
});

// Apply auth middleware to all routes
router.use(authenticateAdmin);

// GET /api/v1/admin/videos - Get videos with pagination and filtering
router.get(
  '/',
  requireAdminPermission(['read']),
  videoController.getVideos
);

// GET /api/v1/admin/videos/:id - Get video by ID
router.get(
  '/:id',
  requireAdminPermission(['read']),
  validateRequest(videoIdParamSchema, 'params'),
  videoController.getVideoById
);

// POST /api/v1/admin/videos/upload - Upload video
router.post(
  '/upload',
  requireAdminPermission(['write']),
  videoUploadMiddleware,
  videoController.uploadVideo
);

// DELETE /api/v1/admin/videos/:id - Delete video
router.delete(
  '/:id',
  requireAdminPermission(['delete']),
  validateRequest(videoIdParamSchema, 'params'),
  videoController.deleteVideo
);

export default router;