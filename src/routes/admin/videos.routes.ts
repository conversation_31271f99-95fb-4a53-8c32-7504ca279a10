import { Router } from 'express';
import { AdminVideoController, videoUploadMiddleware } from '@/controllers/admin/AdminVideoController';
import { authenticateAdmin } from '@/middleware/adminAuth';

const router = Router();
const videoController = new AdminVideoController();

// POST /api/v1/admin/videos/upload
router.post(
  '/upload',
  authenticateAdmin,
  videoUploadMiddleware,
  videoController.uploadVideo
);

export default router; 