import { Router } from 'express';
import { authController } from '@/controllers/AuthController';
import { validate, authSchemas } from '@/utils/validation';
import { authenticate } from '@/middleware/auth';

const router = Router();

/**
 * POST /auth/login-password
 * User login with username and password
 */
router.post('/login-password', validate(authSchemas.login), authController.loginWithPassword);

/**
 * POST /auth/login-phone
 * User login with phone number and SMS code
 */
router.post('/login-phone', validate(authSchemas.loginWithPhone), authController.loginWithPhone);

/**
 * POST /auth/register-password
 * User registration with password
 */
router.post('/register-password', validate(authSchemas.register), authController.registerWithPassword);

/**
 * POST /auth/register-phone
 * User registration with phone number
 */
router.post('/register-phone', validate(authSchemas.registerWithPhone), authController.registerWithPhone);

/**
 * POST /auth/send-sms
 * Send SMS verification code
 */
router.post('/send-sms', validate(authSchemas.sendSms), authController.sendSmsCode);

/**
 * POST /auth/verify-sms
 * Verify SMS code
 */
router.post('/verify-sms', validate(authSchemas.verifySms), authController.verifySmsCode);

/**
 * POST /auth/refresh
 * Refresh access token using refresh token
 */
router.post('/refresh', authenticate, authController.refreshToken);

/**
 * POST /auth/logout
 * User logout (invalidate tokens)
 */
router.post('/logout', authenticate, authController.logout);

/**
 * GET /auth/profile
 * Get current user profile
 */
router.get('/profile', authenticate, authController.getProfile);

/**
 * PUT /auth/password
 * Update user password
 */
router.put('/password', authenticate, validate(authSchemas.updatePassword), authController.updatePassword);

/**
 * POST /auth/reset-password
 * Reset password using SMS code
 */
router.post('/reset-password', validate(authSchemas.resetPasswordRequest), authController.resetPassword);

export default router;
