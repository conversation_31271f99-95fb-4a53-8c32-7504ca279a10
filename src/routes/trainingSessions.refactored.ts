/**
 * Training Sessions Routes (Refactored)
 * Implements the API endpoints specified in the API documentation
 */

import { Router } from 'express';
import { authenticate } from '@/middleware/auth';
import { validate } from '@/utils/validation';
import { trainingSessionController } from '@/controllers/TrainingSessionController';
import Joi from 'joi';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation schemas based on API specification
const createSessionSchema = Joi.object({
  userId: Joi.string().uuid().required(),
  trainingPlanId: Joi.string().uuid().optional(),
  trainingPlanName: Joi.string().min(1).max(255).required(),
  trainingType: Joi.string().valid('rehabilitation', 'game', 'assessment').required(),
});

const uploadSessionDataSchema = Joi.object({
  endTime: Joi.date().iso().required(),
  duration: Joi.number().integer().min(0).required(), // seconds
  averageGripStrength: Joi.number().min(0).max(10).required(),
  maxGripStrength: Joi.number().min(0).max(10).required(),
  averageAccuracy: Joi.number().min(0).max(1).required(),
  totalScore: Joi.number().integer().min(0).required(),
  completedActions: Joi.number().integer().min(0).required(),
  dataPoints: Joi.array().items(
    Joi.object({
      timestamp: Joi.date().iso().required(),
      gripStrength: Joi.number().min(0).max(10).required(),
      actionAccuracy: Joi.number().min(0).max(1).required(),
      targetGripStrength: Joi.number().min(0).max(10).optional(),
      targetAccuracy: Joi.number().min(0).max(1).optional(),
      score: Joi.number().integer().min(0).optional(),
    })
  ).optional(),
});

const updateGoalSchema = Joi.object({
  targetDuration: Joi.number().integer().min(1).max(480).required(), // 1-480 minutes
  targetActions: Joi.number().integer().min(1).max(100).required(),
  targetAccuracy: Joi.number().min(0).max(1).required(),
});

const recordsQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  date: Joi.date().iso().optional(),
});

const realtimeDataQuerySchema = Joi.object({
  limit: Joi.number().integer().min(1).max(1000).default(100),
  offset: Joi.number().integer().min(0).default(0),
});

const uuidParamSchema = Joi.object({
  sessionId: Joi.string().uuid().required(),
  userId: Joi.string().uuid().required(),
});

const userIdParamSchema = Joi.object({
  userId: Joi.string().uuid().required(),
});

/**
 * POST /training/sessions
 * Create a new training session
 */
router.post('/sessions',
  validate(createSessionSchema),
  trainingSessionController.createSession
);

/**
 * PUT /training/sessions/:sessionId
 * Upload training session data and complete session
 */
router.put('/sessions/:sessionId',
  validate(uuidParamSchema, 'params'),
  validate(uploadSessionDataSchema),
  trainingSessionController.uploadSessionData
);

/**
 * GET /training/sessions/:sessionId
 * Get training session details
 */
router.get('/sessions/:sessionId',
  validate(uuidParamSchema, 'params'),
  trainingSessionController.getSessionDetails
);

/**
 * DELETE /training/sessions/:sessionId
 * Cancel/delete a training session
 */
router.delete('/sessions/:sessionId',
  validate(uuidParamSchema, 'params'),
  trainingSessionController.cancelSession
);

/**
 * GET /training/sessions/:sessionId/realtime-data
 * Get real-time data for a training session
 */
router.get('/sessions/:sessionId/realtime-data',
  validate(uuidParamSchema, 'params'),
  validate(realtimeDataQuerySchema, 'query'),
  trainingSessionController.getSessionRealTimeData
);

/**
 * GET /training/today/:userId
 * Get today's training data for user
 */
router.get('/today/:userId',
  validate(userIdParamSchema, 'params'),
  trainingSessionController.getTodayTrainingData
);

/**
 * GET /training/weekly/:userId
 * Get weekly training data for user
 */
router.get('/weekly/:userId',
  validate(userIdParamSchema, 'params'),
  trainingSessionController.getWeeklyTrainingData
);

/**
 * GET /training/records/:userId
 * Get user's training records with pagination
 */
router.get('/records/:userId',
  validate(userIdParamSchema, 'params'),
  validate(recordsQuerySchema, 'query'),
  trainingSessionController.getUserTrainingRecords
);

/**
 * GET /training/goals/:userId/today
 * Get today's training goal for user
 */
router.get('/goals/:userId/today',
  validate(userIdParamSchema, 'params'),
  trainingSessionController.getTodayGoal
);

/**
 * PUT /training/goals/:userId/today
 * Update today's training goal for user
 */
router.put('/goals/:userId/today',
  validate(userIdParamSchema, 'params'),
  validate(updateGoalSchema),
  trainingSessionController.updateTodayGoal
);

export default router;
