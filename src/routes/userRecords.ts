import { Router, Request, Response, NextFunction } from 'express';
import { authenticate, authorizeOwner } from '@/middleware/auth';
import { validateUuidParam, validate } from '@/utils/validation';
import { logger } from '@/utils/logger';
import Joi from 'joi';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation schemas
const recordQuerySchema = Joi.object({
  type: Joi.string().valid('training', 'game', 'assessment', 'all').default('all'),
  limit: Joi.number().integer().min(1).max(100).default(20),
  offset: Joi.number().integer().min(0).default(0),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
});

/**
 * GET /users/:userId/records
 * Get user's activity records
 */
router.get('/:userId/records',
  validateUuidParam('userId'),
  authorizeOwner(),
  validate(recordQuerySchema, 'query'),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.params['userId'] as string;
      const { type, limit, offset, startDate, endDate } = req.query as any;

      // TODO: Implement actual database queries for user records
      const allRecords: any[] = [];

      // Filter by type if specified
      let filteredRecords = allRecords;
      if (type && type !== 'all') {
        filteredRecords = allRecords.filter(record => record.type === type);
      }

      // Filter by date range if specified
      if (startDate || endDate) {
        filteredRecords = filteredRecords.filter(record => {
          const recordDate = new Date(record.completedAt);
          if (startDate && recordDate < new Date(startDate)) return false;
          if (endDate && recordDate > new Date(endDate)) return false;
          return true;
        });
      }

      // Apply pagination
      const totalRecords = filteredRecords.length;
      const paginatedRecords = filteredRecords.slice(offset, offset + limit);

      // Calculate statistics
      const statistics = {
        total: totalRecords,
        byType: {
          training: allRecords.filter(r => r.type === 'training').length,
          game: allRecords.filter(r => r.type === 'game').length,
          assessment: allRecords.filter(r => r.type === 'assessment').length,
        },
        averageScore: Math.round(allRecords.reduce((sum, r) => sum + r.score, 0) / allRecords.length),
        totalDuration: allRecords.reduce((sum, r) => sum + r.duration, 0),
        recentActivity: allRecords[0]?.completedAt || null,
      };

      logger.info('获取用户记录成功', {
        userId,
        type,
        totalRecords,
        returnedRecords: paginatedRecords.length,
      });

      res.json({
        success: true,
        data: {
          records: paginatedRecords,
          statistics,
          pagination: {
            limit,
            offset,
            total: totalRecords,
            hasMore: offset + limit < totalRecords,
          },
        },
        message: '用户记录获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取用户记录失败', { error, userId: req.params['userId'] });
      next(error);
    }
  }
);

/**
 * GET /users/:userId/records/summary
 * Get user's records summary
 */
router.get('/:userId/records/summary',
  validateUuidParam('userId'),
  authorizeOwner(),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.params['userId'] as string;

      // Mock summary data
      const summary = {
        totalRecords: 25,
        thisWeek: 5,
        thisMonth: 18,
        streakDays: 7,
        longestStreak: 14,
        averageDaily: 1.2,
        favoriteActivity: 'strength_training',
        totalTime: 12600, // seconds
        achievements: [
          {
            id: 'week_warrior',
            name: '周训练达人',
            description: '连续一周完成训练',
            unlockedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: 'score_master',
            name: '高分达人',
            description: '单次训练得分超过90分',
            unlockedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          },
        ],
        recentTrends: {
          scoreImprovement: 15.2,
          durationIncrease: 8.5,
          consistencyRate: 0.85,
        },
      };

      logger.info('获取用户记录摘要成功', { userId });

      res.json({
        success: true,
        data: summary,
        message: '用户记录摘要获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取用户记录摘要失败', { error, userId: req.params['userId'] });
      next(error);
    }
  }
);

/**
 * GET /users/:userId/records/:recordId
 * Get specific record details
 */
router.get('/:userId/records/:recordId',
  validateUuidParam('userId'),
  authorizeOwner(),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.params['userId'] as string;
      const recordId = req.params['recordId'] as string;

      // Mock detailed record data
      const record = {
        id: recordId,
        userId,
        type: 'training',
        title: '上肢力量训练详情',
        description: '完成了30分钟的上肢力量训练，包含多个动作组合',
        score: 85,
        duration: 1800,
        difficulty: 'medium',
        completedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        detailedMetadata: {
          exerciseType: 'strength',
          targetMuscles: ['biceps', 'triceps', 'shoulders'],
          exercises: [
            {
              name: '哑铃弯举',
              sets: 3,
              reps: 15,
              weight: 5,
              restTime: 60,
              score: 88,
            },
            {
              name: '肩部推举',
              sets: 3,
              reps: 12,
              weight: 3,
              restTime: 60,
              score: 82,
            },
          ],
          heartRate: {
            average: 125,
            max: 145,
            zones: {
              warmup: 180,
              active: 1200,
              recovery: 420,
            },
          },
          calories: 180,
          feedback: {
            form: 'good',
            effort: 'high',
            notes: '动作标准，建议下次增加重量',
          },
        },
      };

      logger.info('获取用户记录详情成功', { userId, recordId });

      res.json({
        success: true,
        data: record,
        message: '用户记录详情获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      const { recordId } = req.params;
      logger.error('获取用户记录详情失败', { error, userId: req.params['userId'], recordId });
      next(error);
    }
  }
);

export default router;
