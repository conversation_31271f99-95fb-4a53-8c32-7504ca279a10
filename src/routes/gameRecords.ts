import { Router, Request, Response, NextFunction } from 'express';
import { authenticate } from '@/middleware/auth';
import { validate } from '@/utils/validation';
import { logger } from '@/utils/logger';
import { badRequest } from '@/middleware/errorHandler';
import Joi from 'joi';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation schemas
const gameRecordSchema = Joi.object({
  gameType: Joi.string().valid(
    'balance_challenge',
    'reaction_training',
    'memory_match',
    'coordination_test',
    'strength_training'
  ).required().messages({
    'any.only': '游戏类型必须是有效的游戏类型',
    'any.required': '游戏类型是必填项',
  }),
  score: Joi.number().integer().min(0).required().messages({
    'number.min': '分数不能为负数',
    'any.required': '分数是必填项',
  }),
  accuracy: Joi.number().min(0).max(1).optional().messages({
    'number.min': '准确率必须在0-1之间',
    'number.max': '准确率必须在0-1之间',
  }),
  duration: Joi.number().integer().min(1).required().messages({
    'number.min': '游戏时长必须大于0',
    'any.required': '游戏时长是必填项',
  }),
  metadata: Joi.object().optional(),
  difficulty: Joi.string().valid('easy', 'medium', 'hard').optional(),
});

// Helper functions
const updateGameStatistics = async (userId: string, gameType: string, score: number, duration: number) => {
  // In a real app, this would update the database
  // For now, we'll just log the update
  logger.info('更新游戏统计', {
    userId,
    gameType,
    score,
    duration,
  });

  return {
    userId,
    gameType,
    totalSessions: 1, // Would be incremented in real DB
    bestScore: score,
    averageScore: score,
    totalPlaytime: duration,
    lastPlayed: new Date().toISOString(),
  };
};

/**
 * POST /games/records
 * Create a new game record
 */
router.post('/records',
  validate(gameRecordSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const { gameType, score, accuracy, duration, metadata, difficulty } = req.body;

      // Create game record
      const record = {
        id: `game_record_${Date.now()}`,
        userId,
        gameType,
        score,
        accuracy: accuracy || null,
        duration,
        difficulty: difficulty || 'medium',
        metadata: metadata || {},
        sessionDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      };

      // Update statistics
      const statistics = await updateGameStatistics(userId, gameType, score, duration);

      logger.info('游戏记录创建成功', {
        userId,
        recordId: record.id,
        gameType,
        score,
        duration,
      });

      res.status(201).json({
        success: true,
        data: {
          record,
          statistics,
        },
        message: '游戏记录保存成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('游戏记录创建失败', { error, userId: req.user?.id });
      next(error);
    }
  }
);

/**
 * GET /games/records
 * Get user's game records with pagination
 */
router.get('/records',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const limit = parseInt(req.query['limit'] as string) || 20;
      const offset = parseInt(req.query['offset'] as string) || 0;
      const gameType = req.query['gameType'] as string;

      // TODO: Implement actual database queries for game records
      const records: any[] = [];

      logger.info('获取游戏记录', { userId, count: records.length, gameType });

      res.json({
        success: true,
        data: {
          records,
          pagination: {
            limit,
            offset,
            total: 50, // Mock total
            hasMore: offset + limit < 50,
          },
        },
        message: '获取游戏记录成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取游戏记录失败', { error, userId: req.user?.id });
      next(error);
    }
  }
);

/**
 * GET /games/statistics
 * Get user's game statistics
 */
router.get('/statistics',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const gameType = req.query['gameType'] as string;

      // Mock statistics
      const allStats = [
        {
          userId,
          gameType: 'balance_challenge',
          totalSessions: 25,
          bestScore: 1580,
          averageScore: 1125,
          totalPlaytime: 4500, // seconds
          lastPlayed: new Date(Date.now() - 86400000).toISOString(),
          improvementRate: 12.5,
        },
        {
          userId,
          gameType: 'reaction_training',
          totalSessions: 18,
          bestScore: 950,
          averageScore: 780,
          totalPlaytime: 2160,
          lastPlayed: new Date(Date.now() - 172800000).toISOString(),
          improvementRate: 8.3,
        },
        {
          userId,
          gameType: 'memory_match',
          totalSessions: 12,
          bestScore: 2400,
          averageScore: 1850,
          totalPlaytime: 3600,
          lastPlayed: new Date(Date.now() - 259200000).toISOString(),
          improvementRate: 15.2,
        },
      ];

      const statistics = gameType
        ? allStats.filter(stat => stat.gameType === gameType)
        : allStats;

      logger.info('获取游戏统计', { userId, gameType, count: statistics.length });

      res.json({
        success: true,
        data: {
          statistics,
          summary: {
            totalGames: statistics.reduce((sum, stat) => sum + stat.totalSessions, 0),
            totalPlaytime: statistics.reduce((sum, stat) => sum + stat.totalPlaytime, 0),
            averageImprovement: statistics.reduce((sum, stat) => sum + stat.improvementRate, 0) / statistics.length,
          },
        },
        message: '获取游戏统计成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取游戏统计失败', { error, userId: req.user?.id });
      next(error);
    }
  }
);

/**
 * GET /games/leaderboard
 * Get game leaderboard
 */
router.get('/leaderboard',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const gameType = req.query['gameType'] as string || 'balance_challenge';
      const limit = parseInt(req.query['limit'] as string) || 10;

      // TODO: Implement actual database queries for leaderboard
      const leaderboard: any[] = [];

      logger.info('获取游戏排行榜', { gameType, limit });

      res.json({
        success: true,
        data: {
          leaderboard,
          gameType,
          currentUserRank: 3, // Mock current user rank
        },
        message: '获取排行榜成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取游戏排行榜失败', { error });
      next(error);
    }
  }
);

export default router;
