/**
 * Health Check Routes (Refactored)
 * Provides comprehensive system health monitoring endpoints
 */

import { Router } from 'express';
import { healthController } from '@/controllers/HealthController';

const router = Router();

/**
 * GET /health
 * Basic health check endpoint
 */
router.get('/', healthController.basicHealthCheck);

/**
 * GET /health/detailed
 * Detailed health check including all services
 */
router.get('/detailed', healthController.detailedHealthCheck);

/**
 * GET /health/database
 * Database-specific health check
 */
router.get('/database', healthController.databaseHealthCheck);

/**
 * GET /health/cache
 * Cache-specific health check
 */
router.get('/cache', healthController.cacheHealthCheck);

/**
 * GET /health/stats
 * System statistics and metrics
 */
router.get('/stats', healthController.getSystemStats);

export default router;
