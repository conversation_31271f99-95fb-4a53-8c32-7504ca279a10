import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';
import { metricsCollector } from '@/middleware/monitoring';
import * as os from 'os';
import * as fs from 'fs/promises';

const router = Router();
const prisma = new PrismaClient();

// System check functions
const checkDiskSpace = async (): Promise<{ status: string; details?: any }> => {
  try {
    const stats = await fs.stat('.');
    const freeSpace = os.freemem();
    const totalSpace = os.totalmem();
    const usedSpace = totalSpace - freeSpace;
    const usagePercentage = (usedSpace / totalSpace) * 100;

    return {
      status: usagePercentage < 90 ? 'healthy' : 'warning',
      details: {
        freeSpace: Math.round(freeSpace / 1024 / 1024), // MB
        totalSpace: Math.round(totalSpace / 1024 / 1024), // MB
        usagePercentage: Math.round(usagePercentage * 100) / 100,
      },
    };
  } catch (error) {
    return { status: 'unhealthy', details: { error: (error as Error).message } };
  }
};

const checkMemory = (): { status: string; details: any } => {
  const freeMemory = os.freemem();
  const totalMemory = os.totalmem();
  const usedMemory = totalMemory - freeMemory;
  const usagePercentage = (usedMemory / totalMemory) * 100;

  return {
    status: usagePercentage < 85 ? 'healthy' : 'warning',
    details: {
      freeMemory: Math.round(freeMemory / 1024 / 1024), // MB
      totalMemory: Math.round(totalMemory / 1024 / 1024), // MB
      usagePercentage: Math.round(usagePercentage * 100) / 100,
      loadAverage: os.loadavg(),
    },
  };
};

const checkDatabase = async (): Promise<{ status: string; details?: any }> => {
  try {
    const start = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - start;

    return {
      status: responseTime < 1000 ? 'healthy' : 'warning',
      details: {
        responseTime,
        connected: true,
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        connected: false,
        error: (error as Error).message,
      },
    };
  }
};

// Basic health check
router.get('/', async (_req: Request, res: Response) => {
  try {
    const checks = await Promise.allSettled([
      checkDatabase(),
      checkDiskSpace(),
      Promise.resolve(checkMemory()),
    ]);

    const [dbCheck, diskCheck, memoryCheck] = checks;

    const isHealthy = checks.every(check =>
      check.status === 'fulfilled' &&
      check.value.status !== 'unhealthy'
    );

    const healthCheck = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      version: process.env.API_VERSION || '1.0.0',
      timestamp: new Date().toISOString(),
      uptime: Math.round(process.uptime()),
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: dbCheck.status === 'fulfilled' ? dbCheck.value.status : 'unhealthy',
        disk: diskCheck.status === 'fulfilled' ? diskCheck.value.status : 'unhealthy',
        memory: memoryCheck.status === 'fulfilled' ? memoryCheck.value.status : 'unhealthy',
      },
      details: {
        database: dbCheck.status === 'fulfilled' ? dbCheck.value.details : { error: 'Check failed' },
        disk: diskCheck.status === 'fulfilled' ? diskCheck.value.details : { error: 'Check failed' },
        memory: memoryCheck.status === 'fulfilled' ? memoryCheck.value.details : { error: 'Check failed' },
      },
    };

    const statusCode = isHealthy ? 200 : 503;

    res.status(statusCode).json({
      success: isHealthy,
      data: healthCheck,
      message: isHealthy ? 'Health check passed' : 'Health check failed',
      timestamp: new Date().toISOString(),
    });

    logger.info('Health check completed', {
      status: healthCheck.status,
      services: healthCheck.services,
    });

  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      success: false,
      data: {
        status: 'unhealthy',
        version: process.env.API_VERSION || '1.0.0',
        timestamp: new Date().toISOString(),
        services: {
          database: 'unknown',
          disk: 'unknown',
          memory: 'unknown',
        },
      },
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Health check failed',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

// Readiness check (includes database connectivity)
router.get('/ready', async (_req: Request, res: Response) => {
  try {
    // Check database connectivity
    await prisma.$queryRaw`SELECT 1`;

    const readinessCheck = {
      status: 'ready',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        server: 'running',
      },
    };

    res.status(200).json({
      success: true,
      data: readinessCheck,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Readiness check failed:', error);
    res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_NOT_READY',
        message: 'Service is not ready',
        details: {
          database: 'disconnected',
        },
      },
      timestamp: new Date().toISOString(),
    });
  }
});

// Liveness check
router.get('/live', (_req: Request, res: Response) => {
  res.status(200).json({
    success: true,
    data: {
      status: 'alive',
      timestamp: new Date().toISOString(),
    },
    timestamp: new Date().toISOString(),
  });
});

// Liveness check (simple check that the service is running)
router.get('/live', async (_req: Request, res: Response) => {
  try {
    const liveCheck = {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: Math.round(process.uptime()),
      pid: process.pid,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024), // MB
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024), // MB
      },
    };

    res.status(200).json({
      success: true,
      data: liveCheck,
      message: 'Service is alive',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    logger.error('Liveness check failed:', error);
    res.status(503).json({
      success: false,
      error: {
        code: 'LIVENESS_CHECK_FAILED',
        message: 'Liveness check failed',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

// Metrics endpoint
router.get('/metrics', async (_req: Request, res: Response) => {
  try {
    const metrics = metricsCollector.getMetrics();
    const systemMetrics = {
      cpu: {
        loadAverage: os.loadavg(),
        usage: process.cpuUsage(),
      },
      memory: {
        system: {
          free: Math.round(os.freemem() / 1024 / 1024), // MB
          total: Math.round(os.totalmem() / 1024 / 1024), // MB
          usage: Math.round((1 - os.freemem() / os.totalmem()) * 100), // percentage
        },
        process: {
          heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024), // MB
          external: Math.round(process.memoryUsage().external / 1024 / 1024), // MB
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024), // MB
        },
      },
      uptime: {
        process: Math.round(process.uptime()),
        system: Math.round(os.uptime()),
      },
      platform: {
        arch: os.arch(),
        platform: os.platform(),
        version: os.release(),
        nodeVersion: process.version,
      },
    };

    const combinedMetrics = {
      ...metrics,
      system: systemMetrics,
      timestamp: new Date().toISOString(),
    };

    res.json({
      success: true,
      data: combinedMetrics,
      message: '系统指标获取成功',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    logger.error('获取系统指标失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'METRICS_ERROR',
        message: '获取系统指标失败',
      },
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
