import { Router } from 'express';
import { authenticate } from '@/middleware/auth';
import { validate } from '@/utils/validation';
import { rehabilitationController } from '@/controllers/RehabilitationController';
import Joi from 'joi';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation schemas
const recordsQuerySchema = Joi.object({
  limit: Joi.number().integer().min(1).max(100).default(20),
  offset: Joi.number().integer().min(0).default(0),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  type: Joi.string().valid('strength', 'flexibility', 'balance', 'coordination', 'all').default('all'),
});

const plansQuerySchema = Joi.object({
  status: Joi.string().valid('active', 'completed', 'paused', 'all').default('active'),
  difficulty: Joi.string().valid('easy', 'medium', 'hard', 'all').default('all'),
});

const targetsQuerySchema = Joi.object({
  period: Joi.string().valid('daily', 'weekly', 'monthly', 'all').default('all'),
  status: Joi.string().valid('active', 'completed', 'overdue', 'all').default('active'),
});

const dailyRecordsQuerySchema = Joi.object({
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
});

/**
 * GET /training/rehabilitation/training-records/daily
 * 获取用户的每日康复训练记录（按日期分组）
 * 注意：这个路由必须在更通用的 training-records 路由之前
 */
router.get('/rehabilitation/training-records/daily',
  validate(dailyRecordsQuerySchema, 'query'),
  rehabilitationController.getDailyTrainingRecords
);

/**
 * GET /training/rehabilitation/training-records
 * 获取用户的康复训练记录
 */
router.get('/rehabilitation/training-records',
  validate(recordsQuerySchema, 'query'),
  rehabilitationController.getTrainingRecords
);

/**
 * GET /training/rehabilitation/training-plans
 * 获取用户的康复训练计划
 */
router.get('/rehabilitation/training-plans',
  validate(plansQuerySchema, 'query'),
  rehabilitationController.getTrainingPlans
);

/**
 * GET /training/rehabilitation/action-points
 * 获取康复训练动作要点
 */
router.get('/rehabilitation/action-points',
  rehabilitationController.getActionPoints
);

/**
 * GET /training/rehabilitation/training-targets
 * 获取用户的康复训练目标
 */
router.get('/rehabilitation/training-targets',
  validate(targetsQuerySchema, 'query'),
  rehabilitationController.getTrainingTargets
);

/**
 * DELETE /training/rehabilitation/user-data
 * 清空用户的所有训练数据（重置为新用户状态）
 */
router.delete('/rehabilitation/user-data',
  rehabilitationController.clearUserTrainingData
);

export default router;
