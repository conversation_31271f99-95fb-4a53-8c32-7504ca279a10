import { Router, Request, Response, NextFunction } from 'express';
import { userService } from '@/services/userService';
import { validate, userSchemas, validateUuidParam } from '@/utils/validation';
import { authenticate, authorizeOwner } from '@/middleware/auth';
import { notFound } from '@/middleware/errorHandler';

const router = Router();

// All user routes require authentication
router.use(authenticate);

/**
 * GET /users/profile
 * Get current user's profile
 */
router.get('/profile', async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new Error('User not found in request');
    }

    const user = await userService.getUserById(req.user.id);

    if (!user) {
      throw notFound('User not found');
    }

    res.json({
      success: true,
      data: user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /users/profile
 * Update current user's profile
 */
router.put('/profile', validate(userSchemas.updateProfile), async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new Error('User not found in request');
    }

    const updateData = req.body;
    const updatedUser = await userService.updateUser(req.user.id, updateData);

    res.json({
      success: true,
      data: updatedUser,
      message: 'Profile updated successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /users/:userId
 * Get user by ID (public profile view)
 */
router.get('/:userId', validateUuidParam('userId'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId } = req.params;
    if (!userId) {
      throw notFound('User ID is required');
    }
    const user = await userService.getUserById(userId);

    if (!user) {
      throw notFound('User not found');
    }

    // Return limited public profile information
    const publicProfile = {
      id: user.id,
      username: user.username,
      fullName: user.fullName,
      avatarUrl: user.avatarUrl,
      recoveryPhase: user.recoveryPhase,
      createdAt: user.createdAt,
    };

    res.json({
      success: true,
      data: publicProfile,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /users/:userId
 * Update user profile (owner only)
 */
router.put(
  '/:userId',
  validateUuidParam('userId'),
  authorizeOwner('userId'),
  validate(userSchemas.updateProfile),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { userId } = req.params;
      if (!userId) {
        throw notFound('User ID is required');
      }
      const updateData = req.body;

      const updatedUser = await userService.updateUser(userId, updateData);

      res.json({
        success: true,
        data: updatedUser,
        message: 'Profile updated successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  },
);

/**
 * GET /users/:userId/stats
 * Get user training statistics
 */
router.get('/:userId/stats', validateUuidParam('userId'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId: _userId } = req.params;

    // TODO: Implement user statistics calculation
    // For now, return mock data
    const stats = {
      totalSessions: 0,
      totalTrainingTime: 0,
      averageAccuracy: 0,
      currentStreak: 0,
      longestStreak: 0,
      recoveryProgress: 0,
      lastSessionDate: null,
      weeklyGoal: {
        target: 5,
        completed: 0,
        progress: 0,
      },
      monthlyStats: {
        sessions: 0,
        totalTime: 0,
        averageScore: 0,
      },
    };

    res.json({
      success: true,
      data: stats,
      message: 'User statistics will be implemented in the next phase',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /users/:userId/progress
 * Get user recovery progress
 */
router.get(
  '/:userId/progress',
  validateUuidParam('userId'),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { userId: _userId } = req.params;

      // TODO: Implement progress tracking
      // For now, return mock data
      const progress = {
        currentPhase: 'early_recovery',
        overallProgress: 0.25,
        milestones: [
          {
            id: '1',
            name: 'First Session Completed',
            description: 'Complete your first training session',
            completed: false,
            completedAt: null,
          },
          {
            id: '2',
            name: 'Week 1 Goal',
            description: 'Complete 5 training sessions in your first week',
            completed: false,
            completedAt: null,
          },
        ],
        weeklyProgress: {
          week: 1,
          sessionsCompleted: 0,
          sessionsTarget: 5,
          progress: 0,
        },
        strengthProgress: {
          baseline: 0,
          current: 0,
          target: 100,
          improvement: 0,
        },
      };

      res.json({
        success: true,
        data: progress,
        message: 'Progress tracking will be implemented in the next phase',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  },
);

export default router;
