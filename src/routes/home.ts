import { Router, Request, Response, NextFunction } from 'express';
import { authenticate, authorizeOwner } from '@/middleware/auth';
import { validateUuidParam } from '@/utils/validation';
import { userService } from '@/services/userService';

// 数据验证中间件 - 确保响应字段完整性
const validateHomeDataResponse = (req: Request, res: Response, next: NextFunction) => {
  const originalJson = res.json;

  res.json = function(data: any) {
    if (data.success && data.data) {
      // 确保用户信息完整
      data.data.user = data.data.user || {
        id: req.params.userId || 'unknown',
        username: '用户',
        nickname: '用户',
        avatar: null,
        level: 1,
        experience: 0,
        streak: 0,
      };

      // 确保训练数据完整
      data.data.training = data.data.training || {
        totalSessions: 0,
        totalDuration: 0,
        weeklyGoal: 5,
        weeklyProgress: 0,
        recentActivities: [],
        progress: {
          strength: 0,
          flexibility: 0,
          balance: 0,
          coordination: 0,
        },
      };

      // 确保游戏数据完整
      data.data.games = data.data.games || {
        totalGames: 0,
        bestScore: 0,
        recentGames: [],
        achievements: [],
      };

      // 确保摘要数据完整
      data.data.summary = data.data.summary || {
        todayTraining: 0,
        weeklyTraining: 0,
        monthlyTraining: 0,
        totalTraining: 0,
      };

      // 确保今日目标完整
      data.data.todayGoal = data.data.todayGoal || {
        target: 5,
        completed: 0,
        remaining: 5,
        progress: 0.0,
        type: 'sessions',
      };

      // 确保训练统计完整
      data.data.trainingStats = data.data.trainingStats || {
        totalSessions: 0,
        totalDuration: 0,
        averageScore: 0,
        bestScore: 0,
        currentStreak: 0,
        longestStreak: 0,
        improvementRate: 0.0,
        lastSession: new Date().toISOString(),
      };

      // 确保推荐数组
      data.data.recommendations = data.data.recommendations || [];

      // 确保通知数组
      data.data.notifications = data.data.notifications || [];
    }

    return originalJson.call(this, data);
  };

  next();
};
import { trainingService } from '@/services/trainingService';
import { gameService } from '@/services/gameService';
import { notFound } from '@/middleware/errorHandler';

const router = Router();

// All home routes require authentication
router.use(authenticate);



/**
 * GET /home/<USER>/:userId
 * Get comprehensive home page data for a user
 */
router.get('/data/:userId',
  validateUuidParam('userId'),
  validateHomeDataResponse,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.params['userId'] as string;

      // TODO: Implement actual user data retrieval
      const homeData = {
        user: {
          id: userId,
          username: '',
          nickname: '',
          avatar: null,
          level: 1,
          experience: 0,
          streak: 0,
        },
        training: {
          totalSessions: 45,
          totalDuration: 1350,
          weeklyGoal: 5,
          weeklyProgress: 3,
          recentActivities: [
            {
              id: 'activity1',
              type: 'strength',
              name: '上肢力量训练',
              duration: 30,
              difficulty: 'medium',
              completedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
              score: 85,
            },
          ],
          progress: {
            strength: 75,
            flexibility: 68,
            balance: 82,
            coordination: 71,
          },
        },
        games: {
          totalGames: 28,
          bestScore: 1580,
          recentGames: [
            {
              id: 'game1',
              name: '平衡挑战',
              type: 'balance',
              score: 1250,
              duration: 180,
              difficulty: 'medium',
              playedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            },
          ],
          achievements: [
            {
              id: 'ach1',
              name: '初学者',
              description: '完成第一个游戏',
              icon: 'trophy',
              unlockedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
              category: 'milestone',
            },
          ],
        },
        summary: {
          todayTraining: 1,
          weeklyTraining: 3,
          monthlyTraining: 12,
          totalTraining: 45,
        },
        todayGoal: {
          target: 5,
          completed: 1,
          remaining: 4,
          progress: 0.2,
          type: 'sessions',
        },
        trainingStats: {
          totalSessions: 45,
          totalDuration: 1350,
          averageScore: 83,
          bestScore: 95,
          currentStreak: 7,
          longestStreak: 14,
          improvementRate: 12.5,
          lastSession: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        },
        recommendations: [
          {
            type: 'training',
            title: '今日推荐训练',
            description: '基于您的训练历史推荐',
            action: 'start_training',
          },
          {
            type: 'game',
            title: '挑战新游戏',
            description: '提升您的协调能力',
            action: 'start_game',
          },
        ],
        notifications: [
          {
            id: 'welcome',
            type: 'info',
            title: '欢迎回来！',
            message: '继续您的康复训练之旅',
            timestamp: new Date().toISOString(),
          },
        ],
      };

      res.json({
        success: true,
        data: homeData,
        message: '首页数据获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * GET /home/<USER>/:userId
 * Get quick statistics for home page widgets
 */
router.get('/quick-stats/:userId',
  validateUuidParam('userId'),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.params['userId'] as string;

      // Get quick stats for the user
      const stats = {
        todayTraining: 0,
        weeklyGoal: 5,
        weeklyProgress: 3,
        streak: 7,
        totalSessions: 45,
        achievements: 12,
        userId, // Include userId to show it's being used
      };

      res.json({
        success: true,
        data: stats,
        message: '快速统计获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * GET /home/<USER>/:userId
 * Get personalized recommendations
 */
router.get('/recommendations/:userId',
  validateUuidParam('userId'),
  authorizeOwner,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.params['userId'] as string;

      // Get personalized recommendations for the user
      const recommendations = [
        {
          id: 'rec1',
          type: 'training',
          title: '上肢力量训练',
          description: `基于用户${userId}的康复进度推荐`,
          difficulty: 'medium',
          duration: 30,
          category: 'strength',
        },
        {
          id: 'rec2',
          type: 'game',
          title: '平衡挑战游戏',
          description: '提升平衡能力的趣味游戏',
          difficulty: 'easy',
          duration: 15,
          category: 'balance',
        },
      ];

      res.json({
        success: true,
        data: recommendations,
        message: '推荐内容获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      next(error);
    }
  }
);

export default router;
