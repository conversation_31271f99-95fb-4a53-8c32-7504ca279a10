import { Router, Request, Response, NextFunction } from 'express';
import { authenticate, authorizeOwner } from '@/middleware/auth';
import { validateUuidParam, validate } from '@/utils/validation';
import { logger } from '@/utils/logger';
import Joi from 'joi';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation schemas
const trainingSessionSchema = Joi.object({
  duration: Joi.number().integer().min(1).max(7200).required(), // 1 second to 2 hours
  completedActions: Joi.number().integer().min(0).default(0),
  accuracy: Joi.number().min(0).max(1).optional(),
  exerciseType: Joi.string().valid('strength', 'flexibility', 'balance', 'coordination', 'cardio').required(),
  difficulty: Joi.string().valid('easy', 'medium', 'hard').default('medium'),
  targetMuscles: Joi.array().items(Joi.string()).optional(),
  metadata: Joi.object().optional(),
});

const sessionQuerySchema = Joi.object({
  exerciseType: Joi.string().valid('strength', 'flexibility', 'balance', 'coordination', 'cardio', 'all').default('all'),
  limit: Joi.number().integer().min(1).max(100).default(20),
  offset: Joi.number().integer().min(0).default(0),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
});

/**
 * POST /training/sessions
 * Create a new training session
 */
router.post('/sessions',
  validate(trainingSessionSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.sub;
      const { duration, completedActions, accuracy, exerciseType, difficulty, targetMuscles, metadata } = req.body;

      // Generate session ID
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Calculate score based on duration, accuracy, and completed actions
      const baseScore = Math.min(duration / 60 * 10, 100); // 10 points per minute, max 100
      const accuracyBonus = accuracy ? accuracy * 50 : 0; // up to 50 bonus points
      const actionBonus = Math.min(completedActions * 2, 30); // 2 points per action, max 30
      const score = Math.round(baseScore + accuracyBonus + actionBonus);

      // Mock training session data
      const trainingSession = {
        id: sessionId,
        userId,
        duration,
        completedActions,
        accuracy: accuracy || null,
        exerciseType,
        difficulty,
        score,
        targetMuscles: targetMuscles || [],
        metadata: metadata || {},
        sessionDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      };

      logger.info('创建训练会话成功', {
        userId,
        sessionId,
        exerciseType,
        duration,
        score,
      });

      res.status(201).json({
        success: true,
        data: trainingSession,
        message: '训练会话创建成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('创建训练会话失败', { error, userId: req.user?.sub });
      next(error);
    }
  }
);

/**
 * GET /training/sessions
 * Get user's training sessions
 */
router.get('/sessions',
  validate(sessionQuerySchema, 'query'),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.sub;
      const { exerciseType, limit, offset, startDate, endDate } = req.query as any;

      // Mock training sessions data
      const allSessions = [
        {
          id: 'session_1',
          userId,
          duration: 1800, // 30 minutes
          completedActions: 15,
          accuracy: 0.85,
          exerciseType: 'strength',
          difficulty: 'medium',
          score: 92,
          targetMuscles: ['biceps', 'triceps', 'shoulders'],
          sessionDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          metadata: {
            sets: 3,
            reps: 15,
            weight: 5,
            restTime: 60,
          },
        },
        {
          id: 'session_2',
          userId,
          duration: 1500, // 25 minutes
          completedActions: 12,
          accuracy: 0.78,
          exerciseType: 'flexibility',
          difficulty: 'easy',
          score: 78,
          targetMuscles: ['spine', 'hips', 'shoulders'],
          sessionDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          metadata: {
            stretchCount: 12,
            holdTime: 30,
          },
        },
        {
          id: 'session_3',
          userId,
          duration: 2100, // 35 minutes
          completedActions: 18,
          accuracy: 0.92,
          exerciseType: 'balance',
          difficulty: 'hard',
          score: 105,
          targetMuscles: ['core', 'legs'],
          sessionDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          metadata: {
            balanceTime: 120,
            challenges: 8,
          },
        },
        {
          id: 'session_4',
          userId,
          duration: 1200, // 20 minutes
          completedActions: 10,
          accuracy: 0.88,
          exerciseType: 'coordination',
          difficulty: 'medium',
          score: 85,
          targetMuscles: ['full_body'],
          sessionDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
          metadata: {
            patterns: 6,
            complexity: 'medium',
          },
        },
        {
          id: 'session_5',
          userId,
          duration: 900, // 15 minutes
          completedActions: 8,
          accuracy: 0.75,
          exerciseType: 'cardio',
          difficulty: 'easy',
          score: 68,
          targetMuscles: ['heart', 'lungs'],
          sessionDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          metadata: {
            heartRate: {
              average: 120,
              max: 140,
            },
            calories: 85,
          },
        },
      ];

      // Filter by exercise type if specified
      let filteredSessions = allSessions;
      if (exerciseType && exerciseType !== 'all') {
        filteredSessions = allSessions.filter(session => session.exerciseType === exerciseType);
      }

      // Filter by date range if specified
      if (startDate || endDate) {
        filteredSessions = filteredSessions.filter(session => {
          const sessionDate = new Date(session.sessionDate);
          if (startDate && sessionDate < new Date(startDate)) return false;
          if (endDate && sessionDate > new Date(endDate)) return false;
          return true;
        });
      }

      // Apply pagination
      const totalSessions = filteredSessions.length;
      const paginatedSessions = filteredSessions.slice(offset, offset + limit);

      // Calculate statistics
      const statistics = {
        total: totalSessions,
        byType: {
          strength: allSessions.filter(s => s.exerciseType === 'strength').length,
          flexibility: allSessions.filter(s => s.exerciseType === 'flexibility').length,
          balance: allSessions.filter(s => s.exerciseType === 'balance').length,
          coordination: allSessions.filter(s => s.exerciseType === 'coordination').length,
          cardio: allSessions.filter(s => s.exerciseType === 'cardio').length,
        },
        averageScore: Math.round(allSessions.reduce((sum, s) => sum + s.score, 0) / allSessions.length),
        totalDuration: allSessions.reduce((sum, s) => sum + s.duration, 0),
        averageAccuracy: allSessions.reduce((sum, s) => sum + (s.accuracy || 0), 0) / allSessions.length,
        recentSession: allSessions[0]?.sessionDate || null,
      };

      logger.info('获取训练会话成功', {
        userId,
        exerciseType,
        totalSessions,
        returnedSessions: paginatedSessions.length,
      });

      res.json({
        success: true,
        data: {
          sessions: paginatedSessions,
          statistics,
          pagination: {
            limit,
            offset,
            total: totalSessions,
            hasMore: offset + limit < totalSessions,
          },
        },
        message: '训练会话获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取训练会话失败', { error, userId: req.user?.sub });
      next(error);
    }
  }
);

/**
 * GET /training/sessions/:sessionId
 * Get specific training session details
 */
router.get('/sessions/:sessionId',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.sub;
      const sessionId = req.params.sessionId;

      // Mock detailed session data
      const session = {
        id: sessionId,
        userId,
        duration: 1800,
        completedActions: 15,
        accuracy: 0.85,
        exerciseType: 'strength',
        difficulty: 'medium',
        score: 92,
        targetMuscles: ['biceps', 'triceps', 'shoulders'],
        sessionDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        detailedMetadata: {
          exercises: [
            {
              name: '哑铃弯举',
              sets: 3,
              reps: 15,
              weight: 5,
              restTime: 60,
              score: 88,
              form: 'good',
            },
            {
              name: '肩部推举',
              sets: 3,
              reps: 12,
              weight: 3,
              restTime: 60,
              score: 82,
              form: 'excellent',
            },
            {
              name: '三头肌伸展',
              sets: 3,
              reps: 10,
              weight: 2,
              restTime: 45,
              score: 90,
              form: 'good',
            },
          ],
          heartRate: {
            average: 125,
            max: 145,
            zones: {
              warmup: 180,
              active: 1200,
              recovery: 420,
            },
          },
          calories: 180,
          feedback: {
            form: 'good',
            effort: 'high',
            notes: '动作标准，建议下次增加重量',
          },
          environment: {
            temperature: 22,
            humidity: 45,
          },
        },
      };

      logger.info('获取训练会话详情成功', { userId, sessionId });

      res.json({
        success: true,
        data: session,
        message: '训练会话详情获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取训练会话详情失败', { error, userId: req.user?.sub, sessionId: req.params.sessionId });
      next(error);
    }
  }
);

/**
 * GET /training/statistics
 * Get user's training statistics
 */
router.get('/statistics',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.sub;

      // Mock training statistics
      const statistics = {
        overall: {
          totalSessions: 45,
          totalDuration: 67500, // in seconds
          averageScore: 83.5,
          bestScore: 105,
          averageAccuracy: 0.84,
          currentStreak: 7,
          longestStreak: 14,
          improvementRate: 12.5, // percentage
        },
        byType: {
          strength: {
            sessions: 18,
            duration: 32400,
            averageScore: 87.2,
            bestScore: 105,
            lastSession: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          },
          flexibility: {
            sessions: 12,
            duration: 18000,
            averageScore: 78.5,
            bestScore: 92,
            lastSession: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          },
          balance: {
            sessions: 8,
            duration: 10800,
            averageScore: 85.8,
            bestScore: 98,
            lastSession: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          },
          coordination: {
            sessions: 5,
            duration: 4500,
            averageScore: 82.0,
            bestScore: 95,
            lastSession: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
          },
          cardio: {
            sessions: 2,
            duration: 1800,
            averageScore: 70.5,
            bestScore: 75,
            lastSession: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          },
        },
        trends: {
          last7Days: [
            { date: '2025-07-18', sessions: 1, duration: 1800, score: 92 },
            { date: '2025-07-19', sessions: 1, duration: 1500, score: 78 },
            { date: '2025-07-20', sessions: 1, duration: 2100, score: 105 },
            { date: '2025-07-21', sessions: 1, duration: 1200, score: 85 },
            { date: '2025-07-22', sessions: 1, duration: 900, score: 68 },
            { date: '2025-07-23', sessions: 0, duration: 0, score: 0 },
            { date: '2025-07-24', sessions: 1, duration: 1800, score: 88 },
          ],
          monthlyProgress: {
            currentMonth: 18,
            lastMonth: 15,
            improvement: 20.0,
          },
        },
      };

      logger.info('获取训练统计成功', { userId });

      res.json({
        success: true,
        data: statistics,
        message: '训练统计获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取训练统计失败', { error, userId: req.user?.sub });
      next(error);
    }
  }
);

export default router;
