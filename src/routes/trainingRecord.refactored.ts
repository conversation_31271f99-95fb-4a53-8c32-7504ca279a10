/**
 * Training Record Routes (Refactored)
 * Provides comprehensive training data storage and querying endpoints
 */

import { Router } from 'express';
import { authenticate } from '@/middleware/auth';
import { trainingRecordController } from '@/controllers/TrainingRecordController';
import { validateRequest } from '@/middleware/validation';
import {
  userIdParamSchema,
  dailyRecordsQuerySchema,
  weeklyRecordsQuerySchema,
  monthlyRecordsQuerySchema,
  exportDataQuerySchema,
  userSummaryQuerySchema,
  cleanupOptionsSchema,
} from '@/utils/validation/trainingRecordValidation';

const router = Router();



// Apply authentication to all other routes
router.use(authenticate);

/**
 * GET /records/user/:userId/daily?date=YYYY-MM-DD
 * Get daily training records for a user
 */
router.get(
  '/records/user/:userId/daily',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(dailyRecordsQuerySchema, 'query'),
  trainingRecordController.getDailyRecords
);

/**
 * GET /records/user/:userId/weekly?week=YYYY-WW
 * Get weekly training records for a user
 */
router.get(
  '/records/user/:userId/weekly',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(weeklyRecordsQuerySchema, 'query'),
  trainingRecordController.getWeeklyRecords
);

/**
 * GET /records/user/:userId/monthly?month=YYYY-MM
 * Get monthly training records for a user
 */
router.get(
  '/records/user/:userId/monthly',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(monthlyRecordsQuerySchema, 'query'),
  trainingRecordController.getMonthlyRecords
);

/**
 * GET /records/user/:userId/summary?days=30
 * Get training data summary for a user
 */
router.get(
  '/records/user/:userId/summary',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(userSummaryQuerySchema, 'query'),
  trainingRecordController.getUserSummary
);

/**
 * GET /records/user/:userId/export?format=json&period=month&date=YYYY-MM
 * Export training data for a user
 */
router.get(
  '/records/user/:userId/export',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(exportDataQuerySchema, 'query'),
  trainingRecordController.exportUserData
);

/**
 * POST /records/user/:userId/cleanup
 * Clean up old training data for a user
 */
router.post(
  '/records/user/:userId/cleanup',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(cleanupOptionsSchema, 'body'),
  trainingRecordController.cleanupUserData
);

/**
 * POST /records/cleanup
 * Clean up old training data for all users (admin only)
 */
router.post(
  '/records/cleanup',
  validateRequest(cleanupOptionsSchema, 'body'),
  trainingRecordController.cleanupAllData
);

export default router;
