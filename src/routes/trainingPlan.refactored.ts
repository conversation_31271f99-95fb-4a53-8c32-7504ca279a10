/**
 * Training Plan Routes (Refactored)
 * Provides comprehensive training plan management and recommendation endpoints
 */

import { Router } from 'express';
import { trainingPlanController } from '@/controllers/TrainingPlanController';
import { validateRequest } from '@/middleware/validation';
import {
  createTrainingPlanSchema,
  updateTrainingPlanSchema,
  approveTrainingPlanSchema,
  completeTrainingPlanSchema,
  trackExecutionSchema,
  getUserTrainingPlansQuerySchema,
  getRecommendationsQuerySchema,
  uuidParamSchema,
  userIdParamSchema,
} from '@/utils/validation/trainingPlanValidation';

const router = Router();

/**
 * POST /training/plans
 * Create a new training plan
 */
router.post(
  '/plans',
  validateRequest(createTrainingPlanSchema, 'body'),
  trainingPlanController.createTrainingPlan
);

/**
 * GET /training/plans/:id
 * Get training plan by ID
 */
router.get(
  '/plans/:id',
  validateRequest(uuidParamSchema, 'params'),
  trainingPlanController.getTrainingPlanById
);

/**
 * PUT /training/plans/:id
 * Update training plan
 */
router.put(
  '/plans/:id',
  validateRequest(uuidParamSchema, 'params'),
  validateRequest(updateTrainingPlanSchema, 'body'),
  trainingPlanController.updateTrainingPlan
);

/**
 * DELETE /training/plans/:id
 * Delete training plan (soft delete)
 */
router.delete(
  '/plans/:id',
  validateRequest(uuidParamSchema, 'params'),
  trainingPlanController.deleteTrainingPlan
);

/**
 * POST /training/plans/:id/approve
 * Approve training plan
 */
router.post(
  '/plans/:id/approve',
  validateRequest(uuidParamSchema, 'params'),
  validateRequest(approveTrainingPlanSchema, 'body'),
  trainingPlanController.approveTrainingPlan
);

/**
 * POST /training/plans/:id/activate
 * Activate training plan
 */
router.post(
  '/plans/:id/activate',
  validateRequest(uuidParamSchema, 'params'),
  trainingPlanController.activateTrainingPlan
);

/**
 * POST /training/plans/:id/complete
 * Complete training plan
 */
router.post(
  '/plans/:id/complete',
  validateRequest(uuidParamSchema, 'params'),
  validateRequest(completeTrainingPlanSchema, 'body'),
  trainingPlanController.completeTrainingPlan
);

/**
 * POST /training/plans/:id/track
 * Track training plan execution
 */
router.post(
  '/plans/:id/track',
  validateRequest(uuidParamSchema, 'params'),
  validateRequest(trackExecutionSchema, 'body'),
  trainingPlanController.trackExecution
);

/**
 * GET /training/plans/:id/progress
 * Analyze training plan progress
 */
router.get(
  '/plans/:id/progress',
  validateRequest(uuidParamSchema, 'params'),
  trainingPlanController.analyzeProgress
);

/**
 * GET /training/plans/user/:userId
 * Get user training plans with pagination and filters
 */
router.get(
  '/plans/user/:userId',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(getUserTrainingPlansQuerySchema, 'query'),
  trainingPlanController.getUserTrainingPlans
);

/**
 * GET /training/plans/user/:userId/recommendations
 * Get personalized training plan recommendations
 */
router.get(
  '/plans/user/:userId/recommendations',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(getRecommendationsQuerySchema, 'query'),
  trainingPlanController.getPersonalizedRecommendations
);

/**
 * GET /training/plans/user/:userId/stats
 * Get user training plan statistics
 */
router.get(
  '/plans/user/:userId/stats',
  validateRequest(userIdParamSchema, 'params'),
  trainingPlanController.getUserTrainingPlanStats
);

export default router;
