import { Router } from 'express';
import { AppRehabilitationController } from '@/controllers/app/AppRehabilitationController';
import { authenticate } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';
import Jo<PERSON> from 'joi';

const router = Router();
const controller = new AppRehabilitationController();

// All app routes require user authentication
router.use(authenticate);

const idParamSchema = Joi.object({
  id: Joi.string().uuid().required(),
});

router.get(
  '/plans',
  controller.getPlansList
);

router.get(
  '/plans/:id',
  validateRequest(idParamSchema, 'params'),
  controller.getPlanDetails
);

export default router; 