import { Router, Request, Response, NextFunction } from 'express';
import { authController } from '@/controllers/AuthController';
import { validate, authSchemas } from '@/utils/validation';
import { authenticate } from '@/middleware/auth';
import { userService } from '@/services/userService';
import { smsService } from '@/services/smsService';

const router = Router();

/**
 * POST /auth/login-password
 * User login with username and password
 */
router.post('/login-password', validate(authSchemas.login), authController.loginWithPassword);

/**
 * POST /auth/login-phone
 * User login with phone number and SMS code
 */
router.post('/login-phone', validate(authSchemas.loginWithPhone), authController.loginWithPhone);

/**
 * POST /auth/register
 * User registration
 */
router.post('/register', validate(authSchemas.register), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userData = req.body;

    const result = await userService.createUser(userData);

    res.status(201).json({
      success: true,
      data: {
        user: result.user,
        accessToken: result.tokens.accessToken,
        refreshToken: result.tokens.refreshToken,
        expiresIn: result.tokens.expiresIn,
      },
      message: 'Registration successful',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /auth/refresh
 * Refresh access token
 */
router.post('/refresh', validate(authSchemas.refreshToken), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { refreshToken } = req.body;

    const tokens = await userService.refreshToken(refreshToken);

    res.json({
      success: true,
      data: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn,
      },
      message: 'Token refreshed successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /auth/me
 * Get current user information
 */
router.get('/me', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new Error('User not found in request');
    }

    const user = await userService.getUserById(req.user.id);

    if (!user) {
      throw new Error('User not found');
    }

    res.json({
      success: true,
      data: user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /auth/logout
 * Logout user (invalidate refresh tokens)
 */
router.post('/logout', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new Error('User not found in request');
    }

    await userService.logout(req.user.id);

    res.json({
      success: true,
      message: 'Logout successful',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /auth/send-sms
 * Send SMS verification code
 */
router.post('/send-sms', validate(authSchemas.sendSms), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { phoneNumber, purpose } = req.body; // Changed 'type' to 'purpose'
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');

    // For registration, check if phone number is already registered
    if (purpose === 'register') {
      const isRegistered = await smsService.isPhoneRegistered(phoneNumber);
      if (isRegistered) {
        res.status(409).json({
          success: false,
          error: {
            code: 'PHONE_ALREADY_REGISTERED',
            message: 'This phone number is already registered',
          },
          timestamp: new Date().toISOString(),
        });
        return;
      }
    }

    // For login, check if phone number is registered
    if (purpose === 'login') {
      const isRegistered = await smsService.isPhoneRegistered(phoneNumber);
      if (!isRegistered) {
        res.status(404).json({
          success: false,
          error: {
            code: 'PHONE_NOT_REGISTERED',
            message: 'This phone number is not registered',
          },
          timestamp: new Date().toISOString(),
        });
        return;
      }
    }

    // Send SMS verification code
    const result = await smsService.sendVerificationCode({
      phoneNumber,
      purpose, // Use 'purpose' directly
      ipAddress: ipAddress || undefined,
      userAgent: userAgent || undefined,
    });

    res.json({
      success: true,
      data: result,
      message: 'SMS verification code sent successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /auth/register-phone
 * User registration with phone number and SMS code
 */
router.post(
  '/register-phone',
  validate(authSchemas.registerWithPhone),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const phoneData = req.body;

      const result = await userService.registerWithPhone(phoneData);

      res.status(201).json({
        success: true,
        data: {
          user: result.user,
          accessToken: result.tokens.accessToken,
          refreshToken: result.tokens.refreshToken,
          expiresIn: result.tokens.expiresIn,
        },
        message: 'Registration successful',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  },
);

/**
 * POST /auth/login-password
 * User login with username/phone and password
 */
router.post('/login-password',
  validate(authSchemas.loginWithPassword),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const loginData = req.body;

      const result = await userService.loginWithPassword(loginData);

      res.json({
        success: true,
        data: {
          user: result.user,
          accessToken: result.tokens.accessToken,
          refreshToken: result.tokens.refreshToken,
          expiresIn: result.tokens.expiresIn,
        },
        message: 'Login successful',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * POST /auth/reset-password
 * Reset password using SMS verification
 */
router.post('/reset-password',
  validate(authSchemas.resetPasswordRequest),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const resetData = req.body;

      await userService.resetPassword(resetData);

      res.json({
        success: true,
        message: 'Password reset successful',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
