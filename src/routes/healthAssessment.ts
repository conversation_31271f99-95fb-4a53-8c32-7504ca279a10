import { Router, Request, Response, NextFunction } from 'express';
import { authenticate } from '@/middleware/auth';
import { validate } from '@/utils/validation';
import { logger } from '@/utils/logger';
import { badRequest, notFound } from '@/middleware/errorHandler';
import Joi from 'joi';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation schemas
const assessmentSchema = Joi.object({
  height: Joi.number().min(100).max(250).required().messages({
    'number.min': '身高必须在100-250cm之间',
    'number.max': '身高必须在100-250cm之间',
    'any.required': '身高是必填项',
  }),
  weight: Joi.number().min(30).max(200).required().messages({
    'number.min': '体重必须在30-200kg之间',
    'number.max': '体重必须在30-200kg之间',
    'any.required': '体重是必填项',
  }),
  painLevel: Joi.number().integer().min(0).max(10).required().messages({
    'number.min': '疼痛等级必须在0-10之间',
    'number.max': '疼痛等级必须在0-10之间',
    'any.required': '疼痛等级是必填项',
  }),
  mobilityLevel: Joi.number().integer().min(0).max(10).required().messages({
    'number.min': '活动能力等级必须在0-10之间',
    'number.max': '活动能力等级必须在0-10之间',
    'any.required': '活动能力等级是必填项',
  }),
  notes: Joi.string().max(500).optional(),
});

// Helper functions
const calculateBMI = (height: number, weight: number): number => {
  return Math.round((weight / ((height / 100) ** 2)) * 100) / 100;
};

const calculateRecoveryStage = (painLevel: number, mobilityLevel: number, bmi: number): string => {
  const score = (10 - painLevel) * 0.4 + mobilityLevel * 0.4 + (bmi >= 18.5 && bmi <= 24 ? 10 : 5) * 0.2;
  if (score >= 8) return 'advanced';
  if (score >= 5) return 'intermediate';
  return 'beginner';
};

const calculateRiskScore = (painLevel: number, mobilityLevel: number): number => {
  return Math.min((painLevel / 10) * 0.4 + ((10 - mobilityLevel) / 10) * 0.4, 1);
};

const generateRecommendations = (recoveryStage: string, painLevel: number, mobilityLevel: number): string[] => {
  const recommendations: string[] = [];

  if (painLevel > 7) {
    recommendations.push('建议咨询医生，疼痛等级较高');
    recommendations.push('进行轻度拉伸和呼吸练习');
  } else if (painLevel > 4) {
    recommendations.push('适度进行康复训练');
    recommendations.push('注意休息，避免过度运动');
  } else {
    recommendations.push('可以进行常规康复训练');
  }

  if (mobilityLevel < 4) {
    recommendations.push('重点提升基础活动能力');
    recommendations.push('从简单的关节活动开始');
  } else if (mobilityLevel < 7) {
    recommendations.push('逐步增加运动强度');
    recommendations.push('加强平衡和协调训练');
  } else {
    recommendations.push('可以进行高强度康复训练');
    recommendations.push('考虑增加功能性训练');
  }

  switch (recoveryStage) {
  case 'beginner':
    recommendations.push('建议每天进行15-20分钟的基础训练');
    break;
  case 'intermediate':
    recommendations.push('建议每天进行30-45分钟的中等强度训练');
    break;
  case 'advanced':
    recommendations.push('可以进行60分钟以上的高强度训练');
    break;
  }

  return recommendations;
};

/**
 * POST /health/assessment
 * Create a new health assessment
 */
router.post('/assessment',
  validate(assessmentSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const { height, weight, painLevel, mobilityLevel, notes } = req.body;

      // Calculate derived values
      const bmi = calculateBMI(height, weight);
      const recoveryStage = calculateRecoveryStage(painLevel, mobilityLevel, bmi);
      const riskScore = calculateRiskScore(painLevel, mobilityLevel);
      const recommendations = generateRecommendations(recoveryStage, painLevel, mobilityLevel);

      // Create assessment object (in a real app, this would be saved to database)
      const assessment = {
        id: `assessment_${Date.now()}`,
        userId,
        height,
        weight,
        painLevel,
        mobilityLevel,
        bmi,
        recoveryStage,
        riskScore,
        recommendations,
        notes,
        assessmentDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      };

      logger.info('健康评估创建成功', {
        userId,
        assessmentId: assessment.id,
        recoveryStage,
        riskScore,
      });

      res.status(201).json({
        success: true,
        data: assessment,
        message: '健康评估完成',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('健康评估创建失败', { error, userId: req.user?.id });
      next(error);
    }
  }
);

/**
 * GET /health/assessment/latest
 * Get user's latest health assessment
 */
router.get('/assessment/latest',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;

      // TODO: Implement actual database queries for latest assessment
      const latestAssessment = null;

      logger.info('获取最新健康评估', { userId, assessmentId: latestAssessment.id });

      res.json({
        success: true,
        data: latestAssessment,
        message: '获取最新健康评估成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取健康评估失败', { error, userId: req.user?.id });
      next(error);
    }
  }
);

/**
 * GET /health/assessment/history
 * Get user's health assessment history
 */
router.get('/assessment/history',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const limit = parseInt(req.query['limit'] as string) || 10;
      const offset = parseInt(req.query['offset'] as string) || 0;

      // Mock assessment history
      const assessments = Array.from({ length: Math.min(limit, 5) }, (_, index) => ({
        id: `assessment_${Date.now() - (index + 1) * 86400000}`,
        userId,
        height: 175,
        weight: 70 + index * 0.5,
        painLevel: Math.max(1, 5 - index),
        mobilityLevel: Math.min(10, 5 + index),
        bmi: Math.round(((70 + index * 0.5) / ((175 / 100) ** 2)) * 100) / 100,
        recoveryStage: index < 2 ? 'intermediate' : 'beginner',
        riskScore: Math.max(0.1, 0.5 - index * 0.1),
        assessmentDate: new Date(Date.now() - (index + 1) * 86400000).toISOString(),
        createdAt: new Date(Date.now() - (index + 1) * 86400000).toISOString(),
      }));

      logger.info('获取健康评估历史', { userId, count: assessments.length });

      res.json({
        success: true,
        data: {
          assessments,
          pagination: {
            limit,
            offset,
            total: 15, // Mock total
            hasMore: offset + limit < 15,
          },
        },
        message: '获取健康评估历史成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('获取健康评估历史失败', { error, userId: req.user?.id });
      next(error);
    }
  }
);

export default router;
