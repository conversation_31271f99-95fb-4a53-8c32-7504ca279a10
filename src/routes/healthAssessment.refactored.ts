/**
 * Health Assessment Routes (Refactored)
 * Provides comprehensive health assessment management endpoints
 */

import { Router } from 'express';
import { healthAssessmentController } from '@/controllers/HealthAssessmentController';
import { validateRequest } from '@/middleware/validation';
import {
  createHealthAssessmentSchema,
  updateHealthAssessmentSchema,
  completeAssessmentSchema,
  generateAssessmentReportSchema,
  getUserAssessmentsQuerySchema,
  getAssessmentStatsQuerySchema,
  getAssessmentTemplatesQuerySchema,
  uuidParamSchema,
  userIdParamSchema,
} from '@/utils/validation/healthAssessmentValidation';

const router = Router();

/**
 * POST /health/assessments
 * Create a new health assessment
 */
router.post(
  '/assessments',
  validateRequest(createHealthAssessmentSchema, 'body'),
  healthAssessmentController.createAssessment
);

/**
 * GET /health/assessments/templates
 * Get assessment templates (must be before /:id route)
 */
router.get(
  '/assessments/templates',
  validateRequest(getAssessmentTemplatesQuerySchema, 'query'),
  healthAssessmentController.getAssessmentTemplates
);

/**
 * GET /health/assessments/:id
 * Get assessment by ID
 */
router.get(
  '/assessments/:id',
  validateRequest(uuidParamSchema, 'params'),
  healthAssessmentController.getAssessmentById
);

/**
 * PUT /health/assessments/:id
 * Update assessment
 */
router.put(
  '/assessments/:id',
  validateRequest(uuidParamSchema, 'params'),
  validateRequest(updateHealthAssessmentSchema, 'body'),
  healthAssessmentController.updateAssessment
);

/**
 * DELETE /health/assessments/:id
 * Delete assessment (soft delete)
 */
router.delete(
  '/assessments/:id',
  validateRequest(uuidParamSchema, 'params'),
  healthAssessmentController.deleteAssessment
);

/**
 * POST /health/assessments/:id/start
 * Start an assessment
 */
router.post(
  '/assessments/:id/start',
  validateRequest(uuidParamSchema, 'params'),
  healthAssessmentController.startAssessment
);

/**
 * POST /health/assessments/:id/complete
 * Complete an assessment with results
 */
router.post(
  '/assessments/:id/complete',
  validateRequest(uuidParamSchema, 'params'),
  validateRequest(completeAssessmentSchema, 'body'),
  healthAssessmentController.completeAssessment
);

/**
 * GET /health/assessments/:id/results
 * Get assessment results
 */
router.get(
  '/assessments/:id/results',
  validateRequest(uuidParamSchema, 'params'),
  healthAssessmentController.getAssessmentResults
);

/**
 * POST /health/assessments/:id/report
 * Generate assessment report
 */
router.post(
  '/assessments/:id/report',
  validateRequest(uuidParamSchema, 'params'),
  validateRequest(generateAssessmentReportSchema, 'body'),
  healthAssessmentController.generateAssessmentReport
);

/**
 * GET /health/assessments/:id/reports
 * Get all reports for an assessment
 */
router.get(
  '/assessments/:id/reports',
  validateRequest(uuidParamSchema, 'params'),
  healthAssessmentController.getAssessmentReports
);

/**
 * GET /health/assessments/user/:userId
 * Get user assessments with pagination and filters
 */
router.get(
  '/assessments/user/:userId',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(getUserAssessmentsQuerySchema, 'query'),
  healthAssessmentController.getUserAssessments
);

/**
 * GET /health/assessments/user/:userId/stats
 * Get user assessment statistics
 */
router.get(
  '/assessments/user/:userId/stats',
  validateRequest(userIdParamSchema, 'params'),
  validateRequest(getAssessmentStatsQuerySchema, 'query'),
  healthAssessmentController.getUserAssessmentStats
);

export default router;
