import Joi from 'joi';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

interface EnvConfig {
  NODE_ENV: string;
  PORT: number;
  DATABASE_URL: string;
  REDIS_URL?: string;
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  REFRESH_TOKEN_EXPIRES_IN: string;
  ENCRYPTION_KEY: string;
  CORS_ORIGIN: string;
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  MAX_FILE_SIZE: number;
  UPLOAD_PATH: string;
  SMS_API_KEY?: string;
  SMS_API_URL?: string;
  SMTP_HOST?: string;
  SMTP_PORT?: number;
  SMTP_USER?: string;
  SMTP_PASS?: string;
  LOG_LEVEL: string;
  LOG_FILE: string;
  WEBSOCKET_PORT: number;
  WEBSOCKET_CORS_ORIGIN: string;
  HEALTH_CHECK_TIMEOUT: number;
  MINIO_ENDPOINT?: string;
  MINIO_ACCESS_KEY?: string;
  MINIO_SECRET_KEY?: string;
  MINIO_BUCKET?: string;
  PROMETHEUS_PORT?: number;
  METRICS_ENABLED: boolean;
}

const envSchema = Joi.object({
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),

  PORT: Joi.number().port().default(3000),

  DATABASE_URL: Joi.string().uri().required().description('PostgreSQL database connection string'),

  REDIS_URL: Joi.string().uri().optional().description('Redis connection string'),

  JWT_SECRET: Joi.string().min(32).required().description('JWT signing secret (minimum 32 characters)'),

  JWT_EXPIRES_IN: Joi.string().default('15m').description('JWT token expiration time'),

  REFRESH_TOKEN_EXPIRES_IN: Joi.string().default('7d').description('Refresh token expiration time'),

  ENCRYPTION_KEY: Joi.string().min(32).required().description('Encryption key for sensitive data'),

  CORS_ORIGIN: Joi.string().default('http://localhost:3000').description('Allowed CORS origins (comma-separated)'),

  RATE_LIMIT_WINDOW_MS: Joi.number()
    .positive()
    .default(900000) // 15 minutes
    .description('Rate limiting window in milliseconds'),

  RATE_LIMIT_MAX_REQUESTS: Joi.number().positive().default(100).description('Maximum requests per window'),

  MAX_FILE_SIZE: Joi.number()
    .positive()
    .default(10485760) // 10MB
    .description('Maximum file upload size in bytes'),

  UPLOAD_PATH: Joi.string().default('./uploads').description('File upload directory path'),

  SMS_API_KEY: Joi.string().optional().description('SMS service API key'),

  SMS_API_URL: Joi.string().uri().optional().description('SMS service API URL'),

  SMTP_HOST: Joi.string().optional().description('SMTP server host'),

  SMTP_PORT: Joi.number().port().optional().description('SMTP server port'),

  SMTP_USER: Joi.string().email().optional().description('SMTP username'),

  SMTP_PASS: Joi.string().optional().description('SMTP password'),

  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info').description('Logging level'),

  LOG_FILE: Joi.string().default('logs/app.log').description('Log file path'),

  WEBSOCKET_PORT: Joi.number().port().default(3001).description('WebSocket server port'),

  WEBSOCKET_CORS_ORIGIN: Joi.string().default('*').description('WebSocket CORS origin'),

  HEALTH_CHECK_TIMEOUT: Joi.number().positive().default(5000).description('Health check timeout in milliseconds'),

  MINIO_ENDPOINT: Joi.string().optional().description('MinIO endpoint'),

  MINIO_ACCESS_KEY: Joi.string().optional().description('MinIO access key'),

  MINIO_SECRET_KEY: Joi.string().optional().description('MinIO secret key'),

  MINIO_BUCKET: Joi.string().optional().description('MinIO bucket name'),

  PROMETHEUS_PORT: Joi.number().port().optional().description('Prometheus metrics port'),

  METRICS_ENABLED: Joi.boolean().default(true).description('Enable metrics collection'),
}).unknown();

export function validateEnv(): EnvConfig {
  const { error, value } = envSchema.validate(process.env);

  if (error) {
    throw new Error(`Environment validation error: ${error.message}`);
  }

  return value as EnvConfig;
}

export const env = validateEnv();
