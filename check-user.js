const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUser() {
  try {
    const user = await prisma.user.findUnique({
      where: { id: '550e8400-e29b-41d4-a716-446655440000' },
      select: {
        id: true,
        username: true,
        email: true,
        isActive: true,
      },
    });
    
    if (user) {
      console.log('User found:', user);
    } else {
      console.log('User not found');
    }
  } catch (error) {
    console.error('Error checking user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUser();
