{"name": "shoutao-backend", "version": "1.0.0", "description": "Shoutao App Backend - Smart Glove Rehabilitation Training System", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node -r tsconfig-paths/register dist/main.js", "start:old": "node -r tsconfig-paths/register dist/index.js", "dev": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/main.ts", "dev:old": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "ts-node scripts/seed.ts", "db:reset": "npx prisma migrate reset", "prepare": "husky install", "worker": "ts-node-dev src/workers/videoWorker.ts"}, "prisma": {"seed": "ts-node -r tsconfig-paths/register prisma/seed.ts"}, "keywords": ["rehabilitation", "smart-glove", "healthcare", "nodejs", "typescript", "express"], "author": "Shoutao Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.14.0", "bcryptjs": "^2.4.3", "bullmq": "^5.8.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "helmet": "^7.1.0", "ioredis": "^5.4.1", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "prisma": "^5.7.1", "redis": "^4.6.10", "reflect-metadata": "^0.2.2", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "^4.2.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.55.0", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"]}, "directories": {"doc": "docs", "test": "tests"}, "types": "./dist/main.d.ts"}