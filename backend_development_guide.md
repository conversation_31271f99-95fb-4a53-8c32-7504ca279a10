# Shoutao App 后端开发指南

## 目录
- [1. 项目概述](#1-项目概述)
- [2. 技术架构](#2-技术架构)
- [3. 开发环境搭建](#3-开发环境搭建)
- [4. 数据库设计](#4-数据库设计)
- [5. 微服务架构](#5-微服务架构)
- [6. API实现指南](#6-api实现指南)
- [7. 实时数据处理](#7-实时数据处理)
- [8. 部署方案](#8-部署方案)
- [9. 安全性设计](#9-安全性设计)
- [10. 监控与运维](#10-监控与运维)
- [11. 开发时间线](#11-开发时间线)

## 1. 项目概述

### 1.1 项目背景
Shoutao App 是一个智能手套康复训练应用的后端系统，支持：
- 用户认证与管理
- 智能手套设备数据处理
- 康复训练数据分析
- 果园采摘游戏系统
- 社区交流功能
- 实时数据监控

### 1.2 技术要求
- **响应时间**: API响应 < 200ms，实时数据处理 < 50ms
- **并发支持**: 支持1000+用户同时在线
- **数据安全**: 医疗级数据加密和隐私保护
- **可扩展性**: 微服务架构，支持水平扩展
- **可靠性**: 99.9%可用性，完善的容错机制

## 2. 技术架构

### 2.1 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │  智能手套设备    │    │   Web管理后台    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (Nginx+Kong)  │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   认证服务       │    │   用户服务       │    │   设备服务       │
│  (Auth Service) │    │ (User Service)  │    │(Device Service) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   游戏服务       │    │   社区服务       │    │  数据分析服务    │
│ (Game Service)  │    │(Community Svc)  │    │(Analytics Svc)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  实时数据网关    │
                    │(Realtime Gateway)│
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │   消息队列       │
│   (主数据库)     │    │   (缓存层)       │    │  (Bull Queue)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 技术栈选择

**后端框架**:
- **Node.js + TypeScript**: 主要API服务
- **Express.js**: Web框架
- **Socket.IO**: 实时通信
- **Python + FastAPI**: 数据分析服务

**数据存储**:
- **PostgreSQL 15**: 主数据库
- **Redis 7**: 缓存和会话存储
- **MinIO**: 文件存储 (S3兼容)

**基础设施**:
- **Docker**: 容器化
- **Nginx**: 反向代理和负载均衡
- **Kong**: API网关
- **Prometheus + Grafana**: 监控

## 3. 开发环境搭建

### 3.1 环境要求
```bash
# 基础环境
Node.js >= 18.0.0
Python >= 3.9
Docker >= 20.10
Docker Compose >= 2.0
PostgreSQL >= 15
Redis >= 7.0
```

### 3.2 项目初始化
```bash
# 创建项目目录
mkdir shoutao-backend
cd shoutao-backend

# 初始化项目结构
mkdir -p {services,shared,docker,scripts,docs}
mkdir -p services/{auth,user,device,game,community,analytics,realtime}
mkdir -p shared/{types,utils,middleware}

# 初始化主package.json
npm init -y
npm install -g @nestjs/cli typescript ts-node

# 创建各个微服务
cd services/auth && nest new . --skip-git
cd ../user && nest new . --skip-git
cd ../device && nest new . --skip-git
# ... 其他服务
```

### 3.3 Docker开发环境
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: shoutao_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_dev_data:/data

volumes:
  postgres_dev_data:
  redis_dev_data:
  minio_dev_data:
```

## 4. 数据库设计

### 4.1 核心表结构

#### 用户相关表
```sql
-- 用户基础信息表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone_number VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    age INTEGER,
    gender VARCHAR(10),
    recovery_phase VARCHAR(50),
    recovery_progress DECIMAL(4,3) DEFAULT 0.000,
    avatar_url VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户认证令牌表
CREATE TABLE user_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_type VARCHAR(20) NOT NULL, -- 'refresh', 'reset_password', 'email_verify'
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户设备关联表
CREATE TABLE user_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    device_mac VARCHAR(17) UNIQUE NOT NULL,
    device_name VARCHAR(100) DEFAULT 'Smart Glove',
    device_type VARCHAR(50) DEFAULT 'smart_glove',
    pairing_token VARCHAR(255),
    last_connected_at TIMESTAMP,
    battery_level SMALLINT CHECK (battery_level >= 0 AND battery_level <= 100),
    firmware_version VARCHAR(20),
    hardware_version VARCHAR(20),
    calibration_data JSONB,
    device_status VARCHAR(20) DEFAULT 'offline', -- 'online', 'offline', 'error'
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 训练和康复相关表
```sql
-- 康复计划表
CREATE TABLE rehabilitation_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_name VARCHAR(100) NOT NULL,
    recovery_stage VARCHAR(50) NOT NULL, -- 'early', 'intermediate', 'advanced'
    target_force_range JSONB NOT NULL, -- {"min": 0.5, "max": 2.0}
    training_duration_minutes INTEGER DEFAULT 30,
    sessions_per_week INTEGER DEFAULT 5,
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    difficulty_params JSONB,
    created_by UUID REFERENCES users(id), -- 医生或系统
    approved_by UUID REFERENCES users(id), -- 审批医生
    start_date DATE NOT NULL,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 训练会话表
CREATE TABLE training_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    device_id UUID REFERENCES user_devices(id),
    rehabilitation_plan_id UUID REFERENCES rehabilitation_plans(id),
    session_type VARCHAR(50) NOT NULL, -- 'rehabilitation', 'game', 'assessment', 'free_training'
    session_name VARCHAR(100),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    planned_duration_seconds INTEGER,
    actual_duration_seconds INTEGER,
    target_actions INTEGER,
    completed_actions INTEGER DEFAULT 0,
    target_accuracy DECIMAL(4,3),
    achieved_accuracy DECIMAL(4,3),
    average_grip_strength DECIMAL(6,3),
    max_grip_strength DECIMAL(6,3),
    min_grip_strength DECIMAL(6,3),
    total_score INTEGER DEFAULT 0,
    session_status VARCHAR(20) DEFAULT 'active', -- 'active', 'completed', 'paused', 'abandoned'
    end_reason VARCHAR(50), -- 'completed', 'timeout', 'user_stopped', 'device_error'
    session_data JSONB, -- 存储会话特定数据
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 训练数据点表（时序数据）
CREATE TABLE training_data_points (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID REFERENCES training_sessions(id) ON DELETE CASCADE,
    timestamp TIMESTAMP NOT NULL,
    sequence_number INTEGER NOT NULL, -- 数据点序号
    grip_strength DECIMAL(6,3), -- 抓握力度 (N)
    finger_positions JSONB, -- 手指位置数据
    hand_pose JSONB, -- 手部姿态数据
    accuracy DECIMAL(4,3), -- 动作准确度
    action_type VARCHAR(50), -- 动作类型
    gesture_type VARCHAR(50), -- 手势类型
    confidence DECIMAL(4,3), -- 识别置信度
    sensor_data JSONB, -- 原始传感器数据
    processed_data JSONB, -- 处理后的数据
    quality_score DECIMAL(4,3), -- 数据质量评分
    anomaly_flags JSONB, -- 异常标记
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为时序数据创建分区表（按月分区）
CREATE TABLE training_data_points_y2024m01 PARTITION OF training_data_points
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
-- ... 其他月份分区

-- 游戏记录表
CREATE TABLE game_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES training_sessions(id),
    game_type VARCHAR(50) NOT NULL, -- 'orchard_picking', 'grip_training'
    level INTEGER NOT NULL DEFAULT 1,
    score INTEGER NOT NULL DEFAULT 0,
    fruits_collected INTEGER DEFAULT 0,
    target_fruits INTEGER DEFAULT 0,
    average_grip_strength DECIMAL(6,3),
    max_grip_strength DECIMAL(6,3),
    min_grip_strength DECIMAL(6,3),
    accuracy DECIMAL(4,3),
    play_time_seconds INTEGER,
    combo_count INTEGER DEFAULT 0,
    perfect_picks INTEGER DEFAULT 0,
    missed_fruits INTEGER DEFAULT 0,
    penalty_score INTEGER DEFAULT 0,
    end_reason VARCHAR(50), -- 'completed', 'timeout', 'abandoned'
    level_config JSONB, -- 关卡配置快照
    metadata JSONB, -- 额外游戏数据
    played_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 游戏动作记录表
CREATE TABLE game_actions (
    id BIGSERIAL PRIMARY KEY,
    game_record_id UUID REFERENCES game_records(id) ON DELETE CASCADE,
    timestamp TIMESTAMP NOT NULL,
    sequence_number INTEGER NOT NULL,
    action_type VARCHAR(50) NOT NULL, -- 'fruit_picked', 'missed', 'perfect'
    grip_strength DECIMAL(6,3),
    accuracy DECIMAL(4,3),
    score INTEGER DEFAULT 0,
    fruit_type VARCHAR(50),
    position_x DECIMAL(6,3),
    position_y DECIMAL(6,3),
    gesture_data JSONB,
    sensor_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 社区帖子表
CREATE TABLE posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    author_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200),
    content TEXT NOT NULL,
    post_type VARCHAR(50) DEFAULT 'forum', -- 'forum', 'friend_circle', 'expert_column'
    category VARCHAR(50),
    tags JSONB, -- 标签数组
    images JSONB, -- 图片URL数组
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    views_count INTEGER DEFAULT 0,
    is_pinned BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    status VARCHAR(20) DEFAULT 'published', -- 'draft', 'published', 'hidden', 'deleted'
    moderation_status VARCHAR(20) DEFAULT 'approved', -- 'pending', 'approved', 'rejected'
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 评论表
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    author_id UUID REFERENCES users(id) ON DELETE CASCADE,
    parent_comment_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    likes_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'published',
    moderation_status VARCHAR(20) DEFAULT 'approved',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 点赞表
CREATE TABLE likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    target_id UUID NOT NULL, -- 可以是post_id或comment_id
    target_type VARCHAR(20) NOT NULL, -- 'post' 或 'comment'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, target_id, target_type)
);

-- 收藏表
CREATE TABLE favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, post_id)
);
```

### 4.2 索引优化策略
```sql
-- 用户相关索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone_number);
CREATE INDEX idx_users_active ON users(is_active) WHERE is_active = true;

-- 设备相关索引
CREATE INDEX idx_user_devices_user_id ON user_devices(user_id);
CREATE INDEX idx_user_devices_mac ON user_devices(device_mac);
CREATE INDEX idx_user_devices_status ON user_devices(device_status);

-- 训练会话索引
CREATE INDEX idx_training_sessions_user_date ON training_sessions(user_id, DATE(start_time));
CREATE INDEX idx_training_sessions_status ON training_sessions(session_status);
CREATE INDEX idx_training_sessions_type ON training_sessions(session_type);

-- 时序数据索引
CREATE INDEX idx_training_data_points_session_time ON training_data_points(session_id, timestamp);
CREATE INDEX idx_training_data_points_timestamp ON training_data_points(timestamp);
CREATE INDEX idx_training_data_points_grip_strength ON training_data_points(grip_strength) 
    WHERE grip_strength IS NOT NULL;
```

## 5. 微服务架构

### 5.1 认证服务 (Auth Service)

#### 目录结构
```
services/auth/
├── src/
│   ├── controllers/
│   │   ├── auth.controller.ts
│   │   └── token.controller.ts
│   ├── services/
│   │   ├── auth.service.ts
│   │   ├── jwt.service.ts
│   │   └── sms.service.ts
│   ├── guards/
│   │   ├── jwt-auth.guard.ts
│   │   └── roles.guard.ts
│   ├── strategies/
│   │   ├── jwt.strategy.ts
│   │   └── local.strategy.ts
│   ├── dto/
│   │   ├── login.dto.ts
│   │   ├── register.dto.ts
│   │   └── phone-login.dto.ts
│   └── entities/
│       ├── user.entity.ts
│       └── user-token.entity.ts
├── test/
└── package.json
```

#### 核心实现
```typescript
// auth.controller.ts
import { Controller, Post, Body, UseGuards, Req } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto, PhoneLoginDto, RegisterDto } from './dto';

@Controller('api/v1/auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    const result = await this.authService.validateUser(
      loginDto.username,
      loginDto.password
    );
    
    if (!result.user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    
    return this.authService.login(result.user);
  }

  @Post('login-phone')
  async loginWithPhone(@Body() phoneLoginDto: PhoneLoginDto) {
    // 验证短信验证码
    const isValidCode = await this.authService.verifySmsCode(
      phoneLoginDto.phoneNumber,
      phoneLoginDto.smsCode
    );
    
    if (!isValidCode) {
      throw new BadRequestException('Invalid SMS code');
    }
    
    return this.authService.loginWithPhone(phoneLoginDto.phoneNumber);
  }

  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('refresh')
  async refreshToken(@Body('refreshToken') refreshToken: string) {
    return this.authService.refreshToken(refreshToken);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  async logout(@Req() req) {
    return this.authService.logout(req.user.id);
  }
}
```

### 5.2 设备服务 (Device Service)

#### 实时数据处理
```typescript
// device-data.gateway.ts
import { 
  WebSocketGateway, 
  WebSocketServer, 
  SubscribeMessage, 
  MessageBody,
  ConnectedSocket 
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({
  namespace: '/device-stream',
  cors: { origin: '*' }
})
export class DeviceDataGateway {
  @WebSocketServer()
  server: Server;

  constructor(
    private readonly deviceService: DeviceService,
    private readonly sensorProcessor: SensorDataProcessor,
    private readonly cacheService: CacheService
  ) {}

  @SubscribeMessage('sensor-data')
  async handleSensorData(
    @MessageBody() data: SensorDataPacket,
    @ConnectedSocket() client: Socket
  ) {
    try {
      // 验证设备
      const device = await this.deviceService.validateDevice(data.deviceId);
      
      // 处理传感器数据
      const processedData = await this.sensorProcessor.process(data);
      
      // 缓存最新数据
      await this.cacheService.cacheLatestSensorData(
        device.userId,
        processedData
      );
      
      // 检查阈值警告
      const alerts = await this.sensorProcessor.checkThresholds(
        device.userId,
        processedData
      );
      
      if (alerts.length > 0) {
        client.emit('sensor-alerts', alerts);
      }
      
      // 发送处理结果
      client.emit('data-processed', {
        timestamp: processedData.timestamp,
        quality: processedData.quality,
        status: 'success'
      });
      
    } catch (error) {
      client.emit('error', { 
        message: 'Data processing failed',
        error: error.message 
      });
    }
  }

  @SubscribeMessage('device-status')
  async handleDeviceStatus(
    @MessageBody() data: DeviceStatusUpdate,
    @ConnectedSocket() client: Socket
  ) {
    await this.deviceService.updateDeviceStatus(data.deviceId, data.status);
    
    // 广播设备状态更新
    this.server.emit('device-status-updated', {
      deviceId: data.deviceId,
      status: data.status,
      timestamp: new Date()
    });
  }
}
```

## 6. API实现指南

### 6.1 RESTful API设计原则

#### 统一响应格式
```typescript
// common/dto/api-response.dto.ts
export class ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  timestamp: string;
}

// common/interceptors/response.interceptor.ts
@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
    return next.handle().pipe(
      map(data => ({
        success: true,
        data,
        timestamp: new Date().toISOString()
      })),
      catchError(error => {
        return of({
          success: false,
          error: {
            code: error.code || 'INTERNAL_ERROR',
            message: error.message || 'Internal server error'
          },
          timestamp: new Date().toISOString()
        });
      })
    );
  }
}
```

### 6.2 错误处理机制
```typescript
// common/filters/http-exception.filter.ts
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let code = 'INTERNAL_ERROR';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'object') {
        message = (exceptionResponse as any).message || message;
        code = (exceptionResponse as any).code || code;
      } else {
        message = exceptionResponse as string;
      }
    }

    // 记录错误日志
    this.logger.error(
      `${request.method} ${request.url}`,
      exception instanceof Error ? exception.stack : exception
    );

    const errorResponse: ApiResponse<null> = {
      success: false,
      error: {
        code,
        message,
        details: process.env.NODE_ENV === 'development' ? exception : undefined
      },
      timestamp: new Date().toISOString()
    };

    response.status(status).json(errorResponse);
  }
}
```

## 7. 实时数据处理

### 7.1 数据流架构
```typescript
// realtime/sensor-data.processor.ts
@Injectable()
export class SensorDataProcessor {
  constructor(
    private readonly redis: Redis,
    private readonly messageQueue: Queue
  ) {}

  async process(rawData: SensorDataPacket): Promise<ProcessedSensorData> {
    // 数据验证
    this.validateSensorData(rawData);
    
    // 数据校准
    const calibratedData = await this.calibrateData(rawData);
    
    // 特征提取
    const features = this.extractFeatures(calibratedData);
    
    // 手势识别
    const gesture = await this.recognizeGesture(features);
    
    // 质量评估
    const quality = this.assessDataQuality(calibratedData);
    
    const processedData: ProcessedSensorData = {
      timestamp: new Date(),
      deviceId: rawData.deviceId,
      gripStrength: this.calculateGripStrength(calibratedData.pressure),
      fingerPositions: this.calculateFingerPositions(calibratedData.flex),
      handPose: this.calculateHandPose(calibratedData.imu),
      gestureType: gesture.type,
      confidence: gesture.confidence,
      quality: quality,
      rawData: rawData
    };
    
    // 异步存储到数据库
    await this.messageQueue.add('store-sensor-data', processedData);
    
    return processedData;
  }

  private calculateGripStrength(pressureData: number[]): number {
    // 基于压力传感器数据计算抓握力度
    const totalPressure = pressureData.reduce((sum, val) => sum + val, 0);
    const calibrationFactor = 0.1; // 从设备校准数据获取
    return totalPressure * calibrationFactor;
  }

  private async recognizeGesture(features: FeatureVector): Promise<GestureResult> {
    // 调用机器学习模型进行手势识别
    // 可以使用TensorFlow.js或调用Python服务
    return {
      type: 'grip',
      confidence: 0.95
    };
  }
}
```

### 7.2 缓存策略
```typescript
// cache/cache.service.ts
@Injectable()
export class CacheService {
  constructor(private readonly redis: Redis) {}

  // 缓存用户实时状态
  async cacheUserSession(userId: string, sessionData: any): Promise<void> {
    const key = `user:${userId}:session`;
    await this.redis.setex(key, 3600, JSON.stringify(sessionData));
  }

  // 缓存设备状态
  async cacheDeviceStatus(deviceId: string, status: DeviceStatus): Promise<void> {
    const key = `device:${deviceId}:status`;
    await this.redis.hset(key, {
      status: JSON.stringify(status),
      lastUpdate: Date.now().toString()
    });
    await this.redis.expire(key, 7200); // 2小时过期
  }

  // 缓存传感器数据
  async cacheLatestSensorData(userId: string, data: ProcessedSensorData): Promise<void> {
    const key = `sensor:${userId}:latest`;
    await this.redis.setex(key, 300, JSON.stringify(data)); // 5分钟过期
    
    // 同时维护一个时间窗口的数据列表
    const timeWindowKey = `sensor:${userId}:window`;
    await this.redis.lpush(timeWindowKey, JSON.stringify(data));
    await this.redis.ltrim(timeWindowKey, 0, 99); // 保留最近100个数据点
    await this.redis.expire(timeWindowKey, 600); // 10分钟过期
  }

  // 获取用户实时训练统计
  async getRealtimeTrainingStats(userId: string): Promise<RealtimeStats> {
    const key = `training:${userId}:realtime`;
    const cached = await this.redis.get(key);
    
    if (cached) {
      return JSON.parse(cached);
    }
    
    // 如果缓存不存在，从数据库计算并缓存
    const stats = await this.calculateRealtimeStats(userId);
    await this.redis.setex(key, 60, JSON.stringify(stats)); // 1分钟缓存
    
    return stats;
  }
}
```

## 8. 部署方案

### 8.1 生产环境Docker配置
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  # API网关
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - auth-service
      - user-service
      - device-service
    restart: unless-stopped

  # 认证服务
  auth-service:
    build: 
      context: ./services/auth
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${AUTH_DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=15m
      - REFRESH_TOKEN_EXPIRES_IN=7d
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 用户服务
  user-service:
    build:
      context: ./services/user
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${USER_DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      replicas: 2

  # 设备服务
  device-service:
    build:
      context: ./services/device
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DEVICE_DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      replicas: 3 # 设备服务需要更多实例

  # 实时数据网关
  realtime-gateway:
    build:
      context: ./services/realtime
      dockerfile: Dockerfile.prod
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - REDIS_URL=${REDIS_URL}
      - SOCKET_IO_CORS_ORIGIN=${CORS_ORIGIN}
    depends_on:
      - redis
    restart: unless-stopped
    deploy:
      replicas: 2

  # 数据库
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./scripts/init-prod.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Redis集群
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_prod_data:/data
    restart: unless-stopped

  # 监控
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
    restart: unless-stopped

volumes:
  postgres_prod_data:
  redis_prod_data:
  prometheus_data:
  grafana_data:
```

### 8.2 Kubernetes部署配置
```yaml
# k8s/auth-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  labels:
    app: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: shoutao/auth-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: auth-database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
spec:
  selector:
    app: auth-service
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: ClusterIP
```

## 9. 安全性设计

### 9.1 认证和授权
```typescript
// security/jwt.strategy.ts
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly userService: UserService,
    private readonly cacheService: CacheService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET,
      passReqToCallback: true
    });
  }

  async validate(req: Request, payload: JwtPayload): Promise<User> {
    // 检查token是否在黑名单中
    const isBlacklisted = await this.cacheService.isTokenBlacklisted(
      req.headers.authorization?.replace('Bearer ', '')
    );
    
    if (isBlacklisted) {
      throw new UnauthorizedException('Token has been revoked');
    }

    // 验证用户是否仍然有效
    const user = await this.userService.findById(payload.sub);
    if (!user || !user.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }

    return user;
  }
}

// security/roles.guard.ts
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}
```

### 9.2 数据加密
```typescript
// security/encryption.service.ts
@Injectable()
export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32;
  private readonly ivLength = 16;
  private readonly tagLength = 16;

  constructor() {
    if (!process.env.ENCRYPTION_KEY) {
      throw new Error('ENCRYPTION_KEY environment variable is required');
    }
  }

  private getKey(): Buffer {
    return crypto.scryptSync(process.env.ENCRYPTION_KEY, 'salt', this.keyLength);
  }

  encrypt(text: string): EncryptedData {
    const iv = crypto.randomBytes(this.ivLength);
    const cipher = crypto.createCipher(this.algorithm, this.getKey());
    cipher.setAAD(Buffer.from('shoutao-medical-data', 'utf8'));

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const tag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }

  decrypt(encryptedData: EncryptedData): string {
    const decipher = crypto.createDecipher(this.algorithm, this.getKey());
    decipher.setAAD(Buffer.from('shoutao-medical-data', 'utf8'));
    decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  // 加密敏感的医疗数据
  encryptMedicalData(data: any): string {
    const jsonString = JSON.stringify(data);
    const encrypted = this.encrypt(jsonString);
    return Buffer.from(JSON.stringify(encrypted)).toString('base64');
  }

  decryptMedicalData(encryptedBase64: string): any {
    const encryptedData = JSON.parse(Buffer.from(encryptedBase64, 'base64').toString());
    const decrypted = this.decrypt(encryptedData);
    return JSON.parse(decrypted);
  }
}
```

## 10. 监控与运维

### 10.1 健康检查
```typescript
// health/health.controller.ts
@Controller('health')
export class HealthController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    private readonly databaseHealthIndicator: TypeOrmHealthIndicator,
    private readonly redisHealthIndicator: RedisHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.healthCheckService.check([
      () => this.databaseHealthIndicator.pingCheck('database'),
      () => this.redisHealthIndicator.pingCheck('redis'),
      () => this.checkExternalServices(),
    ]);
  }

  @Get('ready')
  @HealthCheck()
  readiness() {
    return this.healthCheckService.check([
      () => this.databaseHealthIndicator.pingCheck('database'),
      () => this.redisHealthIndicator.pingCheck('redis'),
    ]);
  }

  private async checkExternalServices(): Promise<HealthIndicatorResult> {
    // 检查外部服务连接状态
    try {
      // 检查短信服务
      await this.smsService.healthCheck();
      
      // 检查文件存储服务
      await this.fileStorageService.healthCheck();
      
      return {
        external_services: {
          status: 'up',
          message: 'All external services are healthy'
        }
      };
    } catch (error) {
      return {
        external_services: {
          status: 'down',
          message: error.message
        }
      };
    }
  }
}
```

### 10.2 性能监控
```typescript
// monitoring/metrics.service.ts
@Injectable()
export class MetricsService {
  private readonly httpRequestDuration: Histogram<string>;
  private readonly httpRequestTotal: Counter<string>;
  private readonly activeConnections: Gauge<string>;
  private readonly databaseQueryDuration: Histogram<string>;

  constructor() {
    // HTTP请求持续时间
    this.httpRequestDuration = new client.Histogram({
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
    });

    // HTTP请求总数
    this.httpRequestTotal = new client.Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code']
    });

    // 活跃连接数
    this.activeConnections = new client.Gauge({
      name: 'websocket_connections_active',
      help: 'Number of active WebSocket connections'
    });

    // 数据库查询持续时间
    this.databaseQueryDuration = new client.Histogram({
      name: 'database_query_duration_seconds',
      help: 'Duration of database queries in seconds',
      labelNames: ['operation', 'table'],
      buckets: [0.01, 0.05, 0.1, 0.3, 0.5, 1, 3, 5]
    });
  }

  recordHttpRequest(method: string, route: string, statusCode: number, duration: number): void {
    this.httpRequestDuration
      .labels(method, route, statusCode.toString())
      .observe(duration);
    
    this.httpRequestTotal
      .labels(method, route, statusCode.toString())
      .inc();
  }

  setActiveConnections(count: number): void {
    this.activeConnections.set(count);
  }

  recordDatabaseQuery(operation: string, table: string, duration: number): void {
    this.databaseQueryDuration
      .labels(operation, table)
      .observe(duration);
  }
}
```

## 11. 开发时间线

### 第一阶段：基础架构 (4周)

#### Week 1-2: 项目初始化
- [x] 项目结构搭建
- [x] Docker开发环境配置
- [x] 数据库设计和初始化
- [x] 基础微服务框架搭建
- [x] CI/CD流水线配置

#### Week 3-4: 认证系统
- [ ] JWT认证服务实现
- [ ] 用户注册/登录API
- [ ] 手机验证码登录
- [ ] 权限控制系统
- [ ] 安全中间件

**里程碑1**: 前端可以调用认证API，替换Mock数据

### 第二阶段：设备数据处理 (3周)

#### Week 5-6: 实时数据网关
- [ ] WebSocket服务器搭建
- [ ] 设备连接管理
- [ ] 实时数据接收和验证
- [ ] Redis缓存集成
- [ ] 数据质量检查

#### Week 7: 传感器数据处理
- [ ] 数据校准服务
- [ ] 手势识别算法
- [ ] 异常检测机制
- [ ] 设备状态监控

**里程碑2**: 智能手套实时数据传输和处理

### 第三阶段：游戏和训练功能 (3周)

#### Week 8-9: 训练数据管理
- [ ] 训练会话API
- [ ] 数据存储优化
- [ ] 统计分析API
- [ ] 康复计划管理

#### Week 10: 游戏系统
- [ ] 游戏记录API
- [ ] 实时游戏事件处理
- [ ] 关卡配置系统
- [ ] 排行榜功能

**里程碑3**: 完整的训练和游戏数据管理

### 第四阶段：社区功能 (3周)

#### Week 11-12: 社区基础功能
- [ ] 帖子管理API
- [ ] 评论系统
- [ ] 用户互动功能
- [ ] 内容审核

#### Week 13: 高级功能
- [ ] 专家专栏
- [ ] 推荐算法
- [ ] 消息通知
- [ ] 搜索功能

**里程碑4**: 完整的社区交流平台

### 第五阶段：优化和上线 (3周)

#### Week 14-15: 数据分析
- [ ] 康复进度分析
- [ ] 机器学习模型
- [ ] 预测算法
- [ ] 报表生成

#### Week 16: 生产部署
- [ ] 性能优化
- [ ] 安全加固
- [ ] 监控系统
- [ ] 生产环境部署

**最终里程碑**: 系统正式上线运行

---

## 附录

### A. 环境变量配置
```bash
# .env.production
NODE_ENV=production
PORT=3000

# 数据库配置
DATABASE_URL=************************************/shoutao_prod
REDIS_URL=redis://redis:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# 加密配置
ENCRYPTION_KEY=your-encryption-key

# 外部服务
SMS_API_KEY=your-sms-api-key
FILE_STORAGE_ENDPOINT=https://minio.example.com
FILE_STORAGE_ACCESS_KEY=minioaccess
FILE_STORAGE_SECRET_KEY=miniosecret

# 监控配置
PROMETHEUS_ENDPOINT=http://prometheus:9090
GRAFANA_URL=http://grafana:3000
```

### B. 开发工具推荐
- **IDE**: Visual Studio Code + TypeScript插件
- **API测试**: Postman + Newman
- **数据库管理**: pgAdmin 4
- **Redis管理**: RedisInsight
- **监控**: Grafana + Prometheus
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)

### C. 代码质量标准
- **测试覆盖率**: ≥ 80%
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript strict mode
- **文档**: JSDoc注释
- **版本控制**: Git Flow工作流

---

*本文档将随着项目进展持续更新，请定期查看最新版本。*
