const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testTrainingPlanQuery() {
  try {
    console.log('Testing TrainingPlanAdmin query with include...');
    
    // Test the exact query that was failing
    const plans = await prisma.trainingPlanAdmin.findMany({
      skip: 0,
      take: 10,
      orderBy: { sortOrder: 'asc' },
      include: {
        video: true, // This was causing the error
        _count: {
          select: { actionPoints: true },
        },
      },
    });
    
    console.log('✅ Query successful!');
    console.log(`Found ${plans.length} training plans`);
    
    if (plans.length > 0) {
      console.log('First plan:', {
        id: plans[0].id,
        name: plans[0].name,
        video: plans[0].video,
        actionPointsCount: plans[0]._count.actionPoints
      });
    }
    
  } catch (error) {
    console.error('❌ Query failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testTrainingPlanQuery();
