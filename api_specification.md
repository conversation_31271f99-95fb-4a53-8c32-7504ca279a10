# Shoutao App API 规范文档

## 目录
- [1. API概述](#1-api概述)
- [2. 认证API](#2-认证api)
- [3. 用户管理API](#3-用户管理api)
- [4. 设备管理API](#4-设备管理api)
- [5. 训练数据API](#5-训练数据api)
- [6. 游戏API](#6-游戏api)
- [7. 社区API](#7-社区api)
- [8. 数据分析API](#8-数据分析api)
- [9. 实时WebSocket API](#9-实时websocket-api)
- [10. 错误处理](#10-错误处理)
- [11. 数据模型](#11-数据模型)

## 1. API概述

### 1.1 基础信息
- **Base URL**: `https://api.shoutao.com/api/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1

### 1.2 通用响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-20T10:30:00Z",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

### 1.3 认证方式
- **Bearer Token**: `Authorization: Bearer <access_token>`
- **Token类型**: JWT
- **Token有效期**: 15分钟
- **刷新Token有效期**: 7天

### 1.4 请求头要求
```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer <access_token>
X-Client-Version: 1.0.0
X-Platform: android|ios
```

## 2. 认证API

### 2.1 用户名密码登录

**基于前端代码**: `ApiService.login()`

```http
POST /auth/login
```

**请求体**:
```json
{
  "username": "zhangming",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "zhangming",
      "email": "<EMAIL>",
      "phoneNumber": "13800138001",
      "fullName": "张明",
      "age": 45,
      "recoveryPhase": "恢复期",
      "recoveryProgress": 0.75,
      "avatarUrl": "https://cdn.shoutao.com/avatars/user1.jpg",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-20T10:30:00Z"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 900
  },
  "message": "登录成功",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 2.2 手机验证码登录

**基于前端代码**: `ApiService.loginWithPhone()`

```http
POST /auth/login-phone
```

**请求体**:
```json
{
  "phoneNumber": "13800138001",
  "smsCode": "123456"
}
```

**响应**: 同用户名密码登录

### 2.3 发送短信验证码

```http
POST /auth/send-sms
```

**请求体**:
```json
{
  "phoneNumber": "+8613800138001",
  "type": "login" | "register" | "reset_password"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "sent": true,
    "expiresIn": 300,
    "retryAfter": 60
  },
  "message": "SMS verification code sent successfully",
  "timestamp": "2024-01-20T10:30:00.000Z"
}
```

**错误响应**:
```json
{
  "success": false,
  "error": {
    "code": "PHONE_ALREADY_REGISTERED" | "PHONE_NOT_REGISTERED" | "TOO_MANY_REQUESTS",
    "message": "Error description"
  },
  "timestamp": "2024-01-20T10:30:00.000Z"
}
```

### 2.3.1 手机号注册

```http
POST /auth/register-phone
```

**请求体**:
```json
{
  "phoneNumber": "+8613800138001",
  "smsCode": "123456",
  "fullName": "张三",
  "age": 25,
  "gender": "male" | "female" | "other"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "user_13800138001",
      "phoneNumber": "+8613800138001",
      "fullName": "张三",
      "age": 25,
      "gender": "male"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600
  },
  "message": "Registration successful",
  "timestamp": "2024-01-20T10:30:00.000Z"
}
```

### 2.3.2 密码登录

```http
POST /auth/login-password
```

**请求体**:
```json
{
  "identifier": "+8613800138001",
  "password": "MyPassword123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "user_13800138001",
      "phoneNumber": "+8613800138001",
      "fullName": "张三"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600
  },
  "message": "Login successful",
  "timestamp": "2024-01-20T10:30:00.000Z"
}
```

### 2.3.3 密码重置

```http
POST /auth/reset-password
```

**请求体**:
```json
{
  "phoneNumber": "+8613800138001",
  "smsCode": "123456",
  "newPassword": "NewPassword123"
}
```

**响应**:
```json
{
  "success": true,
  "message": "Password reset successful",
  "timestamp": "2024-01-20T10:30:00.000Z"
}
```

### 2.4 获取当前用户信息

**基于前端代码**: `ApiService.getCurrentUser()`

```http
GET /auth/me
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "zhangming",
    "email": "<EMAIL>",
    "phoneNumber": "13800138001",
    "fullName": "张明",
    "age": 45,
    "recoveryPhase": "恢复期",
    "recoveryProgress": 0.75,
    "avatarUrl": "https://cdn.shoutao.com/avatars/user1.jpg",
    "healthMetrics": {
      "height": 175,
      "weight": 70,
      "bmi": 22.9
    },
    "trainingHistory": ["session1", "session2"],
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-20T10:30:00Z"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 2.5 刷新Token

```http
POST /auth/refresh
```

**请求体**:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 900
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 2.6 登出

```http
POST /auth/logout
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "message": "登出成功",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 3. 用户管理API

### 3.1 更新用户信息

```http
PUT /users/profile
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "fullName": "张明",
  "age": 46,
  "recoveryPhase": "康复期",
  "healthMetrics": {
    "height": 175,
    "weight": 68
  }
}
```

### 3.2 上传头像

```http
POST /users/avatar
Authorization: Bearer <access_token>
Content-Type: multipart/form-data
```

**请求体**: FormData with file

**响应**:
```json
{
  "success": true,
  "data": {
    "avatarUrl": "https://cdn.shoutao.com/avatars/user1_new.jpg"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 4. 设备管理API

### 4.1 获取设备状态

**基于前端代码**: `DeviceRepository.getDeviceDetails()`

```http
GET /devices/status
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "deviceName": "Smart Glove Pro",
    "isConnected": true,
    "uptimeHours": 2,
    "uptimeMinutes": 30,
    "battery": {
      "level": 85,
      "statusText": "良好",
      "estimatedHours": 4,
      "estimatedMinutes": 30
    },
    "sensors": [
      {
        "name": "压力传感器",
        "status": "normal",
        "lastUpdateMinutes": 1,
        "iconName": "sensors",
        "value": "2.3N"
      },
      {
        "name": "肌电传感器",
        "status": "normal",
        "lastUpdateMinutes": 1,
        "iconName": "timeline",
        "value": "0.8mV"
      },
      {
        "name": "IMU传感器",
        "status": "normal",
        "lastUpdateMinutes": 1,
        "iconName": "3d_rotation",
        "value": "稳定"
      }
    ],
    "system": {
      "firmwareVersion": "1.2.3",
      "hardwareVersion": "2.1",
      "lastSync": "2024-01-20T10:25:00Z"
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 4.2 设备配对

```http
POST /devices/pair
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "deviceMac": "AA:BB:CC:DD:EE:FF",
  "deviceName": "Smart Glove Pro",
  "pairingCode": "123456"
}
```

### 4.3 设备校准

```http
POST /devices/calibrate
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "deviceId": "device-uuid",
  "sensorType": "FSR",
  "calibrationData": {
    "minValue": 0,
    "maxValue": 1023,
    "forceRange": {
      "min": 0.1,
      "max": 10.0
    }
  }
}
```

## 5. 训练数据API

### 5.1 获取今日训练数据

**基于前端代码**: `ApiService.getTodayTrainingData()`

```http
GET /training/today
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "trainingDuration": 45,
    "completedActions": 12,
    "accuracy": 0.87,
    "score": 680,
    "averageGripStrength": 3.8,
    "maxGripStrength": 4.2,
    "trainingCount": 3,
    "sessions": [
      {
        "id": "session-uuid",
        "sessionType": "rehabilitation",
        "startTime": "2024-01-20T08:00:00Z",
        "duration": 1800,
        "score": 250
      }
    ]
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 5.2 获取周训练数据

**基于前端代码**: `ApiService.getWeeklyTrainingData()`

```http
GET /training/weekly
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "date": "2024-01-15",
      "trainingDuration": 30,
      "completedActions": 8,
      "accuracy": 0.82,
      "score": 420
    },
    {
      "date": "2024-01-16",
      "trainingDuration": 45,
      "completedActions": 12,
      "accuracy": 0.87,
      "score": 680
    }
  ],
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 5.3 获取训练目标

**基于前端代码**: `ApiService.getTodayGoal()`

```http
GET /training/goals
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "targetDuration": 60,
    "targetActions": 15,
    "targetAccuracy": 0.85,
    "currentDuration": 45,
    "currentActions": 12,
    "currentAccuracy": 0.87,
    "progress": {
      "duration": 0.75,
      "actions": 0.80,
      "accuracy": 1.02
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 5.4 开始训练会话

```http
POST /training/sessions/start
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "sessionType": "rehabilitation",
  "plannedDuration": 1800,
  "targetActions": 15,
  "deviceId": "device-uuid",
  "rehabilitationPlanId": "plan-uuid"
}
```

### 5.5 结束训练会话

```http
POST /training/sessions/{sessionId}/end
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "endReason": "completed",
  "actualDuration": 1750,
  "completedActions": 14,
  "notes": "训练完成良好"
}
```

### 5.6 批量上传训练数据

```http
POST /training/sessions/{sessionId}/data
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "dataPoints": [
    {
      "timestamp": "2024-01-20T10:30:00Z",
      "sequenceNumber": 1,
      "gripStrength": 3.2,
      "accuracy": 0.85,
      "actionType": "grip",
      "gestureType": "pinch",
      "confidence": 0.92,
      "sensorData": {
        "pressure": [512, 480, 520],
        "flex": [300, 280, 310],
        "imu": {
          "accel": [0.1, 0.2, 9.8],
          "gyro": [0.01, 0.02, 0.01]
        }
      }
    }
  ]
}
```

## 6. 游戏API

### 6.1 保存游戏记录

**基于前端代码**: `ApiService.saveGameRecord()`

```http
POST /games/records
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "gameType": "orchard_picking",
  "level": 3,
  "score": 1250,
  "fruitsCollected": 15,
  "targetFruits": 16,
  "averageGripStrength": 3.5,
  "maxGripStrength": 4.8,
  "minGripStrength": 2.1,
  "accuracy": 0.89,
  "playTimeSeconds": 180,
  "comboCount": 8,
  "perfectPicks": 5,
  "missedFruits": 1,
  "penaltyScore": 50,
  "endReason": "completed",
  "actions": [
    {
      "timestamp": "2024-01-20T10:30:00Z",
      "actionType": "fruit_picked",
      "gripStrength": 3.2,
      "accuracy": 0.95,
      "score": 100,
      "fruitType": "apple",
      "positionX": 150.5,
      "positionY": 200.3
    }
  ],
  "metadata": {
    "levelConfig": {
      "targetFruits": 16,
      "timeLimit": 200
    }
  }
}
```

### 6.2 获取游戏记录

**基于前端代码**: `ApiService.getGameRecords()`

```http
GET /games/records
Authorization: Bearer <access_token>
Query Parameters:
- gameType: string (optional)
- page: number (default: 1)
- limit: number (default: 20)
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "record-uuid",
      "gameType": "orchard_picking",
      "level": 3,
      "score": 1250,
      "fruitsCollected": 15,
      "accuracy": 0.89,
      "playTime": 180,
      "playedAt": "2024-01-20T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 6.3 获取游戏统计

**基于前端代码**: `ApiService.getGameStatistics()`

```http
GET /games/statistics
Authorization: Bearer <access_token>
Query Parameters:
- gameType: string (required)
- period: string (day|week|month, default: week)
```

**响应**:
```json
{
  "success": true,
  "data": {
    "totalGames": 25,
    "totalScore": 18750,
    "averageScore": 750,
    "bestScore": 1250,
    "totalPlayTime": 4500,
    "averageAccuracy": 0.85,
    "perfectGames": 3,
    "levelProgress": {
      "currentLevel": 3,
      "unlockedLevels": [1, 2, 3],
      "levelScores": {
        "1": 850,
        "2": 920,
        "3": 1250
      }
    },
    "trends": {
      "scoreImprovement": 0.15,
      "accuracyImprovement": 0.08
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 6.4 获取关卡配置

```http
GET /games/levels
Authorization: Bearer <access_token>
Query Parameters:
- gameType: string (required)
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "levelNumber": 1,
      "name": "新手果园",
      "description": "欢迎来到果园！让我们从简单的苹果和草莓开始吧",
      "targetFruits": 6,
      "timeSeconds": 90,
      "difficultyConfig": {
        "maxFruits": 2,
        "spawnIntervalSeconds": 8,
        "requiredAccuracy": 0.3,
        "scoreMultiplier": 1.0
      },
      "unlockRequirement": 0,
      "icon": "🌱",
      "difficulty": "新手"
    }
  ],
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 7. 社区API

### 7.1 获取帖子列表

**基于前端代码**: `CommunityRepository.getPosts()`

```http
GET /community/posts
Authorization: Bearer <access_token>
Query Parameters:
- postType: string (forum|friend_circle|expert_column, optional)
- category: string (optional)
- page: number (default: 1)
- limit: number (default: 20)
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "post-uuid",
      "author": {
        "id": "user-uuid",
        "name": "张明",
        "avatarUrl": "https://cdn.shoutao.com/avatars/user1.jpg",
        "role": "用户"
      },
      "title": "康复训练心得分享",
      "content": "经过一个月的训练，我的手部功能有了明显改善...",
      "postType": "forum",
      "category": "康复心得",
      "tags": ["康复", "训练", "经验"],
      "images": ["https://cdn.shoutao.com/posts/image1.jpg"],
      "likes": 25,
      "comments": 8,
      "views": 120,
      "isPinned": false,
      "isFeatured": false,
      "isFavorite": false,
      "timeAgo": "2小时前",
      "createdAt": "2024-01-20T08:30:00Z",
      "updatedAt": "2024-01-20T08:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 7.2 创建帖子

```http
POST /community/posts
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "title": "我的康复训练经验",
  "content": "分享一些康复训练的心得体会...",
  "postType": "forum",
  "category": "康复心得",
  "tags": ["康复", "训练"],
  "images": ["image1.jpg", "image2.jpg"]
}
```

### 7.3 获取帖子详情

```http
GET /community/posts/{postId}
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "post-uuid",
    "author": {
      "id": "user-uuid",
      "name": "张明",
      "avatarUrl": "https://cdn.shoutao.com/avatars/user1.jpg",
      "role": "用户"
    },
    "title": "康复训练心得分享",
    "content": "经过一个月的训练，我的手部功能有了明显改善...",
    "postType": "forum",
    "likes": 25,
    "comments": 8,
    "views": 121,
    "isFavorite": false,
    "commentList": [
      {
        "id": "comment-uuid",
        "author": {
          "id": "user2-uuid",
          "name": "李红",
          "avatarUrl": "https://cdn.shoutao.com/avatars/user2.jpg"
        },
        "content": "很有用的分享，谢谢！",
        "likes": 3,
        "timeAgo": "1小时前",
        "replies": [
          {
            "id": "reply-uuid",
            "author": {
              "id": "user-uuid",
              "name": "张明",
              "avatarUrl": "https://cdn.shoutao.com/avatars/user1.jpg"
            },
            "content": "不客气，希望对你有帮助",
            "likes": 1,
            "timeAgo": "30分钟前"
          }
        ]
      }
    ],
    "createdAt": "2024-01-20T08:30:00Z"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 7.4 点赞帖子

```http
POST /community/posts/{postId}/like
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "liked": true,
    "likesCount": 26
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 7.5 添加评论

```http
POST /community/posts/{postId}/comments
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "content": "很有用的分享，谢谢！",
  "parentCommentId": null
}
```

### 7.6 收藏帖子

```http
POST /community/posts/{postId}/favorite
Authorization: Bearer <access_token>
```

### 7.7 获取收藏列表

```http
GET /community/favorites
Authorization: Bearer <access_token>
Query Parameters:
- page: number (default: 1)
- limit: number (default: 20)
```

### 7.8 获取专家专栏

```http
GET /community/expert-articles
Authorization: Bearer <access_token>
Query Parameters:
- category: string (optional)
- page: number (default: 1)
- limit: number (default: 10)
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "article-uuid",
      "title": "手部康复的科学方法",
      "summary": "本文介绍了现代手部康复的科学方法和最新研究成果...",
      "author": {
        "id": "expert-uuid",
        "name": "王医生",
        "title": "康复科主任",
        "hospital": "北京康复医院",
        "avatarUrl": "https://cdn.shoutao.com/experts/doctor1.jpg"
      },
      "category": "康复指导",
      "readTime": 8,
      "views": 1250,
      "likes": 89,
      "publishedAt": "2024-01-18T10:00:00Z",
      "coverImage": "https://cdn.shoutao.com/articles/cover1.jpg"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 45,
    "totalPages": 5
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 8. 数据分析API

### 8.1 获取康复进度分析

```http
GET /analytics/recovery-progress
Authorization: Bearer <access_token>
Query Parameters:
- period: string (week|month|quarter, default: month)
```

**响应**:
```json
{
  "success": true,
  "data": {
    "currentProgress": {
      "gripStrength": 3.8,
      "accuracy": 0.87,
      "recoveryStage": "intermediate",
      "overallProgress": 0.75
    },
    "trends": {
      "gripStrengthTrend": 0.15,
      "accuracyTrend": 0.08,
      "consistencyScore": 0.92
    },
    "predictions": {
      "nextWeekGripStrength": [3.9, 4.0, 4.1, 4.2, 4.3, 4.4, 4.5],
      "nextWeekAccuracy": [0.88, 0.89, 0.90, 0.91, 0.92, 0.93, 0.94],
      "estimatedRecoveryTime": 45
    },
    "recommendations": [
      "建议增加握力训练强度",
      "保持当前训练频率",
      "注意休息，避免过度训练"
    ],
    "milestones": [
      {
        "name": "基础握力恢复",
        "achieved": true,
        "achievedAt": "2024-01-10T00:00:00Z"
      },
      {
        "name": "精细动作控制",
        "achieved": false,
        "estimatedDate": "2024-02-15T00:00:00Z"
      }
    ]
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 8.2 获取健康评估结果

**基于前端代码**: `ApiService.calculateHealthAssessment()`

```http
POST /analytics/health-assessment
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "height": 175,
  "weight": 70,
  "age": 45,
  "injuryType": "stroke",
  "injuryDate": "2023-06-15",
  "currentSymptoms": ["weakness", "stiffness"],
  "painLevel": 3,
  "mobilityLevel": 7,
  "dailyActivities": 6
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "assessment-uuid",
    "bmi": 22.9,
    "recoveryStage": "intermediate",
    "recoveryProgress": 0.65,
    "riskScore": 0.3,
    "recommendations": [
      "保持当前训练强度",
      "注意饮食营养均衡",
      "定期进行康复评估"
    ],
    "assessmentDate": "2024-01-20T10:30:00Z",
    "nextAssessmentDate": "2024-02-20T10:30:00Z",
    "detailedScores": {
      "motorFunction": 7.5,
      "sensoryFunction": 6.8,
      "cognitiveFunction": 8.2,
      "functionalIndependence": 7.1
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 8.3 获取用户记录

**基于前端代码**: `ApiService.getUserRecords()`

```http
GET /records
Authorization: Bearer <access_token>
Query Parameters:
- type: string (rehabilitation|game|assessment, optional)
- page: number (default: 1)
- limit: number (default: 20)
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "record-uuid",
      "title": "康复训练",
      "date": "2024-01-19T00:00:00Z",
      "time": "14:30",
      "type": "rehabilitation",
      "details": {
        "duration": 45,
        "score": 680,
        "accuracy": 0.87
      },
      "isFavorite": false
    },
    {
      "id": "record-uuid-2",
      "title": "抓握游戏",
      "date": "2024-01-18T00:00:00Z",
      "time": "16:15",
      "type": "game",
      "details": {
        "score": 1250,
        "level": 3,
        "fruits": 15
      },
      "isFavorite": true
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 9. 实时WebSocket API

### 9.1 连接端点

**设备数据流**:
```
wss://api.shoutao.com/device-stream
```

**游戏事件流**:
```
wss://api.shoutao.com/game-events
```

**通知流**:
```
wss://api.shoutao.com/notifications
```

### 9.2 连接认证

**连接时发送认证信息**:
```json
{
  "type": "auth",
  "token": "Bearer <access_token>",
  "clientInfo": {
    "platform": "android",
    "version": "1.0.0",
    "deviceId": "device-uuid"
  }
}
```

**认证成功响应**:
```json
{
  "type": "auth_success",
  "userId": "user-uuid",
  "sessionId": "session-uuid",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 9.3 设备数据流事件

#### 发送传感器数据
```json
{
  "type": "sensor-data",
  "deviceId": "device-uuid",
  "timestamp": "2024-01-20T10:30:00.123Z",
  "data": {
    "pressure": [512, 480, 520, 495, 510],
    "flex": [300, 280, 310, 290, 305],
    "imu": {
      "accel": [0.1, 0.2, 9.8],
      "gyro": [0.01, 0.02, 0.01],
      "mag": [0.3, 0.4, 0.5]
    },
    "emg": [0.8, 0.9, 0.7, 0.85],
    "battery": 85,
    "temperature": 36.5
  }
}
```

#### 接收处理结果
```json
{
  "type": "data-processed",
  "timestamp": "2024-01-20T10:30:00.125Z",
  "result": {
    "gripStrength": 3.2,
    "gestureType": "pinch",
    "confidence": 0.95,
    "quality": 0.92,
    "status": "success"
  }
}
```

#### 接收传感器警告
```json
{
  "type": "sensor-alerts",
  "alerts": [
    {
      "sensorType": "pressure",
      "alertType": "threshold_exceeded",
      "message": "握力超出安全范围",
      "severity": "warning",
      "value": 5.2,
      "threshold": 5.0,
      "timestamp": "2024-01-20T10:30:00Z"
    }
  ]
}
```

### 9.4 游戏事件流

#### 发送游戏事件
```json
{
  "type": "fruit-picked",
  "gameSessionId": "session-uuid",
  "timestamp": "2024-01-20T10:30:00Z",
  "data": {
    "fruitType": "apple",
    "position": {
      "x": 150.5,
      "y": 200.3
    },
    "gripStrength": 3.2,
    "accuracy": 0.95,
    "timeTaken": 1.2
  }
}
```

#### 接收游戏反馈
```json
{
  "type": "picking-result",
  "result": {
    "success": true,
    "score": 100,
    "bonus": 20,
    "message": "完美采摘！",
    "combo": 3,
    "totalScore": 1250
  }
}
```

#### 振动反馈指令
```json
{
  "type": "vibration-command",
  "intensity": 0.8,
  "duration": 200,
  "pattern": "success"
}
```

## 10. 错误处理

### 10.1 HTTP状态码

| 状态码 | 说明 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | 请求成功处理 |
| 201 | 创建成功 | 资源创建成功 |
| 400 | 请求错误 | 参数错误、格式错误 |
| 401 | 未授权 | Token无效或过期 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 409 | 冲突 | 资源冲突（如重复创建） |
| 422 | 参数验证失败 | 业务逻辑验证失败 |
| 429 | 请求过多 | 触发限流 |
| 500 | 服务器错误 | 内部服务器错误 |
| 503 | 服务不可用 | 服务维护或过载 |

### 10.2 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": {
      "field": "username",
      "reason": "用户名不能为空"
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 10.3 常见错误码

#### 认证相关错误
```json
{
  "code": "INVALID_CREDENTIALS",
  "message": "用户名或密码错误"
}

{
  "code": "TOKEN_EXPIRED",
  "message": "访问令牌已过期"
}

{
  "code": "INVALID_SMS_CODE",
  "message": "短信验证码无效或已过期"
}
```

#### 业务逻辑错误
```json
{
  "code": "DEVICE_NOT_FOUND",
  "message": "设备未找到或未配对"
}

{
  "code": "TRAINING_SESSION_ACTIVE",
  "message": "已有活跃的训练会话"
}

{
  "code": "INSUFFICIENT_PERMISSIONS",
  "message": "权限不足，无法执行此操作"
}
```

#### 系统错误
```json
{
  "code": "DATABASE_ERROR",
  "message": "数据库连接失败"
}

{
  "code": "EXTERNAL_SERVICE_ERROR",
  "message": "外部服务调用失败"
}
```

## 11. 数据模型

### 11.1 用户模型 (User)

**基于前端代码**: `User` 类

```typescript
interface User {
  id: string;
  username: string;
  email?: string;
  phoneNumber?: string;
  fullName: string;
  age: number;
  gender?: 'male' | 'female' | 'other';
  recoveryPhase: string;
  recoveryProgress: number; // 0.0 - 1.0
  avatarUrl?: string;
  healthMetrics?: {
    height?: number;
    weight?: number;
    bmi?: number;
  };
  trainingHistory: string[];
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}
```

### 11.2 设备模型 (Device)

**基于前端代码**: `DeviceDetails` 类

```typescript
interface DeviceDetails {
  deviceName: string;
  isConnected: boolean;
  uptimeHours: number;
  uptimeMinutes: number;
  battery: {
    level: number; // 0-100
    statusText: string;
    estimatedHours: number;
    estimatedMinutes: number;
  };
  sensors: Array<{
    name: string;
    status: 'normal' | 'warning' | 'error';
    lastUpdateMinutes: number;
    iconName: string;
    value: string;
  }>;
  system: {
    firmwareVersion: string;
    hardwareVersion: string;
    lastSync: string;
  };
}
```

### 11.3 训练数据模型 (Training)

**基于前端代码**: `TodayTrainingData` 类

```typescript
interface TodayTrainingData {
  trainingDuration: number; // 分钟
  completedActions: number;
  accuracy: number; // 0.0 - 1.0
  score: number;
  averageGripStrength: number;
  maxGripStrength: number;
  trainingCount: number;
  sessions: Array<{
    id: string;
    sessionType: string;
    startTime: string;
    duration: number; // 秒
    score: number;
  }>;
}

interface TrainingSession {
  id: string;
  userId: string;
  deviceId?: string;
  sessionType: 'rehabilitation' | 'game' | 'assessment' | 'free_training';
  sessionName?: string;
  startTime: string;
  endTime?: string;
  plannedDuration: number; // 秒
  actualDuration?: number; // 秒
  targetActions: number;
  completedActions: number;
  targetAccuracy: number;
  achievedAccuracy?: number;
  averageGripStrength?: number;
  maxGripStrength?: number;
  totalScore: number;
  status: 'active' | 'completed' | 'paused' | 'abandoned';
  endReason?: string;
  notes?: string;
}
```

### 11.4 游戏模型 (Game)

**基于前端代码**: `GameRecord` 类

```typescript
interface GameRecord {
  id: string;
  userId: string;
  sessionId?: string;
  gameType: 'orchard_picking' | 'grip_training';
  level: number;
  score: number;
  fruitsCollected: number;
  targetFruits: number;
  averageGripStrength: number;
  maxGripStrength: number;
  minGripStrength: number;
  accuracy: number;
  playTimeSeconds: number;
  comboCount: number;
  perfectPicks: number;
  missedFruits: number;
  penaltyScore: number;
  endReason: 'completed' | 'timeout' | 'abandoned';
  actions: GameAction[];
  metadata?: any;
  playedAt: string;
}

interface GameAction {
  timestamp: string;
  sequenceNumber: number;
  actionType: 'fruit_picked' | 'missed' | 'perfect';
  gripStrength: number;
  accuracy: number;
  score: number;
  fruitType?: string;
  positionX?: number;
  positionY?: number;
  gestureData?: any;
  sensorData?: any;
}
```

### 11.5 社区模型 (Community)

**基于前端代码**: `Post` 和 `Comment` 类

```typescript
interface Post {
  id: string;
  author: {
    id: string;
    name: string;
    avatarUrl?: string;
    role: string;
  };
  title?: string;
  content: string;
  postType: 'forum' | 'friend_circle' | 'expert_column';
  category?: string;
  tags: string[];
  images: string[];
  likes: number;
  comments: number;
  views: number;
  isPinned: boolean;
  isFeatured: boolean;
  isFavorite: boolean;
  timeAgo: string;
  createdAt: string;
  updatedAt: string;
}

interface Comment {
  id: string;
  postId: string;
  author: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
  content: string;
  likes: number;
  timeAgo: string;
  replies: Comment[];
  createdAt: string;
}
```

### 11.6 传感器数据模型 (Sensor)

```typescript
interface SensorDataPacket {
  deviceId: string;
  timestamp: string;
  data: {
    pressure: number[]; // 压力传感器数据
    flex: number[]; // 弯曲传感器数据
    imu: {
      accel: number[]; // 加速度计 [x, y, z]
      gyro: number[]; // 陀螺仪 [x, y, z]
      mag: number[]; // 磁力计 [x, y, z]
    };
    emg: number[]; // 肌电信号
    battery: number; // 电池电量
    temperature: number; // 温度
  };
}

interface ProcessedSensorData {
  timestamp: string;
  deviceId: string;
  gripStrength: number;
  fingerPositions: number[];
  handPose: {
    roll: number;
    pitch: number;
    yaw: number;
  };
  gestureType: string;
  confidence: number;
  quality: number;
  rawData: SensorDataPacket;
}
```

---

## 附录

### A. API测试用例

#### Postman集合示例
```json
{
  "info": {
    "name": "Shoutao App API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "Authentication",
      "item": [
        {
          "name": "Login with Username",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"username\": \"zhangming\",\n  \"password\": \"password123\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/v1/auth/login",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "auth", "login"]
            }
          }
        }
      ]
    }
  ],
  "variable": [
    {
      "key": "baseUrl",
      "value": "https://api.shoutao.com"
    }
  ]
}
```

### B. OpenAPI 3.0 规范

```yaml
openapi: 3.0.3
info:
  title: Shoutao App API
  description: 智能手套康复训练应用API
  version: 1.0.0
  contact:
    name: Shoutao Team
    email: <EMAIL>
servers:
  - url: https://api.shoutao.com/api/v1
    description: 生产环境
  - url: https://staging-api.shoutao.com/api/v1
    description: 测试环境
paths:
  /auth/login:
    post:
      summary: 用户名密码登录
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  example: zhangming
                password:
                  type: string
                  example: password123
              required:
                - username
                - password
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    AuthResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            user:
              $ref: '#/components/schemas/User'
            accessToken:
              type: string
            refreshToken:
              type: string
            expiresIn:
              type: integer
        timestamp:
          type: string
          format: date-time
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        username:
          type: string
        email:
          type: string
          format: email
        fullName:
          type: string
        age:
          type: integer
        recoveryPhase:
          type: string
        recoveryProgress:
          type: number
          minimum: 0
          maximum: 1
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: object
          properties:
            code:
              type: string
            message:
              type: string
            details:
              type: object
        timestamp:
          type: string
          format: date-time
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - BearerAuth: []
```

### C. 开发工具和资源

- **API文档生成**: Swagger UI / Redoc
- **API测试**: Postman / Insomnia
- **Mock服务**: JSON Server / WireMock
- **性能测试**: Apache JMeter / Artillery
- **监控工具**: Datadog / New Relic
- **日志分析**: ELK Stack / Splunk

---

*本API规范文档基于Shoutao App前端项目的实际需求制定，将随着项目开发进展持续更新和完善。*
