{"compilerOptions": {"target": "es2017", "module": "commonjs", "lib": ["es6"], "allowJs": true, "outDir": "dist", "strict": true, "esModuleInterop": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src", "tests", "prisma/seed.ts", "scripts"], "exclude": ["node_modules", "dist"]}